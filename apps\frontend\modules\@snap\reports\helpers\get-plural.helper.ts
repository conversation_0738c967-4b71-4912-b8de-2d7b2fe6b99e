import { PLURAL_WORDS } from "../config/constants";
import { Dictionary } from "../global";
import { normalizeString } from "./normalize-string.helper";

const getPluralWord: Dictionary = PLURAL_WORDS;

export const getPlural = (key: string): string => {
  if (!key) return "";
  const normalizedkey = normalizeString(key, { case: "lower" });
  const normalizedPlural = Object.entries(getPluralWord).reduce((acc, [key, value]) => {
  const normalizedKey = normalizeString(key, { case: "lower" });
  acc[normalizedKey] = value;
  return acc;
}, {} as Dictionary);

  return normalizedPlural[normalizedkey] || key;
};