from sqlalchemy import Column, BigInteger, Text, UUID, JSO<PERSON>

from models.base import Base
from sqlalchemy.sql import func
from sqlalchemy import DateTime

class Users(Base):
    __tablename__ = "users"
    __table_args__ = {"schema": "public"}  # Define the schema explicitly

    user_id = Column(
        UUID,
        primary_key=True,
        index=True
    )
    image = Column(Text, nullable=True)
    name = Column(Text, nullable=True)
    credits = Column(BigInteger, nullable=True, default=0)
    email = Column(Text, nullable=True)
    last_login = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    report_types = Column(JSON, nullable=True)
    salt = Column(Text, nullable=True)
    verifier = Column(JSON, nullable=True)

    def __repr__(self):
        return (
            f"<Users(user_id={self.user_id}, image={self.image}, name={self.name}, "
            f"credits={self.credits}, email={self.email}, last_login={self.last_login}, "
            f"report_types={self.report_types}, salt={self.salt}, verifier={self.verifier})>"
        )
