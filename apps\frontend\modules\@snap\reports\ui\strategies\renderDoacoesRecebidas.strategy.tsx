import React from "react";
import {
    DoacaoRecebida,
    isDoacaoRecebidaEmpresa,
    isDoacaoRecebidaPessoa
} from "../../model/DoacoesRecebidas";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderDoacoesRecebidas implements RenderStrategy<DoacaoRecebida> {

    validateKeys = (keys: Array<keyof DoacaoRecebida>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the doacoes information for a DoacaoRecebida
     * @param doacaoRecebida The DoacaoRecebida object
     * @returns React element or null if no data
     */
    renderDoacoes = (doacaoRecebida?: DoacaoRecebida): React.ReactElement | null => {
        return renderValidArray('vinculos', doacaoRecebida || {});
    };

    formatByKey: Record<
        string,
        (doacaoRecebida?: DoacaoRecebida) => React.ReactElement | null
    > = {
            razao_social: (doacaoRecebida?: DoacaoRecebida) => {
                if (!doacaoRecebida.razao_social) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(doacaoRecebida.razao_social.label || "Razão Social").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(doacaoRecebida.razao_social.value)}
                                tooltip={renderSourceTooltip(doacaoRecebida.razao_social.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            nome_completo: (doacaoRecebida?: DoacaoRecebida) => {
                if (!doacaoRecebida.nome_completo) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(doacaoRecebida.nome_completo.label || "Nome Completo").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(doacaoRecebida.nome_completo.value)}
                                tooltip={renderSourceTooltip(doacaoRecebida.nome_completo.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (doacaoRecebida?: DoacaoRecebida) => {
                if (!doacaoRecebida?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(doacaoRecebida.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={parseValue(String(value.value))}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            vinculos: (doacaoRecebida?: DoacaoRecebida) => {
                return this.renderDoacoes(doacaoRecebida);
            }
        };

    render = (doacaoRecebida: DoacaoRecebida): React.ReactElement[] => {
        const keys = Object.keys(doacaoRecebida) as Array<keyof DoacaoRecebida>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering based on the type of donation
        let orderedKeys: Array<keyof DoacaoRecebida> = [];

        if (isDoacaoRecebidaEmpresa(doacaoRecebida)) {
            orderedKeys = [
                'razao_social',
                'detalhes',
                'vinculos'
            ];
        } else if (isDoacaoRecebidaPessoa(doacaoRecebida)) {
            orderedKeys = [
                'nome_completo',
                'detalhes',
                'vinculos'
            ];
        }

        // Filter the keys to only include those that exist in the doacaoRecebida object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(doacaoRecebida);
                }

                // Check if the value is an array with the expected structure
                const value = doacaoRecebida[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, doacaoRecebida);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
