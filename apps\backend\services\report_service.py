import logging
import uuid
import json
import time
import random
import asyncio
from datetime import datetime, timezone
from io import BytesIO

import httpx
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Depends, WebSocketDisconnect, FastAPI
from fastapi.responses import JSONResponse
from sqlalchemy.inspection import inspect
from sqlalchemy.ext.asyncio import AsyncSession

from database.db import get_db
from core.config import settings, minio_client
from core.constants import (ReportInputs, Fields, ReportOutputs, ReportMessages, ReportStatus,
                            Timeouts, Endpoints, CustomErrorMessages, MergedReport, SummaryReportStatus, ReportMockValidator)
from models.report_model import UserReports
from services.auth_service import auth_guard
from utils.report_utils import (get_field_from_request, update_blank_report, 
                                get_user_report_types_and_credits, get_report, list_user_reports,
                                update_existing_report, save_merged_report, insert_verifier_on_db, update_error_report, insert_hmacs)
from schemas.report_schema import UpdateReportRequest, SnapApiRequest, SnapApiResponse, InsertReport, InsertVerifier
from utils.user_utils import get_user_data_dict
from utils.db_utils import retry_db_operation

logger = logging.getLogger(__name__)

async def insert_report_handler(body: InsertReport, user: dict, user_reports_id: str, db: AsyncSession):
    logger.info("[insert_report_handler][user(%s)] Inserting new report", user)
    user_id = user.get("sub")

    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        logger.error("[insert_report_handler] Invalid user_id UUID format")
        # return None

    await update_blank_report(
        db=db,
        user_uuid=user_uuid,
        user_reports_id=user_reports_id,
        report_name=body.report_name,
        report_type=body.report_type,
        report_status=body.report_status,
        report_search_args=body.report_search_args,
        subject_name=body.subject_name,
        subject_mother_name=body.subject_mother_name,
        subject_age=body.subject_age,
        subject_sex=body.subject_sex,
        creation_at=body.creation_at,
        modified_at=body.modified_at,
        omitted_notes=body.omitted_notes,
        data=body.data
    )
    if body.hmac: #TODO REMOVE THIS CONDITIONAL WHEN WE START TO USE
        await insert_hmacs(db=db, user_uuid= user_uuid, user_reports_id=user_reports_id, hmac=body.hmac)




async def validate_access_to_report(db, user_id, report_type):
    logger.info("[validate_access_to_report] Validating access for user")
    user_report_types = ['cpf', 'cnpj']
    user_credits = 1000

    if report_type not in user_report_types:
        logger.warning("[validate_access_to_report] User does not have access to report type")
        raise HTTPException(**CustomErrorMessages.no_access.to_dict(report_type))

    if user_credits <= 0:
        logger.warning("[validate_access_to_report] User has no credits")
        raise HTTPException(**CustomErrorMessages.no_credits.to_dict(report_type))
    



async def get_saved_reports_handler(db: AsyncSession, user: dict, limit: int,  page: int,
                                    order: str, column_order: str, hmac_filter: str, hmac_column: str):
    logger.info("[get_saved_reports_handler] Fetching saved reports")

    try:
        user_id = user.get("sub")

        return await retry_db_operation(
            lambda: list_user_reports(
                db=db,
                user_id=user_id,
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column

            )
        )

    except Exception as e:
        logger.error("[get_saved_reports_handler] Failed: %s", e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_users_reports.to_dict())



async def get_one_report_handler(report_id: str, user: dict, db: AsyncSession):
    logger.info("[get_one_report_handler][user(%s)] Fetching one report", user)
    user_id = user.get("sub")
    return await get_report(db, user_id, report_id)



async def update_report_handler(body: UpdateReportRequest, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[update_report_handler] Updating a report")

    report_id = str(body.report_id)
    user_id = str(body.user_id)
    new_name = body.report_name
    omitted_notes = body.omitted_notes

    if new_name is None:
        report_data = await get_report(db, user_id, report_id)
        if report_data:
            return {column.key: getattr(report_data, column.key) for column in inspect(UserReports).mapper.column_attrs}
        else:
            return None

    await update_existing_report(db=db, report_id=report_id, omitted_notes=omitted_notes, report_name=new_name)

    return {
        ReportOutputs.message: ReportMessages.updated,
        Fields.report_name: new_name,
        ReportOutputs.report_id: report_id
    }


async def merge_reports_handler(request: Request, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[merge_reports_handler] Merging reports")

    data = await request.json()
    user_id = get_field_from_request(data, Fields.user_id)
    report_id_list = get_field_from_request(data, ReportInputs.report_id)

    if len(report_id_list) < 2:
        logger.warning("[merge_reports_handler] Invalid number of reports for merge")
        raise HTTPException(status_code=400, detail="Invalid number of reports")

    user_dict = await get_user_data_dict(db, user_id)
    report_type = MergedReport.REPORT_TYPE
    report_name = get_field_from_request(data, Fields.report_name, raise_error=False)
    _, report_cost = await validate_access_to_report(db, user_dict, user_id, report_type)

    report_data_list = []
    for report_id in report_id_list:
        report_snapshot = await get_report(db, user_id, report_id)
        if not report_snapshot:
            raise HTTPException(status_code=400, detail=f"Invalid report ID {report_id}")
        report_data_list.append({key: getattr(report_snapshot, key) for key in UserReports.__table__.columns.keys()})

    merged_report = MergedReport(*report_data_list)
    base_report_dict = merged_report.make_merged_report()

    last_modified_date = datetime.now(timezone.utc)

    return await save_merged_report(db, user_id, report_cost,   last_modified_date, base_report_dict, report_type, report_name)


async def get_data_from_snap_api_handler(body: SnapApiRequest, user: dict, db: AsyncSession) -> JSONResponse:
    logger.info("[get_data_from_snap_api_handler][user(%s)] Fetching data from SNAP API", user)

    headers = {
        'Ocp-Apim-Subscription-Key': settings.SNAP_API_CLIENT_SECRET,
        'Accept': 'application/json'
    }
    user_id = user.get("sub")
    report_type = body.report_type
    request_id = ''
    await validate_access_to_report(db=db, user_id=user_id, report_type=report_type)

    request_payload = {report_type: body.report_input_value}

    async with httpx.AsyncClient() as client:
        response = await client.post(Endpoints.snap_report_enpoint + "/" + report_type, headers=headers, json=request_payload)

    if response.status_code==422:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Input value format wrong", user_id)
        raise HTTPException(**CustomErrorMessages.input_value_snap_wrong.to_dict())
    
    if response.status_code==500:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Problems with snap api", user_id)
        raise HTTPException(**CustomErrorMessages.problems_with_snap_api.to_dict())

    if response.status_code not in [200, 202]:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API failed", user_id)
        raise HTTPException(**CustomErrorMessages.snap_api_failed.to_dict(response.status_code))

    request_id = response.json().get('id')
    if not request_id:
        logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API returned no ID", user_id)
        raise HTTPException(**CustomErrorMessages.snap_api_no_id.to_dict())

    return {"id": request_id}


async def insert_verifier_handler(body: InsertVerifier, db: AsyncSession, user: dict):
    logger.info("[insert_verifier_handler][user(%s)] Inserting verifier", user)
    user_id = user.get("sub")
    return await insert_verifier_on_db(db=db, user_id=user_id, verifier=body.verifier)


async def snap_status_ws(app: FastAPI, request_id: str, user_id: str, report_type: str, report_number: str, report_search_args: dict, reports_id: str):
    logger.info("[snap_status_ws][user(%s)] Tracking SNAP status for request_id=%s, user_id=%s, report_type=%s", user_id, request_id, user_id, report_type)

    connection_manager = app.state.connection_manager
    headers = {
        'Ocp-Apim-Subscription-Key': settings.SNAP_API_CLIENT_SECRET,
        'Accept': 'application/json'
    }

    request_payload = {"id": request_id}
    status_code = ReportStatus.InProgress.pending
    timeout_time = time.time() + Timeouts.TIMEOUT_SECONDS


    try:
        ###TODO CONDICIONAL APENAS PARA EXPORTAR O MOCK REMOVER EM PRODUCAO
        if ReportMockValidator.is_test_case(report_type, report_search_args[report_type]):
            from pathlib import Path
            logger.info("[snap_status_ws][user(%s)] Mocking response for CPF: %s", user_id, report_search_args[report_type])

            if ReportMockValidator.is_error_case(report_type, report_search_args[report_type]):
                logger.info("[snap_status_ws][user(%s)] Mocking error response for CPF: %s", user_id,
                            report_search_args[report_type])
                final_result_json = {}
            else:
                file_name = f"{report_search_args[report_type]}.json"
                file_path = Path(f"mocks/{report_type}") / file_name
                logger.info("[snap_status_ws][user(%s)] Attempting to load mock file: %s", user_id, file_path)

                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        final_result_json = json.load(f)
                        logger.info("[snap_status_ws][user(%s)] Successfully loaded mock data from: %s", user_id, file_path)
                except FileNotFoundError:
                    logger.warning("[snap_status_ws][user(%s)] Mock file not found: %s", user_id, file_path)

        else:
            async with httpx.AsyncClient() as client:
                logger.info("[snap_status_ws][user(%s)] Entered polling loop for request_id=%s", user_id, request_id)
                while status_code in [ReportStatus.InProgress.started, ReportStatus.InProgress.pending]:
                    if time.time() > timeout_time:
                        logger.warning("[snap_status_ws][user(%s)] Timeout reached for request_id=%s", user_id, request_id)
                        await update_error_report(snap_request_id=request_id,user_id= user_id,report_id= reports_id)
                        await try_send_websocket(connection_manager, user_id, reports_id,{
                            "id": reports_id,
                            "status_code": SummaryReportStatus.error
                            })
                        break

                    sleep_time = random.randint(5, 20)
                    logger.info("[snap_status_ws][user(%s)] Sleeping for %s seconds before next status check", user_id, sleep_time)
                    await asyncio.sleep(sleep_time)

                    try:
                        results_response = await client.post(
                            Endpoints.status_endpoint,
                            headers=headers,
                            json=request_payload
                        )
                        status_code = results_response.text
                        logger.info("[snap_status_ws][user(%s)] Polled status for request_id=%s: %s", user_id, request_id, status_code)
                    except Exception as e:
                        logger.error("[snap_status_ws][user(%s)] Error polling status for request_id=%s: %s", user_id, request_id, e)
                        continue

                logger.info("[snap_status_ws][user(%s)] Finished polling. Fetching final result for request_id=%s", user_id, request_id)

                max_retries = 3
                attempt = 0
                final_response = None

                while attempt < max_retries:
                    try:
                        final_response = await client.post(
                            Endpoints.result_endpoint,
                            headers=headers,
                            json=request_payload
                        )
                        final_response.raise_for_status()
                        logger.info("[snap_status_ws][user(%s)] Final result fetched successfully on attempt %s for request_id=%s", user_id, attempt + 1, request_id)
                        final_result = final_response
                        final_result_json = json.loads(final_result.text)
                        break
                    except Exception as e:
                        attempt += 1
                        logger.warning("[snap_status_ws][user(%s)] Attempt %s failed to fetch final result for request_id=%s: %s", user_id, attempt, request_id, e)
                        if attempt == max_retries:
                            logger.error("[snap_status_ws][user(%s)] All %s attempts failed for request_id=%s.", user_id, max_retries, request_id)
                            logger.error("[snap_status_ws][user(%s)] Last final_response content: %s", user_id, getattr(final_response, 'text', 'No response'))
                            logger.error("[snap_status_ws][user(%s)] Fallback to results_response content: %s", user_id, getattr(results_response, 'text', 'No response'))

                            final_result = results_response
                        await asyncio.sleep(1)

        logger.info("[snap_status_ws][user(%s)] Final API response parsed for request_id=%s", user_id, request_id)

        if ReportMockValidator.is_error_case(report_type, report_search_args[report_type]):
            await asyncio.sleep(60)

        pessoa_buscada = final_result_json[report_type][0]['SNAP'][0]['pessoa']

        # Extracting person data
        final_result_json_idade = None
        final_result_json_sexo = ""
        final_result_json_nome = ""
        final_result_json_nome_mae = ""

        logger.info("[snap_status_ws][user(%s)] Searching for pessoa with bookmark 4 for request_id=%s", user_id, request_id)
        for pessoa in pessoa_buscada:
            if 'bookmark' in pessoa and pessoa['bookmark'] == 4:
                final_result_json_idade = pessoa.get('idade', None)
                final_result_json_sexo = pessoa.get('sexo', "")
                final_result_json_nome = pessoa.get('full name', "")
                if 'pessoa' in pessoa:
                    for mae in pessoa['pessoa']:
                        if mae.get('label default key') == 'parente MAE' and 'full name' in mae:
                            final_result_json_nome_mae = mae['full name']

        now = datetime.now(timezone.utc).isoformat()
        status_code_formated = ReportStatus.normalize(status_code)      
        logger.info("[snap_status_ws][user(%s)] Building SnapApiResponse object for request_id=%s", user_id, request_id)
        full_data = SnapApiResponse(
            report_name=f"{report_type} {report_number}",
            report_type=report_type,
            user_reports_id=str(reports_id),
            report_status=status_code_formated,
            report_search_args=report_search_args,
            subject_name=final_result_json_nome,
            subject_mother_name=final_result_json_nome_mae,
            subject_age=final_result_json_idade,
            subject_sex=final_result_json_sexo,
            creation_at=now,
            modified_at=now,
            omitted_notes=[],
            data=final_result_json
        )

        result_dict = json.loads(full_data.model_dump_json())

        logger.info("[snap_status_ws][user(%s)] Saving result to MinIO bucket 'reports' for user_id=%s, reports_id=%s", user_id, user_id, reports_id)
        await save_to_minio(bucket_name="reports", object_name=f"{user_id}_{reports_id}.json", data=result_dict, user_id=user_id)

    except WebSocketDisconnect:
        logger.warning("[snap_status_ws][user(%s)] WebSocket disconnected for request_id=%s. Skipping update_error_report.", user_id, reports_id)
        
    except Exception as e:
        logger.error("[snap_status_ws][user(%s)] Unexpected error for request_id=%s: %s", user_id, request_id, e)

        try:
            await update_error_report(snap_request_id=request_id,user_id= user_id,report_id= reports_id)
            logger.info("[snap_status_ws][user(%s)] Report status updated to error in DB for reports_id=%s.", user_id, reports_id)
        except Exception as db_err:
            logger.error("[snap_status_ws][user(%s)] Failed to update report status in DB after error for reports_id=%s: %s", user_id, reports_id, db_err)

        try:
            await try_send_websocket(connection_manager, user_id, reports_id,{
                        "id": reports_id,
                        "status_code": SummaryReportStatus.error
                        })
            logger.info("[snap_status_ws][user(%s)] Error message sent via WebSocket for reports_id=%s.", user_id, reports_id)
        except Exception as ws_err:
            logger.error("[snap_status_ws][user(%s)] Failed to send WebSocket error message for reports_id=%s: %s", user_id, reports_id, ws_err)


async def try_send_websocket(connection_manager, user_id: str, reports_id: str, payload: dict) -> bool:
    logger.info(f"[try_send_websocket][user{user_id}] Try sending websocket requestsnapid = {reports_id}.")
    websocket = connection_manager.get_connection(user_id)
    if websocket:
        try:
            await websocket.send_json(payload)
            logger.info("[try_send_websocket][user(%s)] Sent websocket to user_id = %s and requestsnapid = %s.", user_id, user_id, reports_id)
            return True
        except Exception as e:
            logger.error("[try_send_websocket][user(%s)] Error sending websocket to user_id = %s and requestsnapid = %s: %s", user_id, user_id, reports_id, str(e))
            connection_manager.disconnect(user_id)

    return False



async def save_to_minio(bucket_name: str, object_name: str, data: dict, user_id: str):
    logger.info("[save_to_minio][user(%s)] Saving object %s to bucket %s", user_id, object_name, bucket_name)
    try:
        if not minio_client.bucket_exists(bucket_name):
            logger.warning("[save_to_minio][user(%s)] Bucket %s not found. Creating...", user_id, bucket_name)
            minio_client.make_bucket(bucket_name)

        json_bytes = json.dumps(data, indent=2).encode("utf-8")
        json_stream = BytesIO(json_bytes)

        minio_client.put_object(
            bucket_name,
            object_name,
            data=json_stream,
            length=len(json_bytes),
            content_type="application/json"
        )
        logger.info("[save_to_minio][user(%s)] Upload completed", user_id)

    except Exception as e:
        logger.error("[save_to_minio] Failed to save object: %s", e)


async def load_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[load_from_minio][user(%s)] Loading object %s", user_id, object_name)
    try:
        response = minio_client.get_object(bucket_name, object_name)
        data = json.loads(response.read())
        data_keys = list(data.keys()) if isinstance(data, dict) else type(data)
        logger.info(f"[load_from_minio][user({user_id})] Retrieved object with keys: {data_keys}")

        return data
    except Exception as e:
        logger.error("[load_from_minio][user(%s)] Failed to load object %s: %s", user_id, object_name, e)
        return None
    
async def delete_from_minio(bucket_name: str, object_name: str, user_id: str) -> dict | None:
    logger.info("[delete_from_minio][user(%s)] Deleting object %s", user_id, object_name)
    try:

        minio_client.remove_object(bucket_name, object_name)
        minio_client.remove_object("reports", object_name)
        logger.info("[delete_from_minio][user(%s)] Object %s deleted after load", user_id, object_name)

    except Exception as e:
        logger.error("[delete_from_minio][user(%s)] Failed to delete object %s: %s", user_id, object_name, e)


