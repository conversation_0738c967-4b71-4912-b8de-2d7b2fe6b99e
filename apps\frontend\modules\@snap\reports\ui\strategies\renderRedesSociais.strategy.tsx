import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { ReadOnlyInputField, CustomLabel, GridContainer } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidUrl, isBase64Image, renderSourceTooltip } from "./helpers.strategy";
import { PerfilRedeSocial } from "../../model/PerfisRedesSociais";
import { ValueWithSource } from "../../model/ValueWithSource";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderPerfisRedesSociais implements RenderStrategy<PerfilRedeSocial> {
    validateKeys = (keys: Array<keyof PerfilRedeSocial>): boolean => {
        return keys.some((campo) => campo in this.formatByKey);
    };

    /**
     * Checks if a value is an array of objects with the expected social media profile format
     * @param value The value to check
     * @returns True if the value is an array of social media profile objects
     */
    isProfileArray = (value: any): boolean => {
        if (!Array.isArray(value) || value.length === 0) {
            return false;
        }

        // Check if the first item has the expected structure
        const firstItem = value[0];
        return (
            typeof firstItem === 'object' &&
            firstItem !== null &&
            'value' in firstItem &&
            'source' in firstItem &&
            typeof firstItem.value === 'object'
        );
    };

    formatByKey: Record<
        string,
        (redeSocial?: PerfilRedeSocial) => React.ReactElement | null
    > = {
            // This is a placeholder to satisfy the RenderStrategy interface
            // The actual rendering is done by renderProfile method
        };

    validateData = (redeSocial?: PerfilRedeSocial): boolean => {
        try {
            if (!redeSocial || typeof redeSocial !== 'object') {
                console.warn('Invalid redeSocial data received:', redeSocial);
                return false;
            }
            return true;
        } catch (error) {
            console.warn('Error validating redeSocial data:', error);
            return false;
        }
    };

    /**
     * Renders a social media profile
     * @param key The social media platform key
     * @param redeSocial The PerfilRedeSocial object
     * @returns React element or null if no data
     */
    renderProfile = (key: string, redeSocial?: PerfilRedeSocial): React.ReactElement | null => {
        // Get the data array for this social media platform
        const profiles = redeSocial?.[key] as Array<any> | undefined;

        if (!profiles?.length) return null;

        // Format the platform name for display
        let platformName = key.charAt(0).toUpperCase() + key.slice(1);

        return (
            <GridContainer cols={1} className="">
                <GridItem fullWidth className="text-primary ">
                    <CustomLabel
                        label={platformName.toUpperCase()}
                        colorClass="bg-primary"
                    />
                </GridItem>

                <GridItem fullWidth>
                    <GridContainer cols={2}>
                        {profiles.map((profile, index) => (
                            <GridItem key={`${key}-column-${index}`} cols={1}>
                                <div className="mb-4">
                                    <div className="group">
                                        <CustomReadOnlyInputField
                                            label={`${(profile.label || platformName).toUpperCase()} ${index + 1}`}
                                            colorClass="bg-border"
                                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                            value=""
                                            tooltip={renderSourceTooltip(profile.source)}
                                        />
                                    </div>
                                    <div className="pl-5 pt-2">
                                        {Object.entries(profile.value as Record<string, ValueWithSource<string>> || {}).map(([key, value]: [any, any], index: number) => {
                                            if (isBase64Image(value.value)) {
                                                return (
                                                    <div className="flex flex-col gap-3 py-2 group" key={key + index}>
                                                        <CustomReadOnlyInputField
                                                            label={`${(value.label || key).toUpperCase()}`}
                                                            value={""}
                                                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                                            tooltip={renderSourceTooltip(value.source)}
                                                        />
                                                        <img
                                                            src={value.value}
                                                            alt={key}
                                                            className="max-w-full h-auto"
                                                        />
                                                    </div>
                                                );
                                            }

                                            if (isValidUrl(value.value) && !isBase64Image(value.value)) {
                                                return (
                                                    <div className="py-2 group" key={key + index}>
                                                        <CustomReadOnlyInputField
                                                            label={`${(value.label || key).toUpperCase()}`}
                                                            value={String(value.value)}
                                                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                                            tooltip={renderSourceTooltip(value.source)}
                                                        />
                                                        <img
                                                            src={String(value.value)}
                                                            alt={`Imagem ${index + 1}`}
                                                            className="max-w-full h-auto pt-4"
                                                        />
                                                    </div>
                                                );
                                            }

                                            return (
                                                <div className="py-2 group" key={key + index}>
                                                    <CustomReadOnlyInputField
                                                        label={`${(value.label || key).toUpperCase()}`}
                                                        value={String(value.value)}
                                                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                                        tooltip={renderSourceTooltip(value.source)}
                                                    />
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </GridItem>
                        ))}
                    </GridContainer>
                </GridItem>
            </GridContainer>
        );
    };

    render = (redeSocial?: PerfilRedeSocial): React.ReactElement[] => {
        try {
            if (!this.validateData(redeSocial)) {
                console.warn('Invalid data structure received');
                return [];
            }

            const keys = Object.keys(redeSocial!) as Array<keyof PerfilRedeSocial>;

            /*   if (!this.validateKeys(keys)) {
                  console.warn('Invalid keys in data:', keys);
                  return [];
              }
   */
            return keys
                .map((chave) => {
                    try {
                        if (chave in this.formatByKey) {
                            return this.formatByKey[chave]?.(redeSocial);
                        }

                        const value = redeSocial![chave];
                        if (this.isProfileArray(value)) {
                            return this.renderProfile(chave as string, redeSocial);
                        }

                        return null;
                    } catch (error) {
                        console.warn(`Error processing key ${chave}:`, error);
                        return null;
                    }
                })
                .filter((el): el is React.ReactElement => el !== null);
        } catch (error) {
            console.warn('Error rendering redeSocial data:', error);
            return [];
        }
    };
}