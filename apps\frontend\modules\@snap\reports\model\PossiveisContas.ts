import { ValueWithSource } from "./ValueWithSource";

export interface _PossivelConta {
    aplicacao?: string;
    found?: string;
    entrada_cpf?: string;
    alerta?: string;
    tipo_alerta?: string;
    existe?: string;
}

export interface PossivelConta {
    site: ValueWithSource<_PossivelConta["aplicacao"]>;
    detalhes: Record<keyof Omit<_PossivelConta, "aplicacao">, ValueWithSource<_PossivelConta[keyof Omit<_PossivelConta, "aplicacao">]>>;
}
