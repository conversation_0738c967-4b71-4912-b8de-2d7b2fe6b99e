import logging
from datetime import datetime, timezone
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, Depends, status, Path
from sqlalchemy import select, insert, update
from sqlalchemy.inspection import inspect
from sqlalchemy.ext.asyncio import AsyncSession
import uuid
import sqlalchemy
import json

from jose import jwt

from services.auth_service import auth_guard
from utils.utils import create_salt
from database.db import DATABASE_URL, get_db, get_database_async_engine
from models.user_model import Users
from core.constants import Fields
from core.jwt_utils import verify_jwt
from schemas.user_schema import UserCreate, UserOut

# Setup logger
logger = logging.getLogger(__name__)


async def get_user_data_info_handler(user_id: str = Path(..., description="ID do usuário"), user=Depends(auth_guard)) -> UserOut:
    logger.info("[get_user_data_info_handler] Authorized - Username: %s", user.get("preferred_username"))
    logger.info("[get_user_data_info_handler] user_id: %s", user_id)
    # Implementation needed (currently only logs)


async def get_access_handler(request: Request, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)) -> dict:
    logger.info("[get_access_handler] Checking user access...")
    logger.info("[get_access_handler] Authorized - Username: %s", user.get("preferred_username"))

    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        logger.error("[get_access_handler] Missing or invalid Authorization header")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

    token = auth_header.split(" ")[1]
    claims = await verify_jwt(token)
    user_data = claims["sub"]
    logger.info("[get_access_handler] Verified user_data: %s", user_data)

    try:
        await get_user_access_document(db, user_data)
        user_uuid = uuid.UUID(user_data["userId"])
    except ValueError:
        logger.error("[get_access_handler] Invalid user UUID format")
        return None

    result = await db.execute(select(Users).where(Users.userId == user_uuid))
    user_record = result.scalars().first()
    logger.info("[get_access_handler] User record fetched: %s", user_record)
    return user_record



async def sync_user(access_token: str) -> dict:
    logger.info("[sync_user] Syncing user with Keycloak token claims...")
    user_info = jwt.get_unverified_claims(access_token)
    user_id = user_info.get("sub")
    email = user_info.get("email")
    full_name = user_info.get("name")
    logger.info("[sync_user] Extracted user info: %s", user_info)
    await upsert_user(user_id, email, full_name)


async def upsert_user(user_id: str, email: str, full_name: str):
    logger.info("[upsert_user] Upserting user record...")
    engine = get_database_async_engine()
    salt = create_salt()
    now = datetime.now(timezone.utc)

    query = """
    INSERT INTO public.users ("user_id", "name", "email","salt", "credits","last_login", "report_types")
    VALUES (:user_id, :full_name, :email, :salt, :credits, :last_login, :report_types)
    ON CONFLICT ("user_id") 
    DO UPDATE SET "name" = :full_name, "email" = :email, "last_login" = :last_login;
    """

    async with engine.begin() as conn:
        await conn.execute(sqlalchemy.text(query), {
            "user_id": user_id,
            "full_name": full_name,
            "email": email,
            "last_login": now,
            "salt": salt,
            "credits": 0,
            "report_types": json.dumps({})
        })
    logger.info("[upsert_user] User upsert completed.")



async def create_new_user(db: AsyncSession, user_data: UserCreate) -> dict:
    logger.info("[create_new_user] Creating new user...")
    save_data = {
        Fields.user_id: user_data.user_id,
        Fields.name: user_data.name,
        Fields.email: user_data.email
    }

    query = (
        insert(Users)
        .values(**save_data)
        .returning(*Users.__table__.columns)
    )
    result = await db.execute(query)
    await db.commit()
    logger.info("[create_new_user] New user created successfully.")
    return result.mappings().first()


async def get_user_access_document(db: AsyncSession, user_data: dict) -> dict:
    logger.info("[get_user_access_document] Fetching user access document...")
    user_id = user_data["user_id"]

    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        logger.error("[get_user_access_document] Invalid user UUID format")
        return None

    document_dict = await get_user_data(db, user_id)
    if document_dict is None:
        logger.warning("[get_user_access_document] User not found, creating new user...")
        document_dict = await create_new_user(db=db, user_data=user_data)
    else:
        logger.info("[get_user_access_document] User found, updating last_login timestamp...")
        await db.execute(
            update(Users)
            .where(Users.user_id == user_uuid)
            .values(last_login=datetime.now(timezone.utc))
        )
        await db.commit()

    logger.info("[get_user_access_document] User document: %s", document_dict)
    return {
        "user_id": user_id,
        "report_types": document_dict.get("report_types", {}),
        "credits": document_dict.get("credits", 0)
    }


async def get_user_data(db: AsyncSession, user_id: str) -> dict:
    logger.info("[get_user_data] Fetching user data for user_id=%s", user_id)
    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        logger.error("[get_user_data] Invalid UUID format for user_id=%s", user_id)
        return None

    result = await db.execute(select(Users).where(Users.user_id == user_uuid))
    user_record = result.scalars().first()

    if user_record:
        logger.info("[get_user_data] User record found for user_id=%s", user_id)
        return {column.key: getattr(user_record, column.key) for column in inspect(Users).mapper.column_attrs}
    
    logger.warning("[get_user_data] No user record found for user_id=%s", user_id)
    return None

