import { Button, Select } from "@snap/design-system";
import { Check } from "lucide-react";
import { useEffect } from "react";
import { ReportInput } from "~/components/ReportInput";
import { useUserData } from "~/store/userStore";
import {
  useNewReportActions,
  useNewReportInputValue,
  useNewReportSelectedType,
} from "~/store/newReportStore";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";
import { useDialogActions } from "~/store/dialogStore";
import { ReportCredits } from "~/types/global";
import { useEncryption } from "~/hooks/useEncryption";

export interface CreateReportDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

export function CreateReportDialogContent({}: CreateReportDialogContentProps) {
  const userData = useUserData();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const { setSelectedReportType, setReportInputValue } = useNewReportActions();
  const reportTypes = userData?.report_types;
  const options = Object.entries(reportTypes as ReportCredits | object)
    .filter(([key]) => key !== "combinado")
    .map(([key]) => ({
      value: key,
      label: key.toUpperCase(),
    })
  );

  useEffect(() => {
    setSelectedReportType(Object.keys(userData?.report_types || {})[0] || "");
  }, []);

  return (
    <div className="space-y-4">
      <p className="text-md mb-4 font-semibold">
        Que tipo de item você deseja criar?
      </p>
      <div className="flex items-center">
        <Button
          className="uppercase w-full !bg-neutral-400"
          onClick={() => console.log("Criar Relatório selecionado")}
          data-testid="button-create-report"
        >
          Criar Relatório
        </Button>
        <Button
          className="font-mono uppercase w-full !bg-neutral-400"
          onClick={() => console.log("Criar Pasta selecionado")}
          data-testid="button-create-folder"
        >
          Criar Pasta
        </Button>
      </div>
      <div className="mb-12">
        <div className="grid grid-cols-2 gap-4 mb-2">
          <div>
            <label className="block text-xs mb-1">Tipo de entrada:</label>
            <Select
              options={options}
              value={selectedReportType}
              onChange={setSelectedReportType}
              placeholder="Selecionar tipo"
              data-testid="select-report-type"
            />
          </div>
          <div>
            <label className="block text-xs mb-1">Preencha o campo:</label>
            <div className="pt-1.5">
              <ReportInput
                inputValue={reportInputValue}
                setInputValue={setReportInputValue}
                reportType={selectedReportType}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function CreateReportDialogFooter() {
  const { newReportMutation } = useReportCRUD();
  const { encryptData } = useEncryption();
  const selectedReportType = useNewReportSelectedType();
  const reportInputValue = useNewReportInputValue();
  const { closeDialog } = useDialogActions();

  const handleCreateNewReport = async () => {
    if (!selectedReportType || !reportInputValue) {
      toast("Preencha todos os campos", {
        description: "Necessário tipo de entrada e seu valor",
        action: {
          label: "Fechar",
          onClick: () => {},
        },
      });
      return;
    }
    const searchArgsFormatInput = {
      [selectedReportType]: [reportInputValue],
    };
    const encryptedValue = await encryptData(searchArgsFormatInput);

    if (!encryptedValue.data) {
      toast("Erro", {
        description: "Erro ao criptografar dados",
        action: {
          label: "Fechar",
          onClick: () => {},
        },
      });
      return;
    }

    newReportMutation.mutateAsync({
      report_type: selectedReportType,
      report_input_value: reportInputValue,
      report_input_encrypted: encryptedValue.data,
    });
  };

  const handleCloseDialog = () => {
    closeDialog();
  };

  return (
    <div className="flex gap-4">
      <Button
        className="uppercase !bg-neutral-400"
        onClick={
          handleCreateNewReport || (() => console.log("Criar Relatório"))
        }
        data-testid="button-confirm-create-report"
      >
        Criar Relatório <Check />
      </Button>
    </div>
  );
}

// Export as static properties so you can use the composition pattern
export const CreateReportDialog = {
  Content: CreateReportDialogContent,
  Footer: CreateReportDialogFooter,
};
