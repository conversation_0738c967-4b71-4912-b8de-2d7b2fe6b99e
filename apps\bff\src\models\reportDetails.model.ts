export type ReportType = "cpf" | "cnpj" | "email" | "telefone" | null;

export interface ReportDetails {
  result: {
    creationDate: string;
    creditsUsed: number;
    data: Record<string, unknown>; // retorno dinâmico imprevisível
    lastModified: string;
    omittedNodes: any[];
    reportName: string;
    reportStatus: string;
    reportType: ReportType;
    requestDate: string;
    searchArgs: Record<string, string[]>;
  };
}

export interface EncryptedData {
  encrypted: number[];
  iv: number[];
}
