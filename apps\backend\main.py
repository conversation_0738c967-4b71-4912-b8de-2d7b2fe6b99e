import logging
import os
from datetime import datetime, timezone
import time
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response


from core.config import settings

LOG_LEVEL = settings.LOG_LEVEL
os.makedirs("logs", exist_ok=True)

today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
log_file_path = f"logs/{today}.log"
logging.Formatter.converter = time.localtime

# Setup logging
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s [%(levelname)s] [%(name)s]: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_file_path, mode='a')
    ]
)

logger = logging.getLogger(__name__)
logger.info("App started.")

from fastapi import FastAPI, WebSocket
from starlette.middleware.cors import CORSMiddleware
from api.routes import router
from typing import Dict, List, Tuple
import asyncio
from database.db import init_db
from contextlib import asynccontextmanager
from kafka_consumer import start_minio_event_consumer


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("[lifespan] Initializing database...")
    await init_db()
    logger.info("[lifespan] Database initialized.")

    logger.info("[lifespan] Starting Kafka consumer...")
    asyncio.create_task(start_minio_event_consumer(app))
    yield

app = FastAPI(lifespan=lifespan)
app.state.running_snap_tasks = {}

origins = [
    "http://***************:3000",
    "http://***************:8000",
    "http://***************:8080",
    "http://***************:5432",
    "https://sturgeon-big-tapir.ngrok-free.app",
    "https://modest-shrew-urgently.ngrok-free.app",
    "http://***************",
    "http://localhost"
]


logger.info("[setup] Configuring CORS middleware...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO CHANGE THE ALOWED ORINGS
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)



class NoCacheMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        response: Response = await call_next(request)
        response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        return response

app.add_middleware(NoCacheMiddleware)

logger.info("[setup] Registering routers...")
app.include_router(router)

@app.get("/")
async def root():
    logger.info("[root] Health check endpoint called")
    return {"message": "FastAPI backend running"}

class ConnectionManager:
    def __init__(self):
        logger.info("[ConnectionManager] Initializing Connection Manager...")
        self.active_connections: Dict[str, WebSocket] = {}

    # def _key(self, user_id: str, user_reports_id: str) -> str:
    #     return f"{user_id}_{user_reports_id}"

    async def connect(self, websocket: WebSocket, user_id: str):
        logger.info(f"[ConnectionManager.connect] Connecting WebSocket for user {user_id}")
        await websocket.accept()
        self.active_connections[user_id] = websocket
        logger.info("[ConnectionManager.connect] Total connections: %s", len(self.active_connections))

    async def disconnect(self, user_id: str):
        logger.info(f"[ConnectionManager.disconnect] Disconnecting WebSocket for user {user_id}")
        websocket = self.active_connections.pop(user_id, None)
        await websocket.close(code=1001, reason="Server is closing the connection")
        logger.info("[ConnectionManager.disconnect] Total connections after disconnect: %s", len(self.active_connections))
        self.log_connections()

    def get_connection(self, user_id: str):
        websocket = self.active_connections.get(user_id)
        if websocket:
            logger.info("[ConnectionManager.get_connection] WebSocket found for user %s", user_id)
        else:
            logger.warning("[ConnectionManager.get_connection] No WebSocket found for user %s", user_id)
        return websocket

    async def send_to_user(self, user_id: str, message: str):
        websocket = self.get_connection(user_id)
        if websocket:
            try:
                await websocket.send_text(message)
                logger.info(f"[ConnectionManager.send_to_user] Message sent to user {user_id}")
            except Exception as e:
                logger.warning(f"[ConnectionManager.send_to_user] Failed to send message to user {user_id}: {e}")
                await self.disconnect(user_id)
    
    def list_connections(self) -> List[str]:
        return list(self.active_connections.keys())
    
    def log_connections(self):
        if self.active_connections:
            logger.info("[ConnectionManager] Active connections: %s", self.list_connections())
        else:
            logger.info("[ConnectionManager] No active connections.")

app.state.connection_manager = ConnectionManager()
logger.info("[setup] ConnectionManager instance created and assigned.")
