import { Router } from "express";
import { addUserSalt, getUserSalt, initializeUserSalt } from "../controllers/user.controller";

const router = Router();

router.get("/:sub/salt", getUserSalt);
router.post("/:sub/salt", addUserSalt);
router.post("/:sub/initialize/salt", initializeUserSalt);
router.get("/:sub/verifier", getUserSalt);
router.post("/:sub/verifier", initializeUserSalt);

export default router;
