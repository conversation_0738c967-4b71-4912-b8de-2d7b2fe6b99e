import { Request, Response, NextFunction } from "express";
import { fetchReportData, fetchReportList } from "../services/report.service";
import { EncryptedData, ReportDetails } from "../models/reportDetails.model";
import {
  ValidationError,
  NotFoundError,
  AuthenticationError,
} from "../@types/errors";
import jwt from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: any;
  token?: string;
}

export const getReportsList = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = req.cookies.token;

    // if (!token) {
    //   throw new AuthenticationError("No authentication token provided");
    // }

    // try {
    //   const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    //   req.user = decoded;
    //   // [TODO] - adicionar validações do token
    // } catch (jwtError) {
    //   throw new AuthenticationError("Invalid or expired token");
    // }

    const data: EncryptedData | null = await fetchReportList();

    if (!data) {
      throw new NotFoundError("Reports");
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    next(error);
  }
};

export const getReportById = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params;
    // const token = req.cookies.token;

    // if (!id) {
    //   throw new ValidationError("Report ID is required");
    // }

    // if (!token) {
    //   throw new AuthenticationError("No authentication token provided");
    // }

    // try {
    //   const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    //   req.user = decoded;
    //   // [TODO] - adicionar validações do token
    //   // [TODO - GERAR NOVO RELATORIO] - complementar verificação de creditos para realizar consulta
    //   // const { report_type_credits } = req.user;
    //   // const reportType = "cpf";
    //   // if (!report_type_credits || report_type_credits[reportType] <= 0) {
    //   //   throw new AuthenticationError(
    //   //     "Insufficient credits for this report type"
    //   //   );
    //   // }
    // } catch (jwtError) {
    //   throw new AuthenticationError("Invalid or expired token");
    // }

    const data: EncryptedData | null = await fetchReportData(id);

    if (!data) {
      throw new NotFoundError("Report");
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    next(error);
  }
};
