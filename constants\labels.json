{"label_default_key": "label default key", "credilink_label": "credilink label", "grau_de_parentesco": "grau de parentesco", "phonenumber": "phonenumber", "endereco": "endereco", "bookmark": "bookmark", "first_names": "first names", "pessoa": "pessoa", "surname": "surname", "endpoint": "endpoint", "operadora": "operadora", "whatsapp": "whatsapp", "emailaddress": "emailaddress", "location": "location", "mandado": "man<PERSON>o", "empresa": "empresa", "razao_social": "razao social", "remuneracao": "remuneracao", "processos": "processos", "despesas_publicas_recursos_recebidos": "despesas publicas recursos recebidos", "empenho": "<PERSON><PERSON><PERSON>", "viagem": "viagem", "servidor_publico": "servidor publico", "diario_oficial": "diario oficial", "partido_politico": "partido politico", "pais": "PAIs", "provedor_de_aplicacoes_de_internet": "provedor de aplicacoes de internet", "candidato": "candidato", "alcunha": "alcunha", "data_nascimento": "data nascimento", "full_name": "full name", "naturalidade": "naturalidade", "nome_mae": "nome mae", "nome_pai": "nome pai", "pena_imposta": "pena imposta", "recaptura": "recaptura", "sexo": "sexo", "source": "source", "entrada_sociedade": "entradasociedade", "cargo_em_sociedade": "cargo em sociedade", "qualificacao_societaria": "qualificação societária", "faixa_etaria": "faixa etaria", "participacao_sociedade": "participacao sociedade", "cargo": "cargo", "data_inicio_sociedade": "data de inicio em sociedade", "data_fim_sociedade": "data de fim em sociedade", "identificador_socio": "identificador socio"}