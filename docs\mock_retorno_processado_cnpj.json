{"id": "6f6e06e9-af51-4067-a53f-03c8c17c4da8", "status_code": "success", "result": {"data": {"report_name": "cnpj 3", "report_type": "cnpj", "user_reports_id": "6f6e06e9-af51-4067-a53f-03c8c17c4da8", "report_status": "pending", "report_search_args": {"cnpj": "16670085000155"}, "subject_name": "TECHBIZ FORENSE DIGITAL LTDA", "subject_mother_name": "", "subject_age": null, "subject_sex": "", "creation_at": "2025-05-19T20:20:56.920580+00:00", "modified_at": "2025-05-19T20:20:56.920580+00:00", "omitted_notes": [], "data": {"cnpj": [{"title": "Dados Empresa", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "CadastroNacionalPJ"], "data_count": 13, "data": [{"detalhes": {"razao social": {"value": "TECHBIZ FORENSE DIGITAL LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "cnpj": {"value": "05.757.597/0002-18", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "procon": {"value": "(NAO TEM)", "label": "PROCON", "source": ["SNAP"], "is_deleted": false}, "info restricao": {"value": "* NADA CONSTA *", "label": "Info Restrição", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "ATIVA", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "data de fundacao": {"value": "24/06/2009", "label": "Data de Fundação", "source": ["SNAP"], "is_deleted": false}, "data_abertura": {"value": "24/06/2009", "label": "Data de Abertura", "source": ["SNAP"], "is_deleted": false}, "sequencial": {"value": "1", "label": "Sequencial", "source": ["SNAP"], "is_deleted": false}, "porte": {"value": "DEMAIS EMPRESA", "label": "Porte", "source": ["SNAP"], "is_deleted": false}, "tipo de imposto": {"value": "LUCRO REAL", "label": "Tipo de Imposto", "source": ["SNAP"], "is_deleted": false}, "total de funcionarios": {"value": "35", "label": "Total de Funcionários", "source": ["SNAP"], "is_deleted": false}, "quantidade de funcionarios acima de 5 salarios": {"value": "10", "label": "Funcionários acima de 5 salários", "source": ["SNAP"], "is_deleted": false}, "quantidade de funcionarios abaixo de 5 salarios": {"value": "25", "label": "Funcionários abaixo de 5 salários", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Telefones", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"], "data_count": 3, "data": [{"detalhes": [{"value": {"phone number": {"value": "553125860228", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "5531983462818", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "5531988684136", "label": "Número do Telefone", "source": ["SintegraMACNPJ"], "is_deleted": false}}, "label": "Telefone", "source": ["SintegraMACNPJ"], "is_deleted": false}]}]}, {"title": "Emails", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "CadastroNacionalPJ"], "data_count": 2, "data": [{"detalhes": [{"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}, {"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Endereços", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "CadastroNacionalPJ", "SintegraMTCNPJ", "SintegraMACNPJ", "SintegraDeSaoPauloCNPJ", "SintegraPBCNPJ", "SintegraSECNPJ", "SintegraMGCNPJ", "SintegraCECNPJ", "SintegraPRCNPJ"], "data_count": 2, "data": [{"detalhes": [{"value": {"logradouro": {"value": "AL DA SERRA", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "400", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "3", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "VILA DA SERRA", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "MG", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "34000000", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "3132111863", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "cidade": {"value": "NOVA LIMA", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "estado ou regiao": {"value": "MG", "label": "Estado ou Região", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}, {"value": {"logradouro": {"value": "RUA SERGIPE", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "1014", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "SALA 502", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "SAVASSI", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "MG", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "30130174", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "3132862339", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "cidade": {"value": "BELO HORIZONTE", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "estado ou regiao": {"value": "MG", "label": "Estado ou Região", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "CadastroNacionalPJ"], "data_count": 5, "data": [{"detalhes": [{"value": {"full name": {"value": "GIOVANI THIBAU CHRISTOFARO", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO ADMINISTRADOR", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "68000367653", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "04/09/2006", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "JAIME RODRIGUES BARBOSA NETO", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO ADMINISTRADOR", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "03770139607", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "08/09/2016", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "LUCIANA BISPO DA SILVA GALAO", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84421630187", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "MARCIO JOSE ROSA GOMES", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "47322497104", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "30/08/2019", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "RAFAEL VELASQUEZ SAAVEDRA DA SILVA", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "01356010610", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Jun<PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["JUCESP"], "data_count": 2, "data": [{"razao_social": {"value": "FACEBOOK SERVICOS ONLINE DO BRASIL LTDA.", "label": "Razão Social", "source": ["JUCESP"], "is_deleted": false}, "detalhes": {"nire": {"value": "35225174099", "label": "NIRE", "source": ["JUCESP"], "is_deleted": false}, "uf": {"value": "SP", "label": "UF", "source": ["JUCESP"], "is_deleted": false}}}, {"razao_social": {"value": "TECHINOVACAO TECNOLOGIA DA INFORMACAO LTDA.", "label": "Razão Social", "source": ["JUCESP"], "is_deleted": false}, "detalhes": {"nire": {"value": "12345678901", "label": "NIRE", "source": ["JUCESP"], "is_deleted": false}, "uf": {"value": "RJ", "label": "UF", "source": ["JUCESP"], "is_deleted": false}}}]}, {"title": "Processos", "subtitle": "Dados consultados na API SNAP.", "source": ["Escavador"], "data_count": 2, "data": [{"numero": {"value": "0001234-56.2023.8.26.0100", "label": "Número do Processo", "source": ["Escavador"], "is_deleted": false}, "detalhes": {"instancia": {"value": "1ª Instância", "label": "Instância", "source": ["Escavador"], "is_deleted": false}, "orgao": {"value": "Tribunal de Justiça de São Paulo", "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, "data da remessa": {"value": "2023-05-15", "label": "<PERSON> da Remessa", "source": ["Escavador"], "is_deleted": false}, "data da instauracao": {"value": "2023-01-10", "label": "Data da Instauração", "source": ["Escavador"], "is_deleted": false}}, "movimentacoes": {"value": "ata - Conteudo - Tipo,2025-01-21 - Intimação do <PERSON><PERSON> <PERSON>, <PERSON><PERSON>. <PERSON><PERSON>, Dra. <PERSON> Mo<PERSON>s e Dr<PERSON> <PERSON> Barros Vidal do despacho de f. 4448, que segue transcrito: “Ciência às partes acerca do Acórdão de f. 4426-39. <PERSON><PERSON><PERSON>, cumpram-se as determinações contidas na sentença de f. 4122-47. Às providências necessárias.\" Campo Grande, 13 de janeiro de 2025. CARLOS ALBERTO GARCETE Juiz de Direito em Substituição Legal - PUBLICACAO,2024-12-02 - <PERSON><PERSON><PERSON> <PERSON> Grande - 2ª Vara do Tribunal do Júri EMENTA - APELAÇÃO CRIMINAL - HOMICÍDIO QUALIFICADO - RECURSO INTERPOSTO PELO MINISTÉRIO PÚBLICO ESTADUAL - IMPRONÚNCIA MANTIDA - AUSÊNCIA DE INDÍCIOS MÍNIMOS E SUFICIENTES APTOS A CONVENCER DA MATERIALIDADE DO FATO E DA EXISTÊNCIA DE INDÍCIOS SUFICIENTES DA AUTORIA/PARTICIPAÇÃO - RECURSO DESPROVIDO. 1. <PERSON> bem sabe, o sistema processual penal brasileiro adota duas fases distintas para o processamento dos crimes dolosos contra a vida: a primeira, denominada de jus accusationis, tem objetivo voltado ao juízo de admissibilidade da acusação; já a segunda", "label": "Movimentações", "source": ["Escavador"], "is_deleted": false}, "advogado": [{"value": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 12345", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 54321", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}], "autor_pessoa": [{"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Autor", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "444.555.666-77", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Autor", "source": ["Escavador"], "is_deleted": false}], "reu_empresa": [{"value": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa ABC Ltda.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, {"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}], "juiz": [{"value": {"cpf": {"value": "555.666.777-88", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "Dr. <PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "999.888.777-66", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "Dr. <PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}], "representante_pessoa": [{"value": {"cpf": {"value": "999.888.777-66", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Representante", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Representante", "source": ["Escavador"], "is_deleted": false}]}, {"numero": {"value": "0007890-12.2022.8.26.0200", "label": "Número do Processo", "source": ["Escavador"], "is_deleted": false}, "detalhes": {"instancia": {"value": "2ª Instância", "label": "Instância", "source": ["Escavador"], "is_deleted": false}, "orgao": {"value": "Tribunal de Justiça de São Paulo", "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, "data da remessa": {"value": "2022-11-20", "label": "<PERSON> da Remessa", "source": ["Escavador"], "is_deleted": false}, "data da instauracao": {"value": "2022-08-05", "label": "Data da Instauração", "source": ["Escavador"], "is_deleted": false}}, "movimentacoes": {"value": "20/11/2022 - Recurso recebido pelo Tribunal\n15/10/2022 - Recurso interposto pela parte autora\n30/09/2022 - Sentença proferida: procedente em parte\n15/09/2022 - Conclusos para sentença\n05/08/2022 - Distribuição do processo", "label": "Movimentações", "source": ["Escavador"], "is_deleted": false}, "advogado": [{"value": {"cpf": {"value": "222.333.444-55", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 78901", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "555.666.777-88", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 54321", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}], "recorrente_empresa": [{"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrente", "source": ["Escavador"], "is_deleted": false}, {"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrente", "source": ["Escavador"], "is_deleted": false}], "recorrido_pessoa": [{"value": {"cpf": {"value": "444.555.666-77", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrido", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrido", "source": ["Escavador"], "is_deleted": false}]}]}, {"title": "Diários Oficiais - CNPJ", "subtitle": "Dados consultados na API SNAP através do CNPJ de entrada.", "source": ["EscavadorDOCNPJ"], "data_count": 4, "data": [{"local": {"value": "<PERSON><PERSON><PERSON> União", "label": "Local", "source": ["EscavadorDOCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "15/05/2023", "label": "Data", "source": ["EscavadorDOCPF"], "is_deleted": false}, "texto correspondente": {"value": "Nomear JOÃO DA SILVA, CPF 123.456.789-00, para exercer o cargo de Analista Administrativo do quadro de pessoal do Ministério da Economia.", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}, "dados adicionais": {"value": "Se<PERSON> 2, <PERSON><PERSON><PERSON><PERSON> 45", "label": "<PERSON><PERSON>", "source": ["EscavadorDOCPF"], "is_deleted": false}, "link": {"value": "https://www.in.gov.br/web/dou/-/portaria-de-15-de-maio-de-2023", "label": "Link", "source": ["EscavadorDOCPF"], "is_deleted": false}}, "descricao": {"value": "Portaria de nomeação para cargo público", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}}, {"local": {"value": "Diário de Justiça do Estado de São Paulo", "label": "Local", "source": ["EscavadorDOCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "22/06/2023", "label": "Data", "source": ["EscavadorDOCPF"], "is_deleted": false}, "texto correspondente": {"value": "Processo nº 0001234-56.2023.8.26.0100. Autor: <PERSON>. Réu: Empresa XYZ Ltda. Juiz: Dr. <PERSON>. Decisão: <PERSON><PERSON> procedente o pedido...", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}, "dados adicionais": {"value": "Cade<PERSON> 4, <PERSON><PERSON><PERSON><PERSON> 123", "label": "<PERSON><PERSON>", "source": ["EscavadorDOCPF"], "is_deleted": false}, "link": {"value": "https://www.dje.tjsp.jus.br/cdje/consultaSimples.do?cdVolume=15&nuDiario=3528", "label": "Link", "source": ["EscavadorDOCPF"], "is_deleted": false}}, "descricao": {"value": "Decisão judicial em processo civil", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}}, {"local": {"value": "São Paulo / SP", "label": "Cidade", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "10/07/2023", "label": "Data", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "edicao extra?": {"value": "Não", "label": "Edição Extra?", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "link": {"value": "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx", "label": "Link", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, "ocorrencia": {"value": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses.", "label": "Texto", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, {"local": {"value": "Rio de Janeiro / RJ", "label": "Cidade", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "05/08/2023", "label": "Data", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "edicao extra?": {"value": "<PERSON>m", "label": "Edição Extra?", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "link": {"value": "https://doweb.rio.rj.gov.br/", "label": "Link", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, "ocorrencia": {"value": "EXTRATO DE CONTRATO Nº 123/2023. Partes: Município do Rio de Janeiro e João da Silva ME. Objeto: Prestação de serviços de manutenção predial. Valor: R$ 150.000,00. Prazo: 12 meses.", "label": "Texto", "source": ["QueridoDiarioCPF"], "is_deleted": false}}]}, {"title": "Diários Oficiais - NOME", "subtitle": "Dados consultados na API SNAP através do NOME da empresa.", "source": ["EscavadorDONome"], "data_count": 2, "data": [{"local": {"value": "Minister<PERSON>o", "label": "Local", "source": ["EscavadorDONome"], "is_deleted": false}, "detalhes": {"data": {"value": "2010-03-03", "label": "Data", "source": ["EscavadorDONome"], "is_deleted": false}, "dados adicionais": {"value": "Diario Oficial do Estado do Rio de Janeiro", "label": "<PERSON><PERSON>", "source": ["EscavadorDONome"], "is_deleted": false}, "link": {"value": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1", "label": "Link", "source": ["EscavadorDONome"], "is_deleted": false}}, "descricao": {"value": "...fevereiro de 2010, ELISETE...", "label": "Descrição", "source": ["EscavadorDONome"], "is_deleted": false}}, {"local": {"value": "Fortaleza / CE", "label": "Cidade", "source": ["QueridoDiarioNome"], "is_deleted": false}, "detalhes": {"data": {"value": "2023-02-15", "label": "Data", "source": ["QueridoDiarioNome"], "is_deleted": false}, "edicao extra?": {"value": "<PERSON>m", "label": "Edição Extra?", "source": ["QueridoDiarioNome"], "is_deleted": false}, "link": {"value": "https://diariooficial.fortaleza.ce.gov.br/", "label": "Link", "source": ["QueridoDiarioNome"], "is_deleted": false}}, "descricao": {"value": "EXTRATO DO CONTRATO Nº 025/2023 - CONTRATANTE: Município de Fortaleza, através da Secretaria Municipal de Educação. CONTRATADA: Maria Silva Consultoria Educacional LTDA, CNPJ 12.345.678/0001-90. OBJETO: Prestação de serviços de consultoria pedagógica. VALOR: R$ 85.000,00. VIGÊNCIA: 12 meses.", "label": "Texto", "source": ["QueridoDiarioNome"], "is_deleted": false}}]}, {"title": "Recursos Públicos Recebidos", "subtitle": "Dados consultados em portais de Transparência estaduais e federal.", "source": ["PortalDaTransparenciaDeMinasGeraisCNPJ", "TransparenciaDFCNPJ", "PortalDaTransparenciaDoAmazonasCNPJ", "TransparenciaPRCNPJ", "TransparenciaManausCNPJ"], "data_count": 7, "data": [{"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "detalhes": {"valor pago": {"value": "R$ 15.750,00", "label": "<PERSON><PERSON> (R$)", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor empenhado": {"value": "R$ 16.000,00", "label": "<PERSON><PERSON>", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor liquidado": {"value": "R$ 15.750,00", "label": "Valor Liquidado", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "detalhes": {"valor pago": {"value": "R$ 30.950,00", "label": "<PERSON><PERSON> (R$)", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor empenhado": {"value": "R$ 28.000,00", "label": "<PERSON><PERSON>", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor liquidado": {"value": "R$ 30.950,00", "label": "Valor Liquidado", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "detalhes": {"nome do orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "codigo do orgao": {"value": "00123", "label": "Código do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "pagamento execicio anterior": {"value": "R$ 0,00", "label": "Pagamento Execício <PERSON>", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero de ordem bancaria": {"value": "2023OB00123", "label": "Nº Ordem Bancária", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero da nota de lancamento": {"value": "2023NL00456", "label": "Nº Nota de Lançamento", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero do empenho": {"value": "2023EM00789", "label": "Nº Empenho", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "fonte do recurso": {"value": "Re<PERSON>rs<PERSON>", "label": "Fonte do Recurso", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "classificacao": {"value": "Educação", "label": "Classificação", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado da Saúde", "label": "Nome do Orgão", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "detalhes": {"nome do orgao": {"value": "Secretaria de Estado da Saúde", "label": "Nome do Orgão", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "data": {"value": "10/06/2023", "label": "Data Despesa", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "unidade gestora": {"value": "Fundo Estadual de Saúde", "label": "Unidade Gestora", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "status pagamento": {"value": "Pago", "label": "Status Pagamento", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "pagamento": {"value": "Integral", "label": "Pagamento", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "valor": {"value": "R$ 7.800,00", "label": "Valor", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "numero de ordem bancaria": {"value": "2023OB00456", "label": "Nº Ordem Bancária", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "historico": {"value": "Prestação de serviços de consultoria", "label": "Hist<PERSON><PERSON><PERSON>", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria Municipal de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "detalhes": {"numero": {"value": "2023NE00321", "label": "Número", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "favorecido": {"value": "<PERSON>", "label": "Favorecido", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "unidade gestora": {"value": "Secretaria Municipal de Educação", "label": "Unidade Gestora", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "data": {"value": "05/04/2023", "label": "Data", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "valor empenhado": {"value": "R$ 22.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "acrescimo": {"value": "R$ 0,00", "label": "Acréscimo (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "anulado": {"value": "R$ 0,00", "label": "Anulado (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "pago": {"value": "R$ 22.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "liquidado": {"value": "R$ 22.500,00", "label": "<PERSON>or <PERSON>ado (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "detalhes": {"valor": {"value": "R$ 18.900,00", "label": "Valor (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "data": {"value": "12/07/2023", "label": "Data Despesa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor empenhado": {"value": "R$ 19.000,00", "label": "<PERSON><PERSON>", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor liquidado": {"value": "R$ 18.900,00", "label": "Valor Liquidado", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "detalhes": {"unidade gestora": {"value": "Secretaria de Estado de Economia", "label": "Unidade Gestora", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "ano_mes": {"value": "2023/07", "label": "<PERSON><PERSON>/<PERSON>s", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "tipo de despesa": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tipo de Despesa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "id": {"value": "DF2023-0789", "label": "ID", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "esfera": {"value": "Fiscal", "label": "Esfera", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "programa de trabalho": {"value": "04.122.8203.8517.0051", "label": "Programa de Trabalho", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "funcao": {"value": "Administração", "label": "Função", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "subfuncao": {"value": "Administração Geral", "label": "Sub-Função", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "programa": {"value": "Gestão Pública", "label": "Programa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "categoria economica": {"value": "Despesas Correntes", "label": "Categoria Econômica", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "grupo de natureza da despesa": {"value": "Outras Despesas Correntes", "label": "Grupo de Despesas", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "modalidade de aplicacao": {"value": "Aplicação Direta", "label": "Modalidade de Aplicação", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "elemento": {"value": "Diárias - Civil", "label": "Elemento", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "subelemento": {"value": "Diárias no País", "label": "Sub-Elemento", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "fonte de recurso": {"value": "Re<PERSON>rs<PERSON>", "label": "Fonte do Recurso", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "empenhado": {"value": "R$ 3.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "liquidado": {"value": "R$ 3.500,00", "label": "<PERSON>or <PERSON>ado (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago ex": {"value": "R$ 0,00", "label": "Pago Ex (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago rpp": {"value": "R$ 0,00", "label": "Pago RPP (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago rpnp": {"value": "R$ 0,00", "label": "Pago RPNP (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago ret": {"value": "R$ 0,00", "label": "Pago RET (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor": {"value": "R$ 3.500,00", "label": "Valor (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}}}]}, {"title": "Doações Enviadas Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEDoacoes"], "data_count": 2, "data": [{"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}, "ano": {"value": "2022", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "cargo eleitoral": {"value": "Vereador", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "unidade eleitoral": {"value": "São Paulo / SP", "label": "Unidade Eleitoral", "source": ["TSEDoacoes"], "is_deleted": false}, "número do candidato": {"value": "12345", "label": "Número", "source": ["TSEDoacoes"], "is_deleted": false}, "partido eleitoral": {"value": "Partido ABC", "label": "Partido", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 1.000,00", "data": "10/05/2022", "recibo": "123456789", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}, {"value": {"valor": "R$ 500,00", "data": "15/06/2022", "recibo": "987654321", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}, "ano": {"value": "2022", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "cargo eleitoral": {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "unidade eleitoral": {"value": "Rio de Janeiro / RJ", "label": "Unidade Eleitoral", "source": ["TSEDoacoes"], "is_deleted": false}, "número do candidato": {"value": "54321", "label": "Número", "source": ["TSEDoacoes"], "is_deleted": false}, "partido eleitoral": {"value": "Partido XYZ", "label": "Partido", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 2.000,00", "data": "20/07/2022", "recibo": "246813579", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}]}, {"title": "Doações Recebidas Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEDoacoes"], "data_count": 4, "data": [{"razao_social": {"value": "Empresa ABC Ltda", "label": "Razão Social", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["TSEDoacoes"], "is_deleted": false}, "cnae": {"value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "label": "CNAE", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 5.000,00", "data": "15/08/2022", "documento": "DOC123456", "recibo": "REC987654", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos de pessoa jurídica", "natureza estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}, {"value": {"valor": "R$ 3.000,00", "data": "20/09/2022", "documento": "DOC654321", "recibo": "REC123456", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos de pessoa jurídica", "natureza estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"razao_social": {"value": "XYZ Comércio e Serviços S.A.", "label": "Razão Social", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["TSEDoacoes"], "is_deleted": false}, "cnae": {"value": "4751-2/01 - Comércio varejista especializado de equipamentos e suprimentos de informática", "label": "CNAE", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 10.000,00", "data": "05/10/2022", "documento": "DOC789012", "recibo": "REC345678", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos de pessoa jurídica", "natureza estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 1.500,00", "data": "12/07/2022", "documento": "DOC456789", "recibo": "REC567890", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": "R$ 800,00", "data": "25/08/2022", "documento": "DOC234567", "recibo": "REC678901", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "Tipo": "Financeira"}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}]}, {"title": "Fornecimentos Enviados Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEFornecimento"], "data_count": 1, "data": [{"razao_social": {"value": "Empresa XYZ Ltda", "label": "Razão Social", "source": ["TSEFornecimento"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["TSEFornecimento"], "is_deleted": false}, "cnae": {"value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "label": "CNAE", "source": ["TSEFornecimento"], "is_deleted": false}}, "fornecimentos": [{"value": {"valor": {"value": "R$ 3.500,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "10/05/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC123456", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC987654", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de material gráfico para campanha", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 1.200,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "15/06/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC654321", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC123456", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de serviços de consultoria", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}]}]}, {"title": "Fornecimentos Recebidos Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEFornecimento"], "data_count": 1, "data": [{"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEFornecimento"], "is_deleted": false}, "ano da eleição": {"value": "2022", "label": "Ano Da Eleição", "source": ["TSEFornecimento"], "is_deleted": false}, "cargo eleitoral": {"value": "Vereador", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "unidade eleitoral": {"value": "São Paulo / SP", "label": "Unidade Eleitoral", "source": ["TSEFornecimento"], "is_deleted": false}, "número do candidato": {"value": "12345", "label": "Número", "source": ["TSEFornecimento"], "is_deleted": false}, "partido eleitoral": {"value": "Partido ABC", "label": "Partido", "source": ["TSEFornecimento"], "is_deleted": false}}, "fornecimentos": [{"value": {"valor": {"value": "R$ 3.000,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "10/05/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC123456", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC987654", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de material gráfico para campanha", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 1.500,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "15/06/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC654321", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC123456", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de serviços de consultoria", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}]}]}]}}}}