from sqlalchemy import Column, ForeignKey, Text, DateTime, Integer
from sqlalchemy.dialects.postgresql import UUID, JSON, JSONB


from models.base import Base
from sqlalchemy.sql import func


class UserReports(Base):
    __tablename__ = "user_reports"
    __table_args__ = {"schema": "public"} 

    # Columns
    user_id = Column(
        UUID,
        ForeignKey("public.users.user_id", ondelete="CASCADE"),
        primary_key=True,
        index=True
    )
    user_reports_id = Column(
        UUID,
        primary_key=True,
        index=True,
        default=func.gen_random_uuid()
    )
    data = Column(JSONB, nullable=True)
    created_at = Column(DateTime(timezone=True), nullable=True)
    modified_at = Column(DateTime(timezone=True), nullable=True)
    report_name = Column(JSONB, nullable=True)
    report_status = Column(JSONB, nullable=True)
    report_type = Column(Text, nullable=True) # maybe change to Enum
    subject_name = Column(JSONB, nullable=True)
    subject_mother_name = Column(JSONB, nullable=True)
    subject_age = Column(Integer, nullable=True)
    subject_sex = Column(JSONB, nullable=True)
    report_search_args = Column(JSONB, nullable=True)
    omitted_notes = Column(JSONB, nullable=True)

    def __repr__(self):
        return (
            f"<UserReports(user_id={self.user_id}, user_reports_id={self.user_reports_id}, "
            f"data={self.data}, "
            f"created_at={self.created_at}, modified_at={self.modified_at}, "
            f"report_name={self.report_name}, report_status={self.report_status}, "
            f"report_type={self.report_type}, subject_name={self.subject_name}, "
            f"subject_mother_name={self.subject_mother_name}, subject_age={self.subject_age}, "
            f"subject_sex={self.subject_sex}, report_search_args={self.report_search_args}, "
            f"omitted_notes={self.omitted_notes})"

        )