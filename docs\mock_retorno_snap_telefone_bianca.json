{"creationDate": "Wed, 07 Aug 2024 18:28:08 GMT", "creditsUsed": 20, "data": {"telefone": [{"IRBIS": [{"alias": [{"alias": "Aversa Mp Csi Cibernetica", "country code": "BR", "origin": "getcontact", "phone number": "5521999891661"}, {"alias": "<PERSON> Mprj", "origin": "tc"}, {"alias": "<PERSON>", "origin": "drupe"}, {"origin": "eyecon"}, {"origin": "hiya"}, {"alias": "<PERSON><PERSON><PERSON> a<PERSON>a", "origin": "callapp"}], "endpoint": "IRBIS", "skype": [{}], "telegram": [{"nome": "JOAO AVERSA", "nome no perfil": "<PERSON>ao<PERSON><PERSON><PERSON>"}], "viber": [{}], "whatsapp": [{"whatsapp": "<PERSON>m"}]}], "IRBISLuna": [{"endpoint": "IRBIS", "pessoa": [{"cpf": "10475224701", "data nascimento": "21/10/1984", "descricao": "contact_details", "first names": "BIANCA", "full name": "BIANCA PARREIRA CABRAL-LOPES", "idioma": "pt", "location": [{"cep ou zipcode": "21910087", "cidade": "Rio de Janeiro", "complemento": "C", "estado ou regiao": "RJ", "logradouro": "R DR MANUEL MARREIROS", "numero": "2857", "pais": "BR"}, {"cep ou zipcode": "21911170", "cidade": "Rio de Janeiro", "estado ou regiao": "RJ", "logradouro": "R DO ROMANCISTA", "numero": "136", "pais": "BR"}], "pessoa": [{"data nascimento": "06/02/1992", "full name": "<PERSON><PERSON>", "label default key": "family Niece", "phonenumber": [{"area code": "47", "country code": "55", "phone number": "5547988208342"}]}, {"data nascimento": "21/11/1969", "full name": "<PERSON><PERSON>", "label default key": "family Brother", "phonenumber": [{"area code": "47", "country code": "55", "phone number": "5547988118802"}]}, {"data nascimento": "06/07/1988", "full name": "<PERSON><PERSON><PERSON>", "label default key": "family Brother", "phonenumber": [{"area code": "92", "country code": "55", "phone number": "5592992957554"}]}, {"data nascimento": "13/12/1945", "full name": "<PERSON>", "label default key": "family Mother", "phonenumber": [{"area code": "51", "country code": "55", "phone number": "5551984703292"}]}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521987511944"}, {"area code": "21", "country code": "55", "phone number": "5521999891661"}, {"area code": "21", "country code": "55", "phone number": "5521973931057"}, {"area code": "21", "country code": "55", "phone number": "5521996761661"}, {"area code": "21", "country code": "55", "phone number": "552133960539"}, {"area code": "51", "country code": "55", "phone number": "555135624382"}, {"area code": "21", "country code": "55", "phone number": "552135461661"}, {"area code": "21", "country code": "55", "phone number": "552139232200"}], "sexo": "Feminino", "surname": "PARREIRA CABRAL-LOPES"}, {"data nascimento": "26/12/1984", "descricao": "contact_details", "first names": "JOAO", "full name": "JOAO AVERSA", "full name1": "JOAO BERNARDO DEDIT", "location": [{"pais": "BR"}, {"pais": "BR"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521999891661"}, {"area code": "21", "country code": "55", "phone number": "5521985592439"}, {"area code": "21", "country code": "55", "phone number": "5521999891661"}], "surname": "AVERSA"}, {"descricao": "personal_profiles", "dominio url": "http://www.facebook.com/people/_/100001296267978", "facebook": [{"id": "100001296267978"}, {"link": "http://www.facebook.com/joao.aversa.3", "nome de usuario": "joao.aversa.3"}], "first names": "JOAO", "full name": "JOAO AVERSA", "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "AVERSA"}, {"alias": [{"alias": "jo%2525c3%2525a3o-aversa-29234320"}, {"alias": "jo%c3%a3o-aversa-29234320"}], "descricao": "professional_and_business", "dominio url": "https://www.linkedin.com/in/jo%C3%A3o-aversa-29234320", "empresa": [{"razao social": "Ministerio Publico do Estado do Rio de Janeiro", "vinculo": [{"cargo": "Perito Computacional", "data inicio": "01/06/2006", "rotulo": "<PERSON><PERSON><PERSON>"}]}, {"razao social": "Spread", "vinculo": [{"cargo": "Analista de Suporte Sr", "data inicio": "01/06/2006", "data termino": "01/12/2009", "rotulo": "<PERSON><PERSON><PERSON>"}]}, {"razao social": "SENAC-RIO", "vinculo": [{"formacao educacional": "Redes de Computadores", "rotulo": "Vinculo Educacional"}]}, {"razao social": "Universidade Federal do Rio de Janeiro", "vinculo": [{"data inicio": "01/01/2003", "data termino": "31/12/2007", "formacao educacional": "<PERSON><PERSON><PERSON>", "rotulo": "Vinculo Educacional"}]}], "first names": "JOAO", "full name": "JOAO AVERSA", "linkedin": [{"id": "20/343/292"}, {"id": "72498638"}, {"id": "29234320"}], "location": [{"cidade": "Rio de Janeiro", "estado ou regiao": "RJ", "pais": "BR"}], "surname": "AVERSA", "url": [{"dominio": "mp.rj.gov.br", "url": "http://www.mp.rj.gov.br/portal/page/portal/Internet/Imprensa/Em_Destaque/Noticia?caid=293&iditem=8308973"}]}, {"descricao": "contact_details", "descricao1": "personal_profiles", "dominio url": "https://plus.google.com/106701897403823646843/about", "first names": "JOAO", "full name": "JOAO BERNARDO AVERSA", "imagem": [{"url": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720"}], "location": [{"pais": "BR"}], "perfil": [{"id": "106701897403823646843"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521996761661"}], "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "BERNARDO AVERSA"}, {"alias": [{"alias": "jbaversa"}], "descricao": "personal_profiles", "dominio url": "https://twitter.com/i/user/86335240", "first names": "JOAO", "full name": "JOAO BERNARDO AVERSA", "idioma": "en", "imagem": [{"url": "https://pbs.twimg.com/profile_images/499046154/jobiaxa3.jpg"}], "perfil": [{"id": "86335240"}], "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "BERNARDO AVERSA"}, {"alias": [{"alias": "joao.aversa.3"}, {"alias": "jo%2525c3%2525a3o-aversa-29234320"}, {"alias": "jo%c3%a3o-aversa-29234320"}], "cpf": "10748896732", "data nascimento": "26/12/1984", "descricao": "contact_details", "empresa": [{"razao social": "Spread", "vinculo": [{"cargo": "Analista de Suporte Sr", "data inicio": "01/06/2006", "data termino": "01/12/2009", "rotulo": "<PERSON><PERSON><PERSON>"}]}, {"razao social": "Ministerio Publico do Estado do Rio de Janeiro", "vinculo": [{"cargo": "Perito Computacional", "data inicio": "01/06/2006", "data termino": "15/09/2017", "rotulo": "<PERSON><PERSON><PERSON>"}]}, {"razao social": "Universidade Federal do Rio de Janeiro", "vinculo": [{"data inicio": "01/01/2003", "data termino": "31/12/2007", "formacao educacional": "<PERSON><PERSON><PERSON>", "rotulo": "Vinculo Educacional"}]}, {"razao social": "SENAC-RIO", "vinculo": [{"data inicio": "15/01/2002", "data termino": "15/01/2002", "formacao educacional": "Redes de Computadores", "rotulo": "Vinculo Educacional"}]}], "facebook": [{"id": "100001296267978"}], "first names": "JOAO", "full name": "JOAO BERNARDO DEDIT", "full name1": "JOAO BERNARDO AVERSA", "full name2": "JOAO BERNARDO GUIMARAES", "full name3": "JOAO BERNARDO GUIMARAES-AVERSA", "idioma": "pt_BR", "linkedin": [{"id": "72498638"}, {"id": "20/343/292"}, {"id": "29234320"}], "location": [{"cep ou zipcode": "21911170", "cidade": "Rio de Janeiro", "estado ou regiao": "RJ", "logradouro": "R DO ROMANCISTA", "numero": "136", "pais": "BR"}], "pessoa": [{"full name": "<PERSON><PERSON><PERSON>", "label default key": "family Mother"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521999891661"}, {"area code": "21", "country code": "55", "phone number": "5521996761661"}, {"area code": "21", "country code": "55", "phone number": "5521985592439"}, {"area code": "21", "country code": "55", "phone number": "5521985594761"}, {"area code": "21", "country code": "55", "phone number": "5521985591944"}, {"area code": "21", "country code": "55", "phone number": "5521988130665"}, {"area code": "21", "country code": "55", "phone number": "552122928459"}, {"area code": "21", "country code": "55", "phone number": "552122207846"}, {"area code": "21", "country code": "55", "phone number": "552133660213"}, {"area code": "21", "country code": "55", "phone number": "552124669210"}, {"area code": "82", "country code": "55", "phone number": "558233285668"}], "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "BERNARDO DEDIT"}, {"descricao": "contact_details", "first names": "JOAO", "full name": "JOAO BERNARDO GUIMARAES", "full name1": "JOAO BERNARDO AVERSA", "location": [{"pais": "BR"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "552122207846"}, {"area code": "21", "country code": "55", "phone number": "5521988130665"}], "surname": "BERNARDO GUIMARAES"}, {"cpf": "10748896732", "data nascimento": "26/12/1984", "descricao": "contact_details", "first names": "JOAO", "full name": "JOAO BERNARDO GUIMARAES-AVERSA", "full name1": "JOAO BERNARDO AVERSA", "location": [{"cep ou zipcode": "21911170", "cidade": "Rio de Janeiro", "estado ou regiao": "RJ", "logradouro": "R DO ROMANCISTA", "numero": "136", "pais": "BR"}], "pessoa": [{"full name": "<PERSON><PERSON><PERSON>", "label default key": "family Mother"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521985594761"}, {"area code": "21", "country code": "55", "phone number": "5521985591944"}, {"area code": "21", "country code": "55", "phone number": "5521996761661"}, {"area code": "21", "country code": "55", "phone number": "552133660213"}, {"area code": "21", "country code": "55", "phone number": "552124669210"}, {"area code": "21", "country code": "55", "phone number": "5521985592439"}, {"area code": "82", "country code": "55", "phone number": "558233285668"}], "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "BERNARDO GUIMARAES-AVERSA"}, {"descricao": "contact_details", "dominio url": "http://www.telelistas.net/templates/resultado_busca.aspx?nome=Roberto+Carneiro", "first names": "ROBERTO", "full name": "ROBERTO JOSE CARNEIRO", "location": [{"pais": "BR"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "552136419125"}, {"area code": "21", "country code": "55", "phone number": "552126424078"}], "surname": "JOSE CARNEIRO"}, {"cpf": "00475807715", "data nascimento": "27/04/1920", "descricao": "contact_details", "first names": "ROBERTO", "full name": "ROBERTO JOSE CARNEIRO", "idioma": "pt", "location": [{"cep ou zipcode": "25964040", "cidade": "Teresopolis", "complemento": "CS 2", "estado ou regiao": "RJ", "logradouro": "R DR ALIPIO DE MIRANDA", "numero": "150", "pais": "BR"}, {"cep ou zipcode": "25964340", "cidade": "Teresopolis", "complemento": "APARTAMENTO 201", "estado ou regiao": "RJ", "logradouro": "R.CEL.SILV.LISBOA CUNHA", "numero": "211", "pais": "BR"}, {"cep ou zipcode": "25964-340", "cidade": "Teresopolis", "estado ou regiao": "RJ", "logradouro": "RUA CORONEL SILVIO LISBOA CUNHA 211 AP201", "pais": "BR"}, {"cep ou zipcode": "25980-250", "cidade": "Teresopolis", "estado ou regiao": "RJ", "logradouro": "Est Santa", "pais": "BR"}, {"cep ou zipcode": "25980-250", "cidade": "Teresopolis", "estado ou regiao": "RJ", "logradouro": "EST SANTA RITA", "pais": "BR"}, {"cep ou zipcode": "25964-340", "cidade": "Teresopolis", "estado ou regiao": "RJ", "logradouro": "Rua Coronel Sil<PERSON> 211", "pais": "BR"}], "pessoa": [{"full name": "<PERSON><PERSON><PERSON>", "label default key": "family Mother"}], "phonenumber": [{"area code": "21", "country code": "55", "phone number": "5521999891661"}, {"area code": "21", "country code": "55", "phone number": "552126424078"}, {"area code": "21", "country code": "55", "phone number": "552136419125"}, {"area code": "21", "country code": "55", "phone number": "552126423478"}], "sexo": "<PERSON><PERSON><PERSON><PERSON>", "surname": "JOSE CARNEIRO"}, {"descricao": "web_pages", "dominio url": "https://cryptome.org/2015/07/ht-email-addresses.htm", "dominio url1": "https://wikileaks.org/hackingteam/emails/emailid/144932", "dominio url2": "http://cm.1-s.es/07-2015/ht-email-addresses.htm", "first names": "NOME", "full name": "NOME NAO ENCONTRADO", "surname": "NAO ENCONTRADO"}]}], "ProvedorDeAplicacacaoDaInternettelefone": [{"endpoint": "ProvedorDeAplicacacaoDaInternet", "internetapplicationprovider": [{"alerta": "Não", "aplicacao": "AmazonPhone", "found": "<PERSON>m", "tipo_alerta": "Não se aplica"}, {"alerta": "Não", "aplicacao": "FacebookPhone", "found": "Não", "tipo_alerta": "Não se aplica"}, {"alerta": "Não", "aplicacao": "TwitterPhone", "found": "Não", "tipo_alerta": "Não se aplica"}]}], "SNAP": [{"endpoint": "SNAP", "pessoa": [{"bookmark": 4, "cpf": "10475224701", "data nascimento": "1984-10-21", "first names": "BIANCA", "full name": "BIANCA PARREIRA CABRAL LOPES", "location": [{"area": "RJ", "bairro": "FREGUESIA ILHA DO GOVERNADOR", "cep ou zipcode": "21911170", "city": "RIO DE JANEIRO", "complemento": "CS", "endereco": "RUA DO ROMANCISTA", "nome": "RUA DO ROMANCISTA"}], "pessoa": [{"full name": "NORMA PERREIRA", "label default key": "parente MAE"}], "phonenumber": [{"phone number": "5521999891661"}], "sexo": "F", "surname": "PARREIRA CABRAL LOPES"}]}], "metadata": {"creationDate": "Wed, 07 Aug 2024 18:28:08 GMT", "lastModified": "Wed, 07 Aug 2024 18:28:08 GMT", "reportId": "Fnu0jKJa5FXB1kJTFC0u", "searchArgs": {"telefone": ["21999891661"]}}}]}, "lastModified": "Wed, 07 Aug 2024 18:28:08 GMT", "omittedNodes": [], "reportName": "Telefone 21999891661", "reportStatus": "Complete", "reportType": "telefone", "requestDate": "Wed, 07 Aug 2024 18:26:32 GMT", "searchArgs": {"telefone": ["21999891661"]}}