import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from services.user_service import (
    get_access_handler,
    get_user_data_info_handler
)
from database.db import get_db


router = APIRouter()


@router.post("/get-user-data_info/{user_id}")
async def get_user_data_info(request: Request, db: AsyncSession = Depends(get_db)):
    logger.info("[get_user_data_info] Handling request to fetch user data info...")
    try:
        return await get_user_data_info_handler(request, db)
    except Exception as e:
        logger.error("[get_user_data_info] Failed to fetch user data info: %s", e)
        raise e


@router.post("/get-access")
async def get_access(request: Request, db: AsyncSession = Depends(get_db)):
    logger.info("[get_access] Handling request to get user access...")
    try:
        return await get_access_handler(request, db)
    except Exception as e:
        logger.error("[get_access] Failed to get user access: %s", e)
        raise e
