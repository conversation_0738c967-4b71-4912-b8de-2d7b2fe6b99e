# REGRAS RELATÓRIO EMAIL

## Seções existentes para o tipo de relatório "EMAIL" e suas respectivas fontes de dados:

1. Dad<PERSON>
     - Fontes de dados: "IRBIS"

2. Imagens
     - <PERSON><PERSON>s de dados: "IRBIS"

3. Telefones
     - Fontes de dados: "IRBIS"

4. Emails
     <PERSON> <PERSON><PERSON>s de dados: "IRBIS"

5. Endereços
     - <PERSON>ontes de dados: "IRBIS"

6. Parentes
     - Fontes de dados: "IRBIS"

7. Empresas Relacionadas
     - Fontes de dados: "IRBIS"

8. Nomes de Usuário
     - Fontes de dados: "IRBIS"

9.  <PERSON><PERSON>s Redes Sociais
      - Fontes de dados: "IRBIS"
  
10. Outras URLs
      - Fontes de dados: "IRBIS"

11. Possíveis Contatos
      - Fontes de dados: "IRBIS"

12. Possíveis Pessoas Relacionadas
      - Fontes de dados: "IRBIS"

12. Possíveis Contas em Sites
      - Fontes de dados: "ProvedorDeAplicacacaoDaInternetemail"

## SEÇÃO: "Dados Pessoais"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.

- Adicionar as propriedades (chave e valor) deste objeto na seção "Dados Pessoais", sem listas e objetos, apenas os valores primitivos. Existem algumas propriedades que não devem ser adicionadas nesse processo. São elas:
  - "bookmark"
  - "first names"
  - "surname"
  - "pessoa" (já que se trata de uma lista)

- No objeto com propriedade **"bookmark": 4**, buscar na lista com chave **"pessoa"** um objeto com a propriedade **"label default key"** com valor que contenha o termo **"mae"**.
  - Adicionar a propriedade **"full name"** deste objeto à seção "Dados Pessoais", porém trocando a chave para **"nome_da_mae"**. Este passo é necessário pois o nome da mãe pode ser determinante para identificar e diferenciar as pessoas de interesse.

- Modificar valores de propriedades específicas:
  - "idade" retorna apenas texto com um número por padrão, então é necessário adicionar "ANOS" ao retorno (ex.: "34" --> "34 ANOS")
  - "sexo" retorna apenas a inicial, sendo necesário atribuir a palavra completa (ex.: "F" --> "Feminino", "M" --> "Masculino")

**Regras para a formatação da seção:**

- Ordenar as propriedades inseridas na seção "Dados Pessoais" colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "full name"
  - "nome_da_mae" (inserido no passo anterior)
  - "idade"
  - "sexo"
  - "nacionalidade" OU "pais do passaporte"
  - "data nascimento" OU "dt_nascimento"
  - "cpf"
  - "identidade"
  - restante das propriedades...

- O objeto que constitui a seção **"Dados Pessoais"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Dados Pessoais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- São objetos com a propriedade primitiva, e com a origem do dado (proveniente do **"endpoint"** da chave principal de onde o dado foi buscado). Neste caso o valor da chave **"endpoint"**  do objeto dentro da lista com chave **"IRBIS"** passa para os objetos que forem provenientes da mesma:

**Formato dos objetos da lista "data" da seção "Dados Pessoais":**

```json
{
    "full name": "ANDREIA FERREIRA DE SOUZA", // Propriedade primitiva
    "source": ["IRBIS"] // origem
},
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cpf": "110.452.316-73",
                    "data de admissao": "18/12/2019",
                    "data nascimento": "02/12/1990",
                    "escolaridade": "EDUCACAO SUPERIOR COMPLETA",
                    "first names": "ANDREIA",
                    "full name": "ANDREIA FERREIRA DE SOUZA",
                    "idade": "34",
                    "info restricao": "* NADA CONSTA *",
                    "pais do passaporte": "BRASIL",
                    "pessoa": [
                        {
                            "full name": "SANDRA FERREIRA DE CARVALHO",
                            "label default key": "parente MAE"
                        },
                        {
                            "full name": "JESUS ALVES DE SOUZA",
                            "label default key": "parente PAI"
                        }
                    ],
                    "pis/pasep": "13055665111",
                    "procon": "(NAO TEM)",
                    "renda estimada": "2917",
                    "sexo": "F",
                    "signo": "SAGITARIO",
                    "status receita": "REGULAR",
                    "surname": "FERREIRA DE SOUZA"
                }
                //outros objetos
            ]
            //outras propriedades
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Dados Pessoais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "full name": "ANDREIA FERREIRA DE SOUZA",
                "source": ["IRBIS"]
            },
            {
                "nome_da_mae": "SANDRA FERREIRA DE CARVALHO",
                "source": ["IRBIS"]
            },
            {
                "idade": "34 ANOS",
                "source": ["IRBIS"]
            },
            {
                "sexo": "Feminino",
                "source": ["IRBIS"]
            },
            {
                "pais do passaporte": "BRASIL",
                "source": ["IRBIS"]
            },
            {
                "data nascimento": "02/12/1990",
                "source": ["IRBIS"]
            },
            {
                "cpf": "110.452.316-73",
                "source": ["IRBIS"]
            },
            //outros objetos da seção
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Imagens"

- No resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"imagem"**  (IRBISLuna[0].pessoa[n].imagem).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Imagens"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Imagens"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cpf": "110.452.316-73",
                    "url": "https://facebook.com/ihsauhdd.jpg"
                }
            ]
            //outros resultados}
        }
    ],

}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Imagens",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "Imagem 1": [
                            {
                                "url": "https://facebook.com/ihsauhdd.jpg",
                                "source": ["IRBIS"],
                            }
                        ]
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Telefones":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"pessoa"** -> lista com chave **"phonenumber"** (IRBISLuna[0].pessoa[n].phonenumber).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Telefones"**.
- Trocar o valor de todas as chaves **"phone number"** para **"Email"** mais o index + 1.
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Telefones"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Telefones"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- São objetos com a propriedade primitiva, com a origem do dado (proveniente do **"endpoint"** da chave principal de onde o dado foi buscado).

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "phonenumber":
            [
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521987511944"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521999891661"
                }
            ]
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Telefones",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "Telefone 1": "5521987511944",
                "source": ["IRBIS"]
            },
            {
                "Telefone 2": "5521999891661",
                "source": ["IRBIS"]
            }
            //outros objetos da seção
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Emails":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"pessoa"** -> lista com chave **"emailaddress"** (IRBIS[0].pessoa[0].emailaddress).

- Adicionar todos os objetos destas listas no **"data"** da seção **"Emails"**.
- Trocar o valor de todas as chaves **"email address"** ou **"e-mail"** para **"Email"** mais o index + 1.
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Emails"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Emails"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- São objetos com a propriedade primitiva, com a origem do dado (proveniente do **"endpoint"** da chave principal de onde o dado foi buscado).

**Formato dos objetos da lista "data" da seção "Emails":**

```json
{
    "Email 1": "<EMAIL>", // Propriedade primitiva
    "source": ["IRBIS"] // origem
}
```
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "emailaddress":[
                {
                    "email address": "<EMAIL>",
                }            
            ]
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Emails",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "Email 1": "<EMAIL>",
                "source": ["IRBIS"]
            }
            //outros objetos da seção
        ]
    }
    //outras seções
]
```
</div> 
</div>


## SEÇÃO: "Endereços"

**Regras para processamento de dados:**
  
- No resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"endereco"** (IRBISLuna[0].pessoa[n].endereco).   

- Adicionar todos os objetos provenientes dessas listas no **"data"** da seção **"Endereços"**.

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "label default key"


**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Endereços"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Endereços"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- Cria-se uma lista com chave **"list"**, contendo cada objeto proveniente de **"location"** ou **"endereco"**.
- Utiliza-se a string **"endereco"** mais o valor do **index + 1** para criar a chave da lista de propriedades. Esta lista é importante para o código do frontend reconhecer que se trata de um agrupamento de informações pertencentes a uma lista, e não apenas dados primitivos avulsos.
- Herda-se o valor da chave **"endpoint"**  do objeto dentro que deu origem ao dado para a chave **"source"** (por ex.: "IRBIS", "SintegraMA", etc).

- **Formato dos objetos da lista "data" da seção "Endereços":**

  ```json
    {
        "source": ["IRBIS"],
        "list": [
            {
                "Endereco 1": [
                    {
                       "logradouro": "R TEOBALDO JOAQUIM DOS SANTOS",
                       "source": ["IRBIS"]
                    },
                    {
                        "complemento": "C",
                        "source": ["IRBIS"]
                    },
                    {
                       "city": "CONTAGEM",
                       "source": ["IRBIS"]
                    },
                    {
                       "cep ou zipcode": "32041100",
                       "source": ["IRBIS"]
                    },
                    {
                       "bairro": "TRES BARRAS",
                       "source": ["IRBIS"]
                    },
                    {
                       "area": "MG",
                       "source": ["IRBIS"]
                    },
                    //outras propriedades
                ],
            }
            // outros objetos da seção, exemplo "Endereco 2"
        ]
    }
´´´

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
         {
            "endpoint": "IRBIS",
            "location": [
                {
                    "area": "MG",
                    "bairro": "TRES BARRAS",
                    "cep ou zipcode": "32041100",
                    "city": "CONTAGEM",
                    "complemento": "C",
                    "label default key": "5531991701966",
                    "logradouro": "R TEOBALDO JOAQUIM DOS SANTOS"
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Endereços",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"], // listar todas as chaves usadas como fonte
        "data": [
           {
                "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "Endereco 1": [
                            {
                                "logradouro": "R TEOBALDO JOAQUIM DOS SANTOS",
                                "source": ["IRBIS"]
                            },
                            {
                                "complemento": "C",
                                "source": ["IRBIS"]
                            },
                            {
                                "city": "CONTAGEM",
                                "source": ["IRBIS"]
                            },
                            {
                                "cep ou zipcode": "32041100",
                                "source": ["IRBIS"]
                            },
                            {
                                "bairro": "TRES BARRAS",
                                "source": ["IRBIS"]
                            },
                            {
                                "area": "MG",
                                "source": ["IRBIS"]
                            },
                            //outras propriedades
                        ],
                    }
                    // outros objetos, por exemplo "Endereco 2"
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Parentes"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"**, buscar a lista com a chave **"pessoa"** (SNAP[0].pessoa).

- Buscar no resultado da chave **"SNAP"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.
  - Buscar a lista com chave **"pessoa"** deste objeto (SNAP[0].pessoa["pessoa com bookmark 4"].pessoa).

- Adicionar os objetos provenientes dessas listas no **"data"** da seção **"Parentes"** SE o objeto se enquadrar em alguma das regras abaixo:
  - A propriedade **"label default key"** OU **"grau de parentesco"** CONTEM algum dos seguintes termos: ["MAE", "IRMA", "PAI", "TIO", "AVOS", "FILHO", "CONJUGE"]
    - Alterar em cada objeto que cair nessa regra, a propriedade **"label default key"** OU **"grau de parentesco"** para **"parentesco"**  

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "bookmark"
   - "surname"
   - "first names"
   - "credilink label"
   - "pessoa"
   - "location"
   - "phonenumber"

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Parentes"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Parentes"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- Cria-se uma lista com chave **"list"**, contendo cada objeto proveniente das fontes de dados.
- Utiliza-se a string **"parente"** mais o valor do **index + 1** para criar a chave da lista de propriedades. Esta lista é importante para o código do frontend reconhecer que se trata de um agrupamento de informações pertencentes a uma lista, e não apenas dados primitivos avulsos.
- Herda-se o valor da chave **"endpoint"**  do objeto dentro que deu origem ao dado para a chave **"source"** (por ex.: "SNAP", "BancoNacionalDeMonitoramentoDePrisoes", etc).

- **Formato dos objetos da lista "data" da seção "Parentes":**

  ```json
    {
        "source": ["SNAP"],
        "list": [
            {
                "Parente 1": [
                    {
                        "full name": "ADRIANA APARECIDA ROSA BIANCHIN", //full name sempre vem primeiro
                        "source": ["SNAP"]
                    },
                    {
                        "parentesco": "parente IRMAO(A)",
                        "source": ["SNAP"]
                    },
                    {
                        "cpf": "32113815893",
                        "source": ["SNAP"]
                    },
                    {
                        "data nascimento": "06/02/1983",
                        "source": ["SNAP"]
                    }
                    {
                        "sexo": "F",
                        "source": ["SNAP"]
                    },
                    {
                        "sexo": "F",
                        "source": ["SNAP"]
                    },
                    {
                        "titulo de eleitor": "307837410167",
                        "source": ["SNAP"]
                    }
                    //outras propriedades
                ],
            }
            // outros objetos da seção, exemplo "Parente 2"
        ]
    }
´´´

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cbo": "716610",
                    "cpf": "942.367.041-53",
                    "ctps": "00070",
                    "data de admissao": "01/11/2017",
                    "data nascimento": "24/12/1976",
                    "descricao cbo": "PINTOR DE OBRAS",
                    "escolaridade": "ENSINO MEDIO COMPLETO",
                    "first names": "JUANIL",
                    "full name": "JUANIL MIRANDA LIMA",
                    "grau de instrucao": "6º AO 9º ANO DO ENSINO FUNDAMENTAL INCOMPLETO",
                    "idade": "47",
                    "info restricao": "* NADA CONSTA *",
                    "nacionalidade": "BRASILEIRO",
                    "pais do passaporte": "BRASIL",
                    "pessoa": [
                        {
                            "full name": "MARIA APARECIDA DE MIRANDA ROSA", 
                            "label default key": "parente MAE" // pessoa parente
                        }
                    ],
                    "pis/pasep": "20019012750",
                    "procon": "(NAO TEM)",
                    "renda estimada": "2089,92",
                    "rg": "923465",
                    "sexo": "M",
                    "signo": "CAPRICORNIO",
                    "status receita": "REGULAR",
                    "surname": "MIRANDA LIMA",
                    "titulo de eleitor": "013947121953",
                    "uf de nascimento": "MS"
                },
                {
                    "cpf": "32113815893",
                    "credilink label": "parente IRMAO(A)",
                    "data nascimento": "06/02/1983",
                    "first names": "ADRIANA",
                    "full name": "ADRIANA APARECIDA ROSA BIANCHIN",
                    "label default key": "parente IRMAO(A)", // pessoa parente
                    "location": [
                        {
                            "area": "SP",
                            "bairro": "PRQ RESIDENCIAL JUNDIAI",
                            "cep ou zipcode": "13212510",
                            "city": "JUNDIAI",
                            "label default key": "11911533317",
                            "logradouro": "R NEUSA FARIA BINI",
                            "numero": "106"
                        }
                    ],
                    "pessoa": [
                        {
                            "full name": "MARIA APARECIDA MIRANDA ROSA",
                            "label default key": "parente MAE"
                        }
                    ],
                    "phonenumber": [
                        {
                            "operadora": "CLARO",
                            "phone number": "+5511911533317"
                        }
                    ],
                    "procon": "(NAO TEM)",
                    "sexo": "F",
                    "surname": "APARECIDA ROSA BIANCHIN",
                    "titulo de eleitor": "307837410167"
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Parentes",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "BancoNacionalDeMonitoramentoDePrisoes"], // listar todas as chaves usadas como fonte
        "data": [
           {
                "source": ["SNAP"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "Parente 1": [
                            {
                                "full name": "MARIA APARECIDA DE MIRANDA ROSA",
                                "source": ["SNAP"]
                            },
                            {
                                "parentesco": "parente MAE",
                                "source": ["SNAP"]
                            }
                        ],
                    },
                    {
                        "Parente 2": [
                            {
                                "full name": "ADRIANA APARECIDA ROSA BIANCHIN", //full name sempre vem primeiro
                                "source": ["SNAP"]
                            },
                            {
                                "parentesco": "parente IRMAO(A)",
                                "source": ["SNAP"]
                            },
                            {
                                "cpf": "32113815893",
                                "source": ["SNAP"]
                            },
                            {
                                "data nascimento": "06/02/1983",
                                "source": ["SNAP"]
                            }
                            {
                                "sexo": "F",
                                "source": ["SNAP"]
                            },
                            {
                                "sexo": "F",
                                "source": ["SNAP"]
                            },
                            {
                                "titulo de eleitor": "307837410167"
                            }
                        ],
                    }
                    // outros objetos, por exemplo "Parente 4"
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Empresas Relacionadas"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"pessoa"** -> lista com chave **"empresa"** (IRBISLuna[0].pessoa[n].empresa).
   - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**

- Adicionar todos os objetos das lista acima no **"data"** da seção **"Empresas Relacionadas"**.
  
**Regras para a formatação da seção:**

- Ordenar as propriedades de cada objeto inserido na seção "Empresas Relacionadas", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "razao social"
  - "rotulo"
  - "cargo"
  - "formacao educacional"
  - "data de inicio"
  - "data_inicio"
  - "data inicio"
  - "data de termino"
  - "data termino"
  - "data_termino"
  - ...restante das propriedades...


- O objeto que constitui a seção **"Empresas Relacionadas"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Empresas Relacionadas"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- Cria-se uma lista com chave **"list"**, contendo cada objeto proveniente das buscas.
- Utiliza-se a string **"sociedade"** mais o valor do **index + 1** para criar a chave da lista de propriedades. Esta lista é importante para o código do frontend reconhecer que se trata de um agrupamento de informações pertencentes a uma lista, e não apenas dados primitivos avulsos.
- Herda-se o valor da chave **"endpoint"**  do objeto que deu origem ao dado para a propriedade **"source"**

**Formato dos objetos da lista "data" da seção "Empresas Relacionadas":**

```json
{
    "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
    "list": [
        {
            "Empresa relacionada 1": [
                {
                    "razao social": "JBG AVERSA TRATAMENTO DE DADOS LTDA",
                    "source": ["IRBIS"],
                },
                {
                    "cnpj": "52643023000118",
                    "source": ["IRBIS"],
                },
                // propriedades restantes
            ]
        }
        // outros objetos, por exemplo exemplo "Empresa relacionada 2"
    ]
}
```
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
   "IRBIS": [
        {
            "endpoint": "SNAP",
            "pessoa": [
               {
                    // outras propriedades
                    "empresa": [ 
                        {
                            "cargosociedade": "49",
                            "cnpj": "52643023000118",
                            "credilink label": "socio",
                            "desccargosociedade": "SOCIO ADMINISTRADOR",
                            "entradasociedade": "24/10/2023",
                            "label default key": "socio",
                            "participacaosociedade": "0",
                            "razao social": "JBG AVERSA TRATAMENTO DE DADOS LTDA"
                        }
                    ]
                }
                //outros objetos
            ]
        }
    ]
    //outros retornos
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Empresas Relacionadas",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"], // listar todas as chaves usadas como fonte
        "data": [
            {
                "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "Empresa relacionada 1": [
                            {
                                "razao social": "JBG AVERSA TRATAMENTO DE DADOS LTDA",
                                "source": ["IRBIS"],
                            },
                            {
                                "cnpj": "52643023000118",
                                "source": ["IRBIS"],
                            },
                            // propriedades restantes
                        ]
                    }
                    // outros objetos, por exemplo exemplo "Empresa relacionada 2"
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Nomes de Usuário":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"pessoa"** -> lista com chave **"alias"** (IRBISLuna[0].pessoa[n].alias).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Nomes de Usuário"**.
- Trocar o valor de todas as chaves **"alias"** para **"nome de usuario"** mais o index + 1.
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Nomes de Usuário"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Nomes de Usuário"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

- São objetos com a propriedade primitiva, com a origem do dado (proveniente do **"endpoint"** da chave principal de onde o dado foi buscado).

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "pessoas": {
                "alias":[
                    {
                        "alias": "Aversa Mp Csi Cibernetica",
                        "country code": "BR",
                        "origin": "getcontact",
                        "phone number": "5521999891661"
                    },          
                ]
            }
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Nomes de Usuário",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "nome de usuario 1": "Aversa Mp Csi Cibernetica",
                "source": ["IRBIS"]
            }
            //outros objetos da seção
        ]
    }
    //outras seções
]
```
</div> 
</div>


## SEÇÃO: "Perfis Redes Sociais":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"skype"** (IRBIS[0].pessoa[n].skype).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"telegram"** (IRBIS[0].pessoa[n].telegram).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"whatsapp"** (IRBIS[0].pessoa[n].whatsapp).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"viber"** (IRBIS[0].pessoa[n].viber).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"perfil"** (IRBIS[0].pessoa[n].perfil).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"facebook"** (IRBISLuna[0].pessoa[n].facebook),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"instagram"** (IRBISLuna[0].pessoa[n].instagram),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"linkedin"** (IRBISLuna[0].pessoa[n].linkedin),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"skype"** (IRBISLuna[0].pessoa[n].skype),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"snapchat"** (IRBISLuna[0].pessoa[n].snapchat),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"telegram"** (IRBISLuna[0].pessoa[n].telegram),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"whatsapp"** (IRBISLuna[0].pessoa[n].whatsapp),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"googleplus"** (IRBISLuna[0].pessoa[n].googleplus),
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"pessoa"** -> lista com a chave **"perfil"** (IRBISLuna[0].pessoa[n].perfil), 
  - somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Perfis Redes Sociais"**.
- Trocar o valor de todas as chaves para que o nome do perfil seja formatado conforme regras específicas (por exemplo, adicionando um prefixo ou índice).

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Perfis Redes Sociais"** e que será utilizado para a construção dinâmica de sua interface precisa conter as seguintes propriedades e valores:
  - **"title":** "Perfis Redes Sociais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS"]
  - **"data":** [...] – esta será a lista com os objetos adicionados à seção, seguindo o formato abaixo.

- São objetos com propriedade primitiva, com a origem do dado proveniente do **"endpoint"** da chave principal.

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
         {
            "endpoint": "IRBIS",
            "telegram": 
                [
                    {
                        "imagem de perfil": "/9j/4AAQSkZJRgABAQEASABIAAD/",
                        "nome": "VICTOR NUNES"
                    }
                ]
            
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Perfis Redes Sociais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"], // listar todas as chaves usadas como fonte
        "data": [
           {
                "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "Perfil 1": [
                            {
                                "imagem de perfil": "/9j/4AAQSkZJRgABAQEASABIAAD/",
                                "source": ["IRBIS"]
                            },
                            //outras propriedades
                        ],
                    }
                    // outros objetos, por exemplo "Endereco 2"
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Outras URLs":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"url"** (IRBIS[0].pessoa[n].url) **com condição**: somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.
- Adicionar todos os objetos desta lista no **"data"** da seção **"Outras URLs"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Outras URLs"** e que será utilizado para a construção dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Outras URLs"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS"]
  - **"data":** [...] (lista dos objetos processados conforme os acessos acima).
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "pessoa": [
                {
                    "bookmark": 4,
                    "url": [
                        {
                            "dominio url": "example.com",
                            "dominio url1": "example1.com",
                            "dominio url2": "example2.com"
                            // ... outras propriedades
                        }
                    ]
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Outras URLs",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "URL": "example.com",
                "source": ["IRBIS"]
            }
            // Outros objetos da seção
        ]
    }
]

```
</div> 
</div>




## SEÇÃO: "Possíveis Contatos":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> dentro da lista com chave **"pessoa"**, buscar a lista com chave **"pessoa"** (IRBIS[0].pessoa[n].pessoa) **com condição**: somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.

- Para os objetos provenientes de **"IRBIS"**:
  - Obter o valor da propriedade **"fullname"** e concatenar se necessário (display: concat:' '), formando o **"Contato"**.
  - Adicionar as propriedades adicionais conforme os seguintes mapeamentos:
    - **"data_nascimento"** → **"Data de Nascimento"**
    - **"sexo"** → **"Sexo"**
    - **"e_mail"** → **"E-mail"**
    - **"numero_tel"** → **"Telefone"**
    - **"pais"** → **"País"**
    - **"regiao"** → **"Estado"**
    - **"cidade"** → **"Cidade"**

- Para os objetos provenientes de **"IRBIS"** (dentro de "pessoa"):
  - Obter o valor da propriedade **"full name"** ou **"fullname"** para formar o **"Contato"**.
  - Adicionar as seguintes propriedades:
    - **"cpf"** → **"CPF"**
    - **"label default key"** → **"Vínculo"**
    - **"nacionalidade"** → **"Nacionalidade"**
    - **"data nascimento"** ou **"data_nascimento"** → **"Data de Nascimento"**
    - **"sexo"** → **"Sexo"**
  - Buscar, dentro do objeto, a lista **"phonenumber"** para obter cada **"phone number"** e adicioná-los como **"Telefones"**.

- Adicionar todos os objetos processados destas listas no **"data"** da seção **"Possíveis Contatos"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contatos"** e que será utilizado para a construção dinâmica da interface precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contatos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS"]
  - **"data":** [...] (lista dos objetos processados conforme os acessos acima).
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "pessoa": [
                {
                    "bookmark": 4,
                    "pessoa": [
                        {
                            "full name": "Maria Souza",
                            "fullname": "Maria Souza",
                            "cpf": "12345678901",
                            "label default key": "amiga",
                            "nacionalidade": "Brasileira",
                            "data nascimento": "02/02/1992",
                            "sexo": "F",
                            "phonenumber": [
                                {
                                    "phone number": "5511988888888"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}

```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contatos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data": [
            {
                "source": ["IRBIS"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "contato 1": [
                            {
                                "fullname": "João Silva",
                                "source": ["IRBIS"],
                            },
                            {
                                "data_nascimento": "01/01/1990",
                                "source": ["IRBIS"],
                            },
                            // propriedades restantes
                        ]
                    }
                    {
                        "contato 3": [
                            {
                                "full name": "Maria Souza",
                                "source": ["IRBIS"],
                            },
                            {
                                 "cpf": "12345678901",
                                "source": ["IRBIS"],
                            },
                            // propriedades restantes
                        ]
                    }
                    // outros objetos como "contato 3"
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Possíveis Pessoas Relacionadas":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"pessoa"** (IRBIS[0].pessoa)  
  - Condição: somente adicionar objetos que atendam a **"bookmark!=4"** ou que tenham **"bookmark"** como indefinido.

- Buscar no resultado da chave **"SNAP"** -> lista com chave **"pessoa"** (SNAP[0].pessoa)  
  - Condições: somente adicionar objetos que satisfaçam todas as seguintes restrições:
    - A propriedade **"label default key"** não contenha nenhum dos seguintes termos:  
      ["parente MAE", "parente IRMA(O)", "parente PAI", "parente TIO(A)", "parente AVOS", "parente FILHO(A)", "parente CONJUGE"].
    - A propriedade **"grau de parentesco"** não contenha "MAE".
    - A propriedade **"bookmark"** não seja igual a **4**.

- Não adicionar as seguintes propriedades dos objetos processados:  
  "bookmark", "surname", "first names", "credilink label", "label default key", "pessoa".

- Adicionar todos os objetos destas listas no **"data"** da seção **"Possíveis Pessoas Relacionadas"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Pessoas Relacionadas"** e que será utilizado para a construção dinâmica da sua interface deverá conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Pessoas Relacionadas"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS", "SNAP"]  // lista com as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] (lista dos objetos processados conforme as regras acima).

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
       {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "cpf": "08936532766",
                    "data nascimento": "04/06/1982",
                    "dt_istalacao": "05/02/2019",
                    "first names": "AILTON",
                    "full name": "AILTON RAMOS DA SILVA JUNIOR",
                    "label default key": "outros_contatos",
                    "location": [
                        {
                            "area": "RJ",
                            "bairro": "FREGUESIA",
                            "cep ou zipcode": "21911430",
                            "city": "RIO DE JANEIRO",
                            "complemento": "CS 2105",
                            "endereco": "RMAGNO MARTINS",
                            "nome": "RMAGNO MARTINS",
                            "numero": "128"
                        }
                    ],
                    "pessoa": [
                        {
                            "full name": "ANTONIA MARIA O RAMOS SILVA",
                            "label default key": "parente MAE"
                        }
                    ],
                    "phonenumber": [
                        {
                            "operadora": "OI",
                            "phone number": "552124669210"
                        }
                    ],

                }
            ]
       }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Pessoas Relacionadas",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS", "SNAP"],
        "data": [
            {
                "source": ["SNAP"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "contato 1": [
                            {
                                "full name": "AILTON RAMOS DA SILVA JUNIOR",
                                "source": ["SNAP"],
                            },
                            {
                                "cpf": "08936532766",
                                "source": ["SNAP"],
                            },
                            {
                                "data nascimento": "04/06/1982",
                                "source": ["SNAP"],
                            },
                            {
                                "dt_istalacao": "05/02/2019",
                                "source": ["SNAP"],
                            },
                            // propriedades restantes
                            {
                                "Endereco 1": [ // location segue a mesma regra dos objetos da seção "Endereços"
                                    {
                                        "endereco": "RMAGNO MARTINS",
                                        "source": ["SNAP"]
                                    },
                                    {
                                        "complemento": "CS 2105",
                                        "source": ["SNAP"]
                                    },
                                    // propriedades restantes
                                ],
                            }
                            {
                                "Telefone 1": [  // phonenumber segue a mesma regra dos objetos da seção "Telefones"
                                    {
                                        "phone number": "552124669210",
                                        "source": ["SNAP"]
                                    }
                                ],
                            }
                        ]
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Possíveis Contas em Sites":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"PAIsemail"** -> lista **"provedor de aplicacoes de internet"** (PAIsemail[0]["provedor de aplicacoes de internet"])
    - Alterar o nome das seguintes chaves:
      - "found" --> "conta encontrada?"
      - "alerta" --> "alvo alertado?"
      - "tipo_alerta" --> "tipo de alerta"
  
  - Adicionar propriedade em cada objeto com o valor do CPF de entrada e com a chave "Entrada CPF". Ex.: {"entrada cpf": "11045231673"}

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contas em Sites"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contas em Sites"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["PAIsemail"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "data": {
        "cpf": {
            "PAIsemail": 
            [
                {
                    "endpoint": "PAIsemail",
                    "PAIs": [
                        {
                            "alerta": "Não",
                            "aplicacao": "casasbahia",
                            "found": "Não",
                            "tipo_alerta": "Não se Aplica"
                        },
                        {
                            "alerta": "Não",
                            "aplicacao": "Estacionamento Digital",
                            "found": "Sim",
                            "tipo_alerta": "Não se Aplica"
                        }
                    ]
                }
            ],
            //outros resultados}
        }
    },
    "searchArgs": {
        "cpf": [
            "10748896732"
        ]
    }
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contas em Sites",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["PAIsemail"],
        "data": [
            {
                "source": ["PAIsemail"], // listar somente as chaves que trouxeram resultado
                "list": [
                    {
                        "site 1": [
                            {
                                "aplicacao": "Estacionamento Digital",
                                "source": ["PAIsemail"],
                            },
                            {
                                "entrada cpf": "10748896732", //usar o valor do CPF do search args
                                "source": ["PAIsemail"],
                            },
                            {
                                "conta encontrada?": "Não",
                                "source": ["PAIsemail"],
                            },
                            {
                                "alvo alertado?": "Não",
                                "source": ["PAIsemail"],
                            },
                            {
                                "tipo de alerta": "Não se Aplica",
                                "source": ["PAIsemail"],
                            },
                        ]
                    },
                    {
                        "site 2": [
                            {
                                "aplicacao": "casasbahia",
                                "source": ["PAIsemail"],
                            },
                            {
                                "entrada cpf": "10748896732", //usar o valor do CPF do search args
                                "source": ["PAIsemail"],
                            },
                            {
                                "conta encontrada?": "Sim",
                                "source": ["PAIsemail"],
                            },
                            {
                                "alvo alertado?": "Não",
                                "source": ["PAIsemail"],
                            },
                            {
                                "tipo de alerta": "Não se Aplica",
                                "source": ["PAIsemail"],
                            },
                        ]
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>