import type { UserData, EncryptedData } from "~/types/global";
import { BFF_REPORT_CLIENT } from "~/services/clients/bff.client";
import { REPORTS_CLIENT } from "~/services/clients/reports.client";

// TODO - remover depois
const DEFAULT_REPORT_CREDITS = {
  cpf: 40,
  cnpj: 20,
  email: 10,
  telefone: 10,
  combinado: 15,
} as const;

interface UserSaltRequest {
  sub: string;
  salt: string;
}

interface UserSaltResponse {
  success: boolean;
  salt: string;
}

/**
 * Fetches the current user data.
 */
export const fetchUserData = async (): Promise<UserData> => {
  try {
    const response = await REPORTS_CLIENT.get<UserData>(`/auth/user`);

    // TODO - remover depois
    const myUserId = "3b9e9488-925a-4fbe-88c7-41ad8e0721ff"
    const adminMockId = "ba3fc7a8-34cd-4d24-9d63-b526b0cadb1a" 
    const modifiedData: UserData = {
      ...response.data,

      // user_id: "3b9e9488-925a-4fbe-88c7-41ad8e0721ff",
      // name: "Andreia Souza",
      // credits: -113,
      // email: "<EMAIL>",
      // last_login: "2025-05-25 18:12:28.943 -0300",
      // salt: "823d9c59c8cff6a594846c403f7ac2fe",
      // saved_reports: 0,
      // image: null,
      // verifier: { "encrypted": "rHPXgkhbi9HG3+NVAR8GZONefTsQf6oa3UVwmpRyiVYh", "iv": "T8IAIxvdS+l+4ZTi" },

      account_type: response.data.user_id === myUserId || response.data.user_id === adminMockId ? "administrador" : "investigador",
      organization_name: response.data.user_id === myUserId || response.data.user_id === adminMockId ? "SNAP" : null,
      organization_logo: response.data.user_id === myUserId || response.data.user_id === adminMockId ? "https://snapforensics.com/wp-content/uploads/2025/01/SNAP-LOGO-1.svg" : "",
      valid_until: "2025-05-25 18:12:28.943 -0300",
      report_types: DEFAULT_REPORT_CREDITS,
    };

    return modifiedData;
    //return response.data;
  } catch (error) {
    console.error("Error fetching user data:", error);
    throw error;
  }
};

export const postUserSalt = async (
  user: UserSaltRequest
): Promise<UserSaltResponse> => {
  try {
    const response = await BFF_REPORT_CLIENT.post(
      `/api/user/${user.sub}/salt`,
      user.salt
    );
    if (!response.data.success) {
      throw new Error("Failed to post user salt");
    }
    const data = response.data;
    return data;
  } catch (error) {
    console.error("Error posting user salt:", error);
    throw error;
  }
};

export const postVerifier = async (verifier: EncryptedData) => {
  try {
    const response = await REPORTS_CLIENT.post<EncryptedData>(`/verifier`, { verifier });
    const data = response.data;
    return data;
  } catch (error) {
    console.error("Error posting verifier:", error);
    throw error;
  }
};

export const getUserSalt = async (sub: string): Promise<UserSaltResponse> => {
  try {
    const response = await BFF_REPORT_CLIENT.get(`/api/user/${sub}/salt`);
    if (!response.data.success) {
      throw new Error("Failed to get user salt");
    }
    const data = response.data;
    return data;
  } catch (error) {
    console.error("Error getting user salt:", error);
    throw error;
  }
};

export const initializeUserSalt = async (sub: string): Promise<string> => {
  try {
    const response = await BFF_REPORT_CLIENT.post(
      `/api/user/${sub}/initialize/salt`
    );

    if (!response.data.success) {
      if (response.status === 409) {
        throw new Error("USER_SALT_ALREADY_EXISTS");
      }
      throw new Error("Failed to initialize user salt");
    }

    const data = await response.data;
    return data.salt;
  } catch (error) {
    console.error("Error initializing user salt:", error);
    throw error;
  }
};
