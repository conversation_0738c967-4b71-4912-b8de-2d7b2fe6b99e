import { useEffect, useState } from "react";
import { <PERSON><PERSON>, I<PERSON> } from "@snap/design-system";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON>aG<PERSON>gle, FaMicrosoft } from "react-icons/fa";
import { ArrowRight, Loader2 } from "lucide-react";
import { redirectToAuthServer } from "~/services/gateways/auth.gateway";
import Cookies from "js-cookie";
import { useLocation, useNavigate } from "react-router";
import icon from "~/assets/reports_logo.svg";
import logo from "~/assets/logo.svg";
import { useQueryClient } from "@tanstack/react-query";
import { useUserActions } from "~/store/userStore";
import { useSecretKeyActions } from "~/store/secretKeyStore";
import { toast } from "sonner";

interface LocationState {
  fromLogout?: boolean;
}

const LoginContainer = () => {
  const location = useLocation();
  const state = location.state as LocationState;
  const queryClient = useQueryClient();
  const { clearUser } = useUserActions();
  const { clearSecretKey } = useSecretKeyActions();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  useEffect(() => {
    clearSecretKey();
    queryClient.clear();
    clearUser();
  }, []);

/*   useEffect(() => {
    state.fromLogout &&
      toast("Logout realizado com sucesso!", { duration: 2000 });
  }, [state]); */

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.warn("Login com email e senha ainda não implementado");
  };

  const handleProviderLogin = async (provider: "microsoft" | "google") => {
    //setIsLoading(true)
    redirectToAuthServer({ provider });
  };

  //[REMOVER] - somente teste
  const handleLogin = async () => {
    Cookies.set("refresh_token", "refresh_teste");
    navigate("/");
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-full max-w-md border-2 bg-background">
        <CardHeader className="p-8">
          <div className="flex items-start gap-4">
            <img
              width={200}
              src={logo}
              alt="SNAP Reports logo"
              className="" //TODO - voltar com sm depois "hidden sm:block"
            />
            <img src={icon} alt="SNAP Reports icon" />
            <h1 className="text-xl leading-none">REPORTS</h1>
          </div>
        </CardHeader>
        <CardContent className="p-8 border-t-2 border-dashed">
          {/* <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? "Entrando..." : "Entrar"}
            </Button>
          </form> */}

          {/* <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Ou com uma conta
              </span>
            </div>
          </div> */}

          <div className="flex flex-col gap-4">
            <Button
              variant="default"
              className="uppercase"
              onClick={() => handleProviderLogin("microsoft")}
              icon={<FaMicrosoft className="mr-2 h-4 w-4" />}
              iconPosition="right"
              data-testid="button-login-microsoft"
            >
              entrar com sua conta microsoft
            </Button>
            <Button
              variant="default"
              className="uppercase"
              onClick={() => handleProviderLogin("google")}
              icon={<FaGoogle className="mr-2 h-4 w-4" />}
              iconPosition="right"
              data-testid="button-login-google"
            >
              entrar com sua conta google
            </Button>
            {/* [REMOVER] - Apenas para demonstração */}
           {/*  <Button
              icon={<ArrowRight />}
              onClick={handleLogin}
              iconPosition="right"
            >
              ENTRAR
            </Button> */}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginContainer;
