import { DadosPessoais } from "../model/DadosPessoais";

export const dadosPessoaisTelefoneMock: DadosPessoais = {
  detalhes: {
    "full name": {
      "value": "BIANCA PARREIRA CABRAL LOPES",
      "label": "Nome Completo",
      "source": ["SNAP"],
      "is_deleted": false
    },
    "nome_da_mae": {
      "value": "NORMA PERREIRA",
      "label": "Nome da Mãe",
      "source": ["SNAP"],
      "is_deleted": false
    },
    "cpf": {
      "value": "104.752.247-01",
      "label": "CPF",
      "source": ["SNAP"],
      "is_deleted": false
    },
    "sexo": {
      "value": "F",
      "label": "Sexo",
      "source": ["SNAP"],
      "is_deleted": false
    },
    "data nascimento": {
      "value": "1984-10-21",
      "label": "Data de Nascimento",
      "source": ["SNAP"],
      "is_deleted": false
    },
  },
};
