import { Navigate, Outlet, useLocation } from "react-router";
import { useEffect } from "react";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { useUserActions } from "~/store/userStore";
import { DialogContainer } from "../DialogContainer";
import { PendingReportSockets } from "../PendingReportSockets";
import { Loading, ModalInstance } from "@snap/design-system";

export default function ProtectedRoute() {
  const location = useLocation();
  const { userQueryUser } = useUserCRUD();
  const { data: userData, isLoading, isError } = userQueryUser;
  const { setUser, setUserSalt } = useUserActions();

  useEffect(() => {
    if (userData) {
      setUser({...userData});
      setUserSalt(userData?.salt || null);
    }
  }, [userData, setUser, setUserSalt]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <Loading size="lg" />
      </div>
    );
  }

  if (isError || !userData) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  return (
    <>
      <ModalInstance />
      <PendingReportSockets />
      <DialogContainer />
      <Outlet />
    </>
  );
}
