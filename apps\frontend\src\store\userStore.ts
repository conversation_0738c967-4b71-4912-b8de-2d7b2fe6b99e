import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { UserData } from "~/types/global";

interface UserActions {
  setUser: (user: UserData) => void;
  setUserSalt: (salt: string | null) => void;
  setIsVerified: (isVerified: boolean) => void;
  clearUser: () => void;
}

interface UserState {
  userData: UserData | null;
  userSalt: string | null;
  isVerified: boolean;
  actions: UserActions;
}

const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set) => ({
        userData: null,
        userSalt: null,
        isVerified: false,
        actions: {
          setUser: (user: UserData) => set({ userData: user }),
          setUserSalt: (salt: string | null) => set({ userSalt: salt }),
          setIsVerified: (isVerified: boolean) => set({ isVerified }),
          clearUser: () => set({ userData: null, userSalt: null }),
        },
      }),
      {
        name: "report-user",
        partialize: (state) => ({
          userData: state.userData,
          userSalt: state.userSalt,
        }),
      }
    )
  )
);

export const useUserData = () => useUserStore((state) => state.userData)
export const useUserSalt = () => useUserStore((state) => state.userSalt)
export const useUserIsVerified = () => useUserStore((state) => state.isVerified)
export const useUserActions = () => useUserStore((state) => state.actions);
