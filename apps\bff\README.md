# BFF (Backend For Frontend) API

## Overview
This is a Node.js/Express backend service that acts as a BFF layer for the SNAP Reports frontend application.

## Tech Stack
- Node.js
- Express
- TypeScript
- JWT for authentication
- <PERSON><PERSON>
- CORS enabled

## Project Structure

```plaintext
apps/bff/
│
├── src/
│   ├── controllers/
│   │   ├── auth.controller.ts
│   │   ├── reports.controller.ts
│   │   └── user.controller.ts
│   │
│   ├── routes/
│   │   ├── auth.routes.ts
│   │   ├── reports.routes.ts
│   │   └── user.routes.ts
│   │
│   ├── middleware/
│   │   └── auth.middleware.ts
│   │
│   └── index.ts
│
├── package.json
├── tsconfig.json
└── README.md
```

## Prerequisites
- Node.js (v16 or higher)
- npm
- TypeScript
- Docker (optional)

## Environment Variables

Create a `.env` file in the root directory:
```env
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://your_frontend
JWT_SECRET=your_jwt_secret
```

## Installation

```bash
# Install dependencies
npm install

```

## Development Mode
```bash
# Run in development mode with hot reload
npm run dev

```

## Production Mode
```bash
# 1. Build the application
npm run build

# 2. Start the production server
npm start

# The build process will:
# - Compile TypeScript to JavaScript
# - Output to /dist directory
# - Optimize for production

```

## Available Scripts

| Command         | Description                           |
|----------------|---------------------------------------|
| `npm install`  | Install project dependencies          |
| `npm run dev`  | Start development server with hot reload |
| `npm run build`| Build the application for production  |
| `npm start`    | Run the production build             |
| `npm run ts-node` | Run TypeScript files directly     |

### Authentication Routes
Base path: `/api/auth`

| Method | Endpoint   | Description                                 | Authentication |
| ------ | ---------- | ------------------------------------------- | -------------- |
| POST   | /microsoft | Authenticate user via Microsoft credentials | No             |
| POST   | /          | General authentication endpoint             | No             |
| POST   | /logout    | Logout user and clear authentication        | Yes            |


### User Routes
Base path: `/api/user`

| Method | Endpoint       | Description              | Authentication |
| ------ | -------------- | ------------------------ | -------------- |
| POST   | /:sub/salt     | Add user's salt          | JWT Required   |
| GET    | /:sub/salt     | Retrieve user's salt     | JWT Required   |
| POST   | /:sub/verifier | Add user's verifier      | JWT Required   |
| GET    | /:sub/verifier | Retrieve user's verifier | JWT Required   |

#### Parameters
- `sub`: User identifier (required in path)
- `salt`: Required in request body for POST /:sub/salt
- `token`: JWT token required in Authorization header or cookies

#### Authentication
All endpoints require a valid JWT token either in:
- Cookie named 'token'

### Reports Routes
Base path: `/api/reports`

| Method | Endpoint | Description                      | Authentication |
| ------ | -------- | -------------------------------- | -------------- |
| GET    | /        | Retrieve list of all reports     | JWT Required   |
| GET    | /:id     | Retrieve a specific report by ID | JWT Required   |

#### Authentication
All endpoints require a valid JWT token in:
- Cookie named 'token'

#### Endpoints Details

**GET /reports**
- Description: Fetches a list of all available reports
- Authentication: Required
- Response:
  ```typescript
  {
    success: boolean,
    data: EncryptedData
  }
