import { RenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { DadosPessoais } from "../../model/DadosPessoais";
import { ValueWithSource } from "../../model/ValueWithSource";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDadosPessoas(sectionTitle: string): RenderStrategy<DadosPessoais> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  // 1) Decide which deta lh e fields count as “deleted”
  const testEntryDeleted = (entry: any): boolean => {
    // all detalhe props must be is_deleted===true
    return entry.detalhes
      ? Object.values(entry.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  };

  // 2) Section is deleted once *every* entry is deleted
  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);


  const formatByKey: Record<
    string,
    (dadosPessoais?: DadosPessoais) => React.ReactElement | null
  > = {
    detalhes: (dadosPessoais) => {
      if (!dadosPessoais?.detalhes) return null;

      // Regra para exibir ou não os campos dessa chave para o modo de exibição atual
      const entries = Object.entries(dadosPessoais.detalhes).filter(([, value]) => {
        const v = value as ValueWithSource<string> & { is_deleted: boolean };
        return shouldInclude(v.is_deleted);
      });

      const onToggleField = (key: string) => {
        // guard optional action
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,

          // a) mutate exactly that detalhe’s flag
          entry => {
            const det = (entry as any).detalhes?.[key];
            if (det) det.is_deleted = !det.is_deleted;
          },

          // b) entry-deleted test we pulled out
          testEntryDeleted,

          // c) section-deleted test we pulled out
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer cols={3} columnFirst>
          {entries.map(([key, value], index) => {
            const valorComFonte = value as ValueWithSource<string>;
            return (
              <CustomGridItem key={`detalhes-${index}-${key}`} cols={1} onToggleField={() => onToggleField(key)}>
                <CustomReadOnlyInputField
                  label={`${(translatePropToLabel(valorComFonte.label)).toUpperCase()}`}
                  value={parseValue(String(valorComFonte.value))}
                  tooltip={renderSourceTooltip(valorComFonte.source)}
                />
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof DadosPessoais>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const render = (dadosPessoais: DadosPessoais): React.ReactElement[] => {
    const keys = Object.keys(dadosPessoais) as Array<keyof DadosPessoais>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Dados Pessoais] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(dadosPessoais))
      .filter((el): el is React.ReactElement => el !== null);
  };

  return {
    validateKeys,
    formatByKey,
    render,
  };
}