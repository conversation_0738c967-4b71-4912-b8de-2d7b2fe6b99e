/* eslint-disable no-unused-vars */
import React from "react";
import { Masonry, type MasonryProps } from "masonic";
import type { MasonryRenderProps } from "~/types/global";

interface MasonicListProps<T>
  extends Omit<MasonryProps<T>, "items" | "render"> {
  items: T[];
  columnWidth?: number;
  columnGutter?: number;
  children: (item: T) => React.ReactNode;
}

function MasonicList<T>({
  items,
  columnWidth = 300,
  columnGutter = 16,
  children,
  ...props
}: MasonicListProps<T>) {
  return (
    <Masonry
      items={items}
      columnWidth={columnWidth}
      columnGutter={columnGutter}
      overscanBy={2}
      render={({ data }: MasonryRenderProps<T>) => children(data)}
      className="masonic-container"
      {...props}
    />
  );
}

export default MasonicList;
