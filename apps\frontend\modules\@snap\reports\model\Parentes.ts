import { ValueWithSource } from "./ValueWithSource";
import { _Endereco } from "./Enderecos";
import { _Telefone } from "./Telefones";

export interface Parente {
    parentesco: ValueWithSource;
    detalhes: Record<
        string,
        ValueWithSource<string>
    >;
    pessoa?: Array<ValueWithSource>;
    telefones?: Array<ValueWithSource<_Telefone>>;
    enderecos?: Array<ValueWithSource<_Endereco>>;
}