import React from "react";
import { Cha<PERSON><PERSON>Box, Separator, Text } from '@snap/design-system';
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip"

export interface TooltipProps {
  title: string;
  side?: "top" | "bottom" | "left" | "right";
  className?: string;
  children: React.ReactNode;
  icon: React.ReactNode;
}

export interface ReportsCustomLabelProps {
  label: string;
  icon?: React.ReactNode;
  colorClass?: string;
  labelTextClass?: string;
  tooltip?: TooltipProps;
}

export const ReportsCustomLabel: React.FC<ReportsCustomLabelProps> = ({
  label,
  icon = undefined,
  colorClass = 'bg-border',
  tooltip = undefined,
  labelTextClass = '',
}) => {
  return (<div className="flex items-center gap-2">
    {icon && icon}
    <div className={`h-3 w-3 ${colorClass}`} />
    <Text variant="label-md" className={`font-mono ${labelTextClass}`}>
      {label}
    </Text>
    {tooltip && (
      <Tooltip>
        <TooltipTrigger className="cursor-help" asChild>
          <span
            className="
              opacity-0
              transition-opacity duration-150
              group-hover:opacity-100
              cursor-help
            "
          >
            {tooltip.icon}
          </span>
        </TooltipTrigger>
        <TooltipContent side={tooltip.side || "right"} className="!p-0 !bg-transparent !shadow-none !border-none">
          <ChamferBox corner="topLeft" className="rounded-md px-1 pt-1 bg-card">
            {/* overlay para escurecer a cor de fundo */}
            <div className="absolute inset-0 bg-black/30 rounded-md pointer-events-none" />
            <div className="relative z-10">
              {tooltip.children}
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    )}
  </div>)
}

