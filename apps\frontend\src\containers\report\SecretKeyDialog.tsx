import { useEffect, useState } from "react";
import { Button, CustomLabel, Input, Text } from "@snap/design-system";
import { useUserData, useUserActions } from "~/store/userStore";
import { useSecretKeyActions, useSecretKey } from "~/store/secretKeyStore";
import { Check, Eye, EyeOff, Info, Save, Undo } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { useModalControl } from "@snap/design-system";
import { useUserCRUD } from "~/hooks/useUserCRUD";
import { verifierPhrase } from "~/helpers/constants";
import { deriveEncryptionKeyArgon2 } from "~/helpers/encryption.helper";
import { encryptionWorker } from "~/services/workers/encryptionWorker.service";
import PasswordStrengthMeter, { validatePasswordNIST } from "~/components/PasswordStrengthMeter";
import { toast } from "sonner";

type UserVerificationState = 'withVerifier' | 'withoutVerifier';

const getUserState = (userData: ReturnType<typeof useUserData>): UserVerificationState => {
  return userData?.verifier ? 'withVerifier' : 'withoutVerifier';
};

type PasswordInputProps = {
  isReadOnly?: boolean;
}

export const PasswordInput = ({ isReadOnly = false }: PasswordInputProps) => {
  const { setSecretKey } = useSecretKeyActions();
  const secretKey = useSecretKey();
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="relative">
      <Input
        id="user-password"
        type={showPassword ? "text" : "password"}
        value={secretKey || ''}
        onChange={(e) => setSecretKey(e.target.value.trim())}
        className="w-full my-2 pr-10 pl-0"
        placeholder="Digitar senha"
        required
        autoFocus
        readOnly={isReadOnly}
        onFocus={(e) => e.target.select()}
        data-testid="input-secret-key"
      />
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-80 hover:opacity-100 cursor-pointer focus:outline-none"
        aria-label={showPassword ? "Esconder senha" : "Mostrar senha"}
      >
        {showPassword ? (
          <EyeOff size={18} />
        ) : (
          <Eye size={18} />
        )}
      </button>
    </div>
  )
}

export function SecretKeyDialogContent() {
  const secretKey = useSecretKey()
  const userData = useUserData();

  const phrases = {
    withVerifier: {
      infoTitle: "Você já possui uma senha associada.",
      infoDescription: "É necessário inserir a senha novamente para visualizar os dados.",
      inputLabel: "Favor inserir a senha para visualizar os dados:"
    },
    withoutVerifier: {
      infoTitle: "Você ainda não possui uma senha associada.",
      infoDescription: "É necessário criar uma senha para associar a sua conta.",
      inputLabel: "Favor inserir uma senha para associar a sua conta:"
    }
  };

  const getPhrase = (key: keyof typeof phrases.withVerifier): string => {
    const userState = getUserState(userData);
    return phrases[userState][key];
  };

  return (
    <div className="">
      <form
        id="secret-key-form"
        onSubmit={(e) => {
          e.preventDefault();
          document.dispatchEvent(new Event("submit-secret-key"));
        }}
      >
        <label htmlFor="user-password" className="flex gap-2 items-center">
          <Tooltip>
            <TooltipTrigger className="cursor-help" asChild>
              <Info size={16} />
            </TooltipTrigger>
            <TooltipContent side="right">
              <div className="flex gap-1 items-center text-accent py-1">
                <Info size={14} />
                <Text>{getPhrase('infoTitle')}</Text>
              </div>
              <Text variant="body-sm">{getPhrase('infoDescription')}</Text>
            </TooltipContent>
          </Tooltip>
          <Text>{getPhrase('inputLabel')}</Text>
        </label>
        <PasswordInput />
        {!userData?.verifier && <PasswordStrengthMeter password={secretKey || ''} />}
      </form>
    </div>
  );
}

export function SecretKeyDialogFooter({ onOpen: onOpenUserSecretKeyDialog }: { onOpen: () => void }) {
  const { open } = useModalControl();
  const { setIsVerified } = useUserActions();
  const userData = useUserData();
  const { addNewVerifierMutation } = useUserCRUD();
  const { onClose: onDialogClose } = useModalControl();
  const secretKey = useSecretKey()
  const hasVerifier = !!userData?.verifier;

  const checkPassword = () => {
    const trimmedPassword = secretKey?.trim();
    const validation = validatePasswordNIST(trimmedPassword || '');
    if (!validation.isValid) {
      toast.error("Senha insegura", {
        description: validation.errorMessage
      });
      return false;
    }

    return true;
  };

  const handleOpenSaveDialog = () => {
    if (!checkPassword()) return;

    open({
      modal: () => ({
        title: "salvar senha".toUpperCase(),
        icon: <Save />,
        content:
          <div className="flex flex-col gap-2">
            <Text variant="body-md">
              Sua senha não ficará armazenada em nosso sistema.
            </Text>
            <Text variant="body-md" className="pb-2">
              Guarde-a em um lugar seguro antes de prosseguir.
            </Text>
            <CustomLabel label="Senha confirmada:" colorClass="bg-accent" />
            <PasswordInput isReadOnly />
          </div>,
        footer: (
          <>
            <Button variant="default" onClick={onOpenUserSecretKeyDialog}>
              <div className="flex items-center gap-2">
                <Text> Alterar</Text>
                <Undo />
              </div>
            </Button>
            <Button variant="default" onClick={handleSubmit}>
              <div className="flex items-center gap-2">
                <Text> Salvar</Text>
                <Save />
              </div>
            </Button>
          </>
        ),
      }),
      config: {
        content: {
          className: "max-w-fit",
        }
      },
    });
  };

  const handleVerifier = async (): Promise<boolean> => {
    console.log("handleVerifier: ", "secret key: ", secretKey, "salt: ", userData?.salt);
    const key = await deriveEncryptionKeyArgon2(secretKey, userData?.salt);
    const rawKeyBuffer = await crypto.subtle.exportKey("raw", key);

    if (!hasVerifier) {
      const ok = await addNewVerifierMutation.mutateAsync(verifierPhrase);
      return ok;
    }

    const decrypted = await encryptionWorker.decrypt(
      userData.verifier,
      rawKeyBuffer
    );

    if (decrypted === verifierPhrase) {
      return true;
    }

    toast.error("Senha incorreta", {
      description: "A senha fornecida não corresponde à senha associada à sua conta."
    });
    return false;
  };


  const handleSubmit = async () => {
    const trimmedPassword = secretKey?.trim();
    if (!trimmedPassword) {
      toast.error("Erro ao validar senha.", {
        description: "A senha não pode estar vazia."
      });
      return;
    };
    console.log("handleSubmit: ", "secret key: ", secretKey, "salt: ", userData?.salt);
    const ok = await handleVerifier();
    if (ok) {
      setIsVerified(ok);
      onDialogClose();
      toast.success("Senha validada com sucesso!");
    }
  };

  useEffect(() => {
    document.addEventListener("submit-secret-key", handleSubmit);
    return () =>
      document.removeEventListener("submit-secret-key", handleSubmit);
  }, [secretKey]);

  return (
    <Button
      variant="default"
      onClick={!hasVerifier ? handleOpenSaveDialog : handleSubmit}
      className="w-fit !bg-neutral-400"
      data-testid="button-confirm-secret-key"
    >
      <div className="flex items-center gap-2">
        <Text>Confirmar</Text>
        <Check />
      </div>
    </Button>
  );
}

export const SecretKeyDialog = {
  Content: SecretKeyDialogContent,
  Footer: SecretKeyDialogFooter,
};