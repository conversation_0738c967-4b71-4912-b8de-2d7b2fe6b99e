import { <PERSON><PERSON>, ChamferBox } from "@snap/design-system";
import { CustomLabel } from "root/domain/models/components/CustomLabel";
import { getInitials } from "~/helpers";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import React, { useState, useEffect } from "react";
import { useReportMetadata, useReportType, useReportProfileImage } from "~/store/reportDetailStore";
import { cn } from "~/lib/utils";
import { isValidUrl } from "root/domain/models/strategy/helpers.strategy";

const ReportProfileHeader: React.FC = () => {
  const metadata = useReportMetadata();
  const reportType = useReportType();
  const profileImage = useReportProfileImage();
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);
  const profileName = metadata?.[REPORT_CONSTANTS.new_report.subject_name] as string || "Nome não disponível";
  const initials = getInitials(profileName);
  const idade = metadata?.[REPORT_CONSTANTS.new_report.subject_age] as number || "N/A";
  const sexo = metadata?.[REPORT_CONSTANTS.new_report.subject_sex] as string || "N/A";
  const nomeMae = metadata?.[REPORT_CONSTANTS.new_report.subject_mother_name] as string || "N/A";
  //@ts-ignore
  const document = metadata?.[REPORT_CONSTANTS.new_report.report_search_args]?.[reportType] || "N/A";
  //TODO - implementar lógica para empresa nos metadados
  //const dataFundacao = dadosPessoais?.detalhes?.["data de fundacao"]?.value || "N/A";
  //const statusReceita = dadosPessoais?.detalhes?.["status receita"]?.value || "N/A";
  // TODO - criar lógica para add imagem e/ou pegar imagem associada ao relatório
  // const avatar = dadosPessoais?.detalhes?.["avatar"]?.value || "N/A";
  const propsToRender = {
    pessoa: [
      { label: "IDADE:", value: idade },
      { label: "SEXO:", value: sexo },
      { label: "NOME DA MÃE:", value: nomeMae }
    ],
    telefone: [
      { label: "IDADE:", value: idade },
      { label: "SEXO:", value: sexo },
      { label: "NOME DA MÃE:", value: nomeMae }
    ],
    empresa: [
      { label: "STATUS NA RECEITA:", value: "ATIVO" },
      { label: "DATA DE FUNDAÇÃO:", value: "02/12/1190" },
    ]
  };
  
  const renderProps = () => {
    switch (reportType) {
      case "cpf":
        return propsToRender.pessoa.map((prop) => (
          <div key={prop.label}>
            <CustomLabel
              label={prop.label}
              hasIcon={false}
              colorClass="bg-border"
            />
            <p>{prop.value}</p>
          </div>
        ));
      case "telefone":
        return propsToRender.telefone.map((prop) => (
          <div key={prop.label}>
            <CustomLabel
              label={prop.label}
              hasIcon={false}
              colorClass="bg-border"
            />
            <p>{prop.value}</p>
          </div>
        ));
      case "cnpj":
        return propsToRender.empresa.map((prop) => (
          <div key={prop.label}>
            <CustomLabel
              label={prop.label}
              hasIcon={false}
              colorClass="bg-border"
            />
            <p>{prop.value}</p>
          </div>
        ));
      default:
        return null;
    }
  };

  // TODO - criar lógica para add imagem e/ou pegar imagem associada ao relatório
  const renderAvatarContent = () => {
    let imgPlaceholder = "";
    const isValidProfileImage = isValidUrl(profileImage);
    if (profileImage && isValidProfileImage) {
      imgPlaceholder = profileImage;
    }

    return imgPlaceholder;
  };


  useEffect(() => {
    setIsAvatarLoading(true);
    const img = new Image();
    img.src = renderAvatarContent();

    img.onload = () => {
      setIsAvatarLoading(false);
    };

    img.onerror = () => {
      setIsAvatarLoading(false);
    };

    // Fallback in case image loading takes too long
    const timer = setTimeout(() => {
      setIsAvatarLoading(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, [profileImage]);

  return (
    <ChamferBox
      className="border-none bg-card w-full rounded-tl-xl rounded-tr-xl rounded-bl-xl"
      corner="bottomRight"
    >
      <div className="flex gap-6">
        <div className="flex-1/8 relative h-51 w-51">
          {isAvatarLoading && (
            <div className="absolute inset-0 z-10 flex items-center justify-center h-full">
              <div className="animate-spin h-20 w-20 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          )}
          <div className={`${isAvatarLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-1000 ease-in-out`}>
            <Avatar
              size="22"
              className={cn(
                "border-8 border-border",
                "[&_img]:h-full [&_img]:w-auto [&_img]:object-cover",
                "[&_span]:h-46 [&_span]:w-46 [&_span]:text-6xl"
              )}

              src={renderAvatarContent()}
              fallback={initials}
              textAlign="left"
              fallbackClassName="text-[32px]"
            />
          </div>
        </div>

        <div className="flex-2/4 space-y-4">
          <h1 className="text-4xl font-bold">{profileName}</h1>

          <div className="grid grid-cols-2 gap-4">
            {renderProps()}
          </div>
        </div>

        <div className="flex-1/4 border-l border-gray-700 pl-4 space-y-4">
          <h2 className="text-lg font-mono">ENTRADAS</h2>

          <div className="">
            <CustomLabel
              label={reportType?.toUpperCase()}
              hasIcon={false}
              className="text-primary"
              colorClass="bg-primary"
            />
            <p>{document}</p>
          </div>
        </div>
      </div>
    </ChamferBox>
  );
};

export default ReportProfileHeader;