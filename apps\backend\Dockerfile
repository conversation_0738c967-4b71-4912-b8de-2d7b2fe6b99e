FROM python:3.12-slim

WORKDIR /app

RUN apt-get update && apt-get install -y curl netcat-openbsd bash && rm -rf /var/lib/apt/lists/*


COPY ./apps/backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy app code and wait script
COPY ./apps/backend /app
COPY ./scripts/wait_until_kafka_alive.sh /app/scripts/wait_until_kafka_alive.sh

RUN chmod +x /app/scripts/wait_until_kafka_alive.sh

ENV PYTHONPATH=/app
EXPOSE 8000

ENTRYPOINT ["/bin/bash", "-c"]
CMD ["/app/scripts/wait_until_kafka_alive.sh && uvicorn main:app --host 0.0.0.0 --port 8000 --log-level debug"]

