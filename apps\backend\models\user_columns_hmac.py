from sqlalchemy import Column, Index, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base

Base = declarative_base()

class UserColumnsHmac(Base):
    __tablename__ = "user_columns_hmac"
    __table_args__ = (
        Index("idx_user_columns_hmac_hmac_column", "hmac", "column_name"),
        {"schema": "public"}
    )


    user_columns_hmac_id = Column(UUID(as_uuid=True), primary_key=True, server_default="gen_random_uuid()")
    hmac = Column(Text, nullable=False)
    column_name = Column(Text, nullable=False)
    user_reports_id = Column(UUID(as_uuid=True), ForeignKey("user_reports.user_reports_id"), nullable=False)


    def __repr__(self):
        return (
            f"<UserColumnsHmac(user_columns_hmac_id={self.user_columns_hmac_id}, hmac={self.hmac}, column_name={self.column_name}, "
            f"user_reports_id={self.user_reports_id})>"
        )