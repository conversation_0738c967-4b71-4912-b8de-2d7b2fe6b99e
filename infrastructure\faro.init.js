import {
    ConsoleInstrumentation,
    ConsoleTransport,
    ErrorsInstrumentation,
    FetchTransport,
    initializeFaro,
    LogLevel,
    SessionInstrumentation,
    WebVitalsInstrumentation,
} from '@grafana/faro-web-sdk';

export const faro= initializeFaro({
    instrumentations: [
        new ErrorsInstrumentation(),
        new WebVitalsInstrumentation(),
        new ConsoleInstrumentation({
            disabledLevels: [LogLevel.TRACE, LogLevel.ERROR], // console.log will be captured
        }),
        new SessionInstrumentation(),
    ],
    transports: [
        new FetchTransport({
            url: 'http://localhost:12347/collect',
            apiKey: '1127',
        }),
        new ConsoleTransport(),
    ],
    app: {
        name: 'techbiz-frontend',
        version: '1.0.0',
    },
});
