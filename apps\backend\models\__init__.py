import logging

from models.base import Base
from models.user_model import Users
from models.report_model import UserReports

# Setup logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s]: %(message)s")

# List all models to be imported easily
__all__ = ["Users", "UserReports"]

logger.info("[models] Models initialized: Users, UserReports")
