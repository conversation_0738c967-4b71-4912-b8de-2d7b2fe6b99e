import axios from "axios";
import { eventBus } from "~/helpers/eventBus.helper";

const BFF_API_URL =
  import.meta.env.MODE && import.meta.env.MODE === "development"
    ? `${import.meta.env.VITE_API_URL}/api/`
    : "/api/";

const reportFetcher = axios.create({
  baseURL: BFF_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

reportFetcher.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Emit unauthorized event --> LOGOUT
      eventBus.emit("UNAUTHORIZED_ERROR");
    }
    return Promise.reject(error);
  }
);

export const BFF_REPORT_CLIENT = reportFetcher;
