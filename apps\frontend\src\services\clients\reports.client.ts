import axios from "axios";
import RootAPI from "root/domain/http/RootAPI";

const REPORTS_API_URL =
  import.meta.env.MODE && import.meta.env.MODE === "development"
    ? `${import.meta.env.VITE_REPORTS_API_URL}/reports/`
    : "/reports/";

const reportsFetcher = axios.create({
  baseURL: REPORTS_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});


export const REPORTS_CLIENT = new RootAPI(reportsFetcher);
