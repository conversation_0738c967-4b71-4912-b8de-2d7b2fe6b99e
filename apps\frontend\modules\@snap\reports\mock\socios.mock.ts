import { Socio } from "../model/Socios";

export const sociosMock: Socio = {
    detalhes: [
        {
            value: {
                "full name": {
                    value: "GIOVANI THIBAU CHRISTOFARO",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO ADMINISTRADOR",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "68000367653",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "04/09/2006",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "<PERSON><PERSON><PERSON>",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "JAIME RODRIGUES BARBOSA NETO",
                    label: "Nome",
                    source: ["SNA<PERSON>"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO ADMINISTRADOR",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "03770139607",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "08/09/2016",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "LUCIANA BISPO DA SILVA GALAO",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84421630187",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "MARCIO JOSE ROSA GOMES",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "47322497104",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "30/08/2019",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "RAFAEL VELASQUEZ SAAVEDRA DA SILVA",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "01356010610",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        }
    ]
};