import { ValueWithSource } from "./ValueWithSource";

export interface _Empresa {
  "razao social": ValueWithSource;
  cnpj: ValueWithSource;
  procon?: ValueWithSource;
  "info restricao"?: ValueWithSource;
  "status receita"?: ValueWithSource;
  "data de fundacao"?: ValueWithSource;
  data_abertura?: ValueWithSource;
  sequencial?: ValueWithSource;
  porte?: ValueWithSource;
  "tipo de imposto"?: ValueWithSource;
  "total de funcionarios"?: ValueWithSource;
  "quantidade de funcionarios acima de 5 salarios"?: ValueWithSource;
  "quantidade de funcionarios abaixo de 5 salarios"?: ValueWithSource;
  cnae?: ValueWithSource;
  "descricao do cnae"?: ValueWithSource;
  cnae1?: ValueWithSource;
  cnae2?: ValueWithSource;
  cnae3?: ValueWithSource;
  cnae4?: ValueWithSource;
  cnae5?: ValueWithSource;
  cnae6?: ValueWithSource;
  cnae7?: ValueWithSource;
  cnae8?: ValueWithSource;
  "cnae secundario"?: ValueWithSource;
  "cnae secundario2"?: ValueWithSource;
  "cnae secundario3"?: ValueWithSource;
  "cnae secundario4"?: ValueWithSource;
  "cnae secundario5"?: ValueWithSource;
  "cnae secundario6"?: ValueWithSource;
  "cnae secundario7"?: ValueWithSource;
  "cnae secundario8"?: ValueWithSource;
}

export interface DadosEmpresa {
  detalhes: _Empresa;
}
