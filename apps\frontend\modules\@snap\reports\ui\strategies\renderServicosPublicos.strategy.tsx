import React from "react";
import { ServicoPublico } from "../../model/ServicoPublico";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel } from "../../helpers";
import { renderSourceTooltip, renderValidArray } from "./helpers.strategy";
import { parseValue } from "../../helpers";
import { ValueWithSource } from "../../model/ValueWithSource";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderServicosPublicos implements RenderStrategy<ServicoPublico> {

    validateKeys = (keys: Array<keyof ServicoPublico>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    formatByKey: Record<
        string,
        (servicoPublico?: ServicoPublico) => React.ReactElement | null
    > = {
            nome_completo: (servicoPublico?: ServicoPublico) => {
                if (!servicoPublico?.nome_completo) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(servicoPublico.nome_completo.label || "Nome Completo").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(servicoPublico.nome_completo.value)}
                                tooltip={renderSourceTooltip(servicoPublico.nome_completo.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (servicoPublico?: ServicoPublico) => {
                if (!servicoPublico?.detalhes) return null;

                return (
                    <CustomGridContainer cols={3} columnFirst className="mb-6">
                        {Object.entries(servicoPublico.detalhes).map(([key, value], index) => {
                            // Cast value to ValueWithSource type
                            const valorComFonte = value as ValueWithSource<string>;
                            return (
                                <GridItem key={`detalhes-${index}-${key}`} cols={1} className="group">
                                    <CustomReadOnlyInputField
                                        label={`${(translatePropToLabel(valorComFonte.label)).toUpperCase()}`}
                                        value={parseValue(String(valorComFonte.value))}
                                        tooltip={renderSourceTooltip(valorComFonte.source)}
                                    />
                                </GridItem>
                            );
                        })}
                    </CustomGridContainer>
                );
            },

            remuneracao: (servicoPublico?: ServicoPublico) => {
                if (!servicoPublico?.remuneracao) return null;

                return renderValidArray('remuneracao', servicoPublico || {}, [], true /* hasLabel */, false /* hasTitle */);
            }

        }

    render = (servicoPublico?: ServicoPublico): React.ReactElement[] => {
        if (!servicoPublico) return [];

        const keys = Object.keys(servicoPublico) as Array<keyof ServicoPublico>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof ServicoPublico> = [
            'nome_completo',
            'detalhes',
            'remuneracao'
        ];

        // Filter the keys to only include those that exist in the servicoPublico object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(servicoPublico);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
