import { Processo } from "../model/Processo";

export const processoMock1: Processo = {
  numero: {
    value: "0001234-56.2023.8.26.0100",
    label: "Número do Processo",
    source: ["Escavador"],
    is_deleted: false,
  },
  detalhes: {
    instancia: {
      value: "1ª Instância",
      label: "Instância",
      source: ["Escavador"],
      is_deleted: false,
    },
    orgao: {
      value: "Tribunal de Justiça de São Paulo",
      label: "<PERSON>rg<PERSON>",
      source: ["Escavador"],
      is_deleted: false,
    },
    "data da remessa": {
      value: "2023-05-15",
      label: "Data da Remessa",
      source: ["Escavador"],
      is_deleted: false,
    },
    "data da instauracao": {
      value: "2023-01-10",
      label: "Data da Instauração",
      source: ["Escavador"],
      is_deleted: false,
    },
  },
  movimentacoes: {
    value: `ata - Conteudo - Tipo,2025-01-21 - Intimação do Dr<PERSON> <PERSON>, Dr<PERSON>. <PERSON><PERSON>, <PERSON><PERSON>. <PERSON>rig<PERSON> Moraes e Dr<PERSON> Barros Vidal do despacho de f. 4448, que segue transcrito: “Ciência às partes acerca do Acórdão de f. 4426-39. Depois, cumpram-se as determinações contidas na sentença de f. 4122-47. Às providências necessárias." Campo Grande, 13 de janeiro de 2025. CARLOS ALBERTO GARCETE Juiz de Direito em Substituição Legal - PUBLICACAO,2024-12-02 - Comarca de Campo Grande - 2ª Vara do Tribunal do Júri EMENTA - APELAÇÃO CRIMINAL - HOMICÍDIO QUALIFICADO - RECURSO INTERPOSTO PELO MINISTÉRIO PÚBLICO ESTADUAL - IMPRONÚNCIA MANTIDA - AUSÊNCIA DE INDÍCIOS MÍNIMOS E SUFICIENTES APTOS A CONVENCER DA MATERIALIDADE DO FATO E DA EXISTÊNCIA DE INDÍCIOS SUFICIENTES DA AUTORIA/PARTICIPAÇÃO - RECURSO DESPROVIDO. 1. Como bem sabe, o sistema processual penal brasileiro adota duas fases distintas para o processamento dos crimes dolosos contra a vida: a primeira, denominada de jus accusationis, tem objetivo voltado ao juízo de admissibilidade da acusação; já a segunda`,
    label: "Movimentações",
    source: ["Escavador"],
    is_deleted: false,
  },
  advogado: [
    {
      value: {
        cpf: {
          value: "123.456.789-00",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Carlos Alberto Souza",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
        oab: {
          value: "OAB/SP 12345",
          label: "OAB",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Advogado",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "987.654.321-00",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Maria Fernanda Lima",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
        oab: {
          value: "OAB/SP 54321",
          label: "OAB",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Advogado",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Autor - Pessoa Física
  autor_pessoa: [
    {
      value: {
        cpf: {
          value: "111.222.333-44",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "João da Silva",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Autor",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "444.555.666-77",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Maria Souza",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Autor",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Réu - Pessoa Jurídica
  reu_empresa: [
    {
      value: {
        cnpj: {
          value: "12.345.678/0001-90",
          label: "CNPJ",
          source: ["Escavador"],
          is_deleted: false,
        },
        "razao social": {
          value: "Empresa ABC Ltda.",
          label: "Razão Social",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Réu",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cnpj: {
          value: "98.765.432/0001-10",
          label: "CNPJ",
          source: ["Escavador"],
          is_deleted: false,
        },
        "razao social": {
          value: "Empresa XYZ S.A.",
          label: "Razão Social",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Réu",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Juiz
  juiz: [
    {
      value: {
        cpf: {
          value: "555.666.777-88",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Dr. Ricardo Oliveira Santos",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Juiz",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "999.888.777-66",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Dr. Ana Paula Ferreira",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Juiz",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Representante - Pessoa Física
  representante_pessoa: [
    {
      value: {
        cpf: {
          value: "999.888.777-66",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Ana Paula Ferreira",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Representante",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "111.222.333-44",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "João da Silva",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Representante",
      source: ["Escavador"],
      is_deleted: false,
    }
  ],
};

export const processoMock2: Processo = {
  numero: {
    value: "0007890-12.2022.8.26.0200",
    label: "Número do Processo",
    source: ["Escavador"],
    is_deleted: false,
  },
  detalhes: {
    instancia: {
      value: "2ª Instância",
      label: "Instância",
      source: ["Escavador"],
      is_deleted: false,
    },
    orgao: {
      value: "Tribunal de Justiça de São Paulo",
      label: "Órgão",
      source: ["Escavador"],
      is_deleted: false,
    },
    "data da remessa": {
      value: "2022-11-20",
      label: "Data da Remessa",
      source: ["Escavador"],
      is_deleted: false,
    },
    "data da instauracao": {
      value: "2022-08-05",
      label: "Data da Instauração",
      source: ["Escavador"],
      is_deleted: false,
    },
  },
  movimentacoes: {
    value: `20/11/2022 - Recurso recebido pelo Tribunal
15/10/2022 - Recurso interposto pela parte autora
30/09/2022 - Sentença proferida: procedente em parte
15/09/2022 - Conclusos para sentença
05/08/2022 - Distribuição do processo`,
    label: "Movimentações",
    source: ["Escavador"],
    is_deleted: false,
  },
  advogado: [
    {
      value: {
        cpf: {
          value: "222.333.444-55",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Roberto Oliveira",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
        oab: {
          value: "OAB/SP 78901",
          label: "OAB",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Advogado",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "555.666.777-88",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Carla Rodrigues",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
        oab: {
          value: "OAB/SP 54321",
          label: "OAB",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Advogado",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Recorrente - Pessoa Jurídica
  recorrente_empresa: [
    {
      value: {
        cnpj: {
          value: "98.765.432/0001-10",
          label: "CNPJ",
          source: ["Escavador"],
          is_deleted: false,
        },
        "razao social": {
          value: "Empresa XYZ S.A.",
          label: "Razão Social",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Recorrente",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cnpj: {
          value: "98.765.432/0001-10",
          label: "CNPJ",
          source: ["Escavador"],
          is_deleted: false,
        },
        "razao social": {
          value: "Empresa XYZ S.A.",
          label: "Razão Social",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Recorrente",
      source: ["Escavador"],
      is_deleted: false,
    },
  ],
  // Exemplo de Recorrido - Pessoa Física
  recorrido_pessoa: [
    {
      value: {
        cpf: {
          value: "444.555.666-77",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Pedro Henrique Almeida",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Recorrido",
      source: ["Escavador"],
      is_deleted: false,
    },
    {
      value: {
        cpf: {
          value: "111.222.333-44",
          label: "CPF",
          source: ["Escavador"],
          is_deleted: false,
        },
        "full name": {
          value: "Ana Maria Rodrigues",
          label: "Nome",
          source: ["Escavador"],
          is_deleted: false,
        },
      },
      label: "Recorrido",
      source: ["Escavador"],
      is_deleted: false,
    },
  ]
};

export const processosMock: Processo[] = [processoMock1, processoMock2];
