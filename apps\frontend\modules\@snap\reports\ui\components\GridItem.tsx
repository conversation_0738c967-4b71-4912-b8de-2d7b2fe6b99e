import React from "react";

interface GridItemProps {
  cols?: 1 | 2 | 3;
  fullWidth?: boolean;
  children: React.ReactNode;
  className?: string;
}

export const GridItem: React.FC<GridItemProps> = ({
  cols = 3,
  fullWidth = false,
  children,
  className = "",
}) => {
  const getColSpanClass = () => {
    if (fullWidth) return "col-span-3";

    /* 
    não funciona o breakpoint por conflito tailwind e ds --> `grid grid-cols-1 md:grid-cols-${cols}`
      1: "col-span-3 md:col-span-1",
      2: "col-span-3 md:col-span-2",
      3: "col-span-3",
    */
    return {
      1: "col-span-1",
      2: "col-span-2",
      3: "col-span-3",
    }[cols];
  };

  return <div className={`${getColSpanClass()} ${className}`}>{children}</div>;
};
