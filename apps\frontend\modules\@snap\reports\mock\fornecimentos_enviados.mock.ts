import { FornecimentoEnviado } from "../model/FornecimentosEnviados";

// Mock for a fornecimento to a company
const fornecimentoEnviado1: FornecimentoEnviado = {
    razao_social: {
        value: "Empresa XYZ Ltda",
        label: "Razão Social",
        source: ["TSEFornecimento"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "12.345.678/0001-90",
            label: "CNPJ",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        cnae: {
            value: "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
            label: "CNAE",
            source: ["TSEFornecimento"],
            is_deleted: false
        }
    },
    fornecimentos: [
        {
            value: {
                valor: {
                    value: "R$ 3.500,00",
                    label: "Valor",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                data: {
                    value: "10/05/2022",
                    label: "Data",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC123456",
                    label: "Documento",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC987654",
                    label: "Recibo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                descricao: {
                    value: "Fornecimento de material gráfico para campanha",
                    label: "Descrição",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                fonte: {
                    value: "Fornecimento direto",
                    label: "Fonte",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                }
            },
            label: "Fornecimento",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        {
            value: {
                valor: {
                    value: "R$ 1.200,00",
                    label: "Valor",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                data: {
                    value: "15/06/2022",
                    label: "Data",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC654321",
                    label: "Documento",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC123456",
                    label: "Recibo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                descricao: {
                    value: "Fornecimento de serviços de consultoria",
                    label: "Descrição",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                especie: {
                    value: "Depósito em espécie",
                    label: "Espécie",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                fonte: {
                    value: "Fornecimento direto",
                    label: "Fonte",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                }
            },
            label: "Fornecimento",
            source: ["TSEFornecimento"],
            is_deleted: false
        }
    ]
};

export const fornecimentosEnviadosMock: FornecimentoEnviado[] = [
    fornecimentoEnviado1,
];
