import { Request, Response, NextFunction } from "express";
import { BaseError } from "../@types/errors";
import {
  JsonWebTokenError,
  TokenExpiredError as JWTTokenExpiredError,
} from "jsonwebtoken";

interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    errors?: any[];
    stack?: string;
  };
}

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error("Error:", {
    name: err.name,
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
  });

  let response: ErrorResponse = {
    success: false,
    error: {
      message: "Internal server error",
    },
  };

  if (err instanceof BaseError) {
    response.error = {
      message: err.message,
      code: err.code,
      errors: err.errors,
    };

    if (process.env.NODE_ENV === "development") {
      response.error.stack = err.stack;
    }

    res.status(err.statusCode).json(response);
    return;
  }

  if (err instanceof JWTTokenExpiredError) {
    response.error = {
      message: "Token has expired",
      code: "TOKEN_EXPIRED",
    };
    res.status(401).json(response);
    return;
  }

  if (err instanceof JsonWebTokenError) {
    response.error = {
      message: "Invalid token",
      code: "INVALID_TOKEN",
    };
    res.status(401).json(response);
    return;
  }

  if (process.env.NODE_ENV === "development") {
    response.error.stack = err.stack;
  }

  res.status(500).json(response);
};