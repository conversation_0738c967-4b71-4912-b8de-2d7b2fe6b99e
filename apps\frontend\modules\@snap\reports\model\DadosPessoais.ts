import { ValueWithSource } from "./ValueWithSource";

interface _Pessoa {
    "full name": ValueWithSource<string>;
    "nome_da_mae"?: ValueWithSource<string>
    nome1?: ValueWithSource<string>;
    "full name1"?: ValueWithSource<string>;
    idade?: ValueWithSource<string>;
    sexo?: ValueWithSource<string>;
    nacionalidade?: ValueWithSource<string>;
    "pais do passaporte"?: ValueWithSource<string>;
    "data nascimento"?: ValueWithSource<string>;
    dt_nascimento?: ValueWithSource<string>;
    cpf: ValueWithSource<string>;
    identidade?: ValueWithSource<string>;
    estadocivil?: ValueWithSource<string>;
    "status receita"?: ValueWithSource<string>;
    "info restricao"?: ValueWithSource<string>;
    "titulo de eleitor"?: ValueWithSource<string>;
    "pis/pasep"?: ValueWithSource<string>;
    ctps?: ValueWithSource<string>;
    procon?: ValueWithSource<string>;
    escolaridade?: ValueWithSource<string>;
    grauinstrucao?: ValueWithSource<string>;
    "descricao cbo"?: ValueWithSource<string>;
    cbo?: ValueWithSource<string>;
    "renda estimada"?: ValueWithSource<string>;
    "renda presumida"?: ValueWithSource<string>;
    "data de admissao"?: ValueWithSource<string>;
  }
  
  export interface DadosPessoais {
    detalhes: _Pessoa;
  }  