import { useCallback } from "react";
import { useUserSalt } from "~/store/userStore";
import { deriveEncryptionKeyArgon2, encryptNgramsDeterministic } from "~/helpers/encryption.helper";
import { encryptionWorker } from "~/services/workers/encryptionWorker.service";
import { useSecretKey } from "~/store/secretKeyStore";
import { EncryptedPayload } from "~/types/global";

export interface CryptoResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export const useEncryption = () => {
  const secretKey = useSecretKey();
  const userSalt = useUserSalt();

  // Derive the raw key (ArrayBuffer) from user password and salt
  const deriveKey = useCallback(async (): Promise<ArrayBuffer> => {
    if (!secretKey || !userSalt) {
      throw new Error("Secret key or salt is missing");
    }
    const key = await deriveEncryptionKeyArgon2(secretKey, userSalt);
    return crypto.subtle.exportKey("raw", key);
  }, [secretKey, userSalt]);

  const encryptData = useCallback(
    async (data: any): Promise<CryptoResult<EncryptedPayload>> => {
      try {
        const rawKey = await deriveKey();
        const encryptedData = await encryptionWorker.encrypt(data, rawKey);
        return {
          success: true,
          data: encryptedData as EncryptedPayload,
        };
      } catch (error) {
        console.error("Encryption error:", error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown encryption error",
        };
      }
    },
    [deriveKey]
  );

  const decryptData = useCallback(
    async (encryptedData: EncryptedPayload): Promise<CryptoResult<any>> => {
      try {
        const rawKey = await deriveKey();
        const decryptedData = await encryptionWorker.decrypt(
          encryptedData,
          rawKey
        );
        return {
          success: true,
          data: decryptedData,
        };
      } catch (error) {
        console.error("Decryption error:", error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown decryption error",
        };
      }
    },
    [deriveKey]
  );


  /**
   * Criptografa os n-grams de um relatório.
   * @param reportObj Objeto contendo os campos do relatório a serem pesquisáveis.
   * @returns Um objeto contendo os n-grams criptografados.
   */
  const encryptNgrams = useCallback(
    async (reportObj: Record<string, any>): Promise<Record<string, string[]>> => {
      const rawKey = await deriveKey();
      return encryptNgramsDeterministic(rawKey, reportObj);
    },
    [deriveKey]
  );

  return {
    encryptData,
    decryptData,
    encryptNgrams,
    salt: userSalt,
  };
};
