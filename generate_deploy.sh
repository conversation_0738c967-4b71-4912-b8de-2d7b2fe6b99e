#!/bin/bash
set -euo pipefail

# Save current directory and restore on exit
ORIGINAL_DIR="$(pwd)"
trap 'cd "$ORIGINAL_DIR"' EXIT
cd "$(dirname "$0")"

# Default values
STACK_NAME=mystack
NETWORK_NAME=mystack-net
PROFILE="prod"
CLIENT_ID_GOOGLE=""
CLIENT_SECRET_GOOGLE=""
KEYCLOAK_ADMIN_PASSWORD=""
CLIENT_SECRET_NAME=client_secret_keycloak
ADMIN_USER_SECRET_NAME=keycloak_admin_user
ADMIN_PASSWORD_SECRET_NAME=keycloak_admin_password
SNAP_API_CLIENT_SECRET_NAME=snap_api_client_secret
CAPTCHA_KEY_NAME=captcha_key
CLIENT_ID_GOOGLE=""
CLIENT_SECRET_GOOGLE=""
CLIENT_ID_MICROSOFT=""
CLIENT_SECRET_MICROSOFT=""
MICROSOFT_TENANT="common"
KEYCLOAK_ADMIN_USER="admin"
KEYCLOAK_ADMIN_PASSWORD=""
SNAP_API_CLIENT_SECRET=""
CAPTCHA_KEY=""

args=(
STACK_NAME
NETWORK_NAME
PROFILE
CLIENT_ID_GOOGLE
CLIENT_SECRET_GOOGLE
KEYCLOAK_ADMIN_PASSWORD
CLIENT_SECRET_NAME
ADMIN_USER_SECRET_NAME
ADMIN_PASSWORD_SECRET_NAME
SNAP_API_CLIENT_SECRET_NAME
CAPTCHA_KEY_NAME
CLIENT_ID_GOOGLE
CLIENT_SECRET_GOOGLE
CLIENT_ID_MICROSOFT
CLIENT_SECRET_MICROSOFT
MICROSOFT_TENANT
KEYCLOAK_ADMIN_USER
KEYCLOAK_ADMIN_PASSWORD
SNAP_API_CLIENT_SECRET
CAPTCHA_KEY
)


print_title() {
  echo ""
  echo "===================="
  echo "$1"
  echo "===================="
}

print_success() {
  echo "✅ $1"
}

print_info() {
  echo "ℹ️ $1"
}

print_warning() {
  echo "⚠️ $1"
}

print_error_and_exit() {
  echo "❌ $1"
  echo "exiting"
  exit 1
}

print_args() {
  print_title "Parsed args:"
  for var_name in "${args[@]}"; do
    value="${!var_name}"  # Indirect expansion to get the value
    echo "$var_name = $value"
  done
}

validate_env() {
  # Required secrets
  print_title "Validating variables"

  [[ -z "$KEYCLOAK_ADMIN_PASSWORD" ]] && print_error_and_exit "--keycloak-admin-password is required."
  [[ -z "$SNAP_API_CLIENT_SECRET" ]] && print_error_and_exit "--snap-api-client-secret is required."
  [[ -z "$CAPTCHA_KEY" ]] && print_error_and_exit "--captcha-key is required."

  print_info "Using Keycloak admin username: $KEYCLOAK_ADMIN_USER"

  # Google OAuth pair validation
  if { [ -n "$CLIENT_ID_GOOGLE" ] && [ -z "$CLIENT_SECRET_GOOGLE" ]; } || \
     { [ -z "$CLIENT_ID_GOOGLE" ] && [ -n "$CLIENT_SECRET_GOOGLE" ]; }; then
      print_error_and_exit "Both --client-id-google and --client-secret-google must be provided together."
  fi

  # Microsoft OAuth + tenant validation
  if [ "$MICROSOFT_TENANT" != "common" ]; then
      echo "🔐 Microsoft Tenant restriction: $MICROSOFT_TENANT"
      if [[ -z "$CLIENT_ID_MICROSOFT" || -z "$CLIENT_SECRET_MICROSOFT" ]]; then
          print_error_and_exit "Tenant specified but Microsoft client creds missing."
      fi
  fi

  if { [ -n "$CLIENT_ID_MICROSOFT" ] && [ -z "$CLIENT_SECRET_MICROSOFT" ]; } || \
     { [ -z "$CLIENT_ID_MICROSOFT" ] && [ -n "$CLIENT_SECRET_MICROSOFT" ]; }; then
      print_error_and_exit "Both --client-id-microsoft and --client-secret-microsoft must be provided together."
  fi

  if [[ -z "$CLIENT_ID_GOOGLE" && -z "$CLIENT_ID_MICROSOFT" ]]; then
      print_error_and_exit "You must provide at least one provider (Google or Microsoft)."
  fi

  print_success "Input validated successfully!"

}

argument_parse(){
  # Argument parsing

  print_title "Parsing arguments..."

  while [[ $# -gt 0 ]]; do
    case "$1" in
      --profile) PROFILE="$2"; shift 2 ;;
      --client-secret-google) CLIENT_SECRET_GOOGLE="$2"; shift 2 ;;
      --client-id-microsoft) CLIENT_ID_MICROSOFT="$2"; shift 2 ;;
      --client-secret-microsoft) CLIENT_SECRET_MICROSOFT="$2"; shift 2 ;;
      --microsoft-tenant) MICROSOFT_TENANT="$2"; shift 2 ;;
      --keycloak-admin-user) KEYCLOAK_ADMIN_USER="$2"; shift 2 ;;
      --keycloak-admin-password) KEYCLOAK_ADMIN_PASSWORD="$2"; shift 2 ;;
      --snap-api-client-secret) SNAP_API_CLIENT_SECRET="$2"; shift 2 ;;
      --captcha-key) CAPTCHA_KEY="$2"; shift 2 ;;
      *) print_error_and_exit "❌ Unknown option while parsing: $1" ;;
    esac
  done

  if [[ "$PROFILE" == "dev" || "$PROFILE" == "back-dev" || "$PROFILE" == "prod-dev" ]]; then
    if [[ "$PROFILE" == "dev" || "$PROFILE" == "back-dev" ]]; then
      env_file=".env"
    else
      env_file=".env.prod.dev"
    fi

    if [[ -f $env_file ]]; then
      echo "📁 Loading .env file, dev profile"
      print_info "Loading .env: $env_file"
      set -a
      # shellcheck source=.env
      source "$env_file"
      set +a
    else
      print_error_and_exit "❌ .env file not found for dev profile"
    fi
  fi

  print_success "Arguments parsed"
}

generate_secret() {
    openssl rand -base64 32 | tr -d '\n'
}

create_a_secret() {
    local SECRET_NAME=$1
    local SECRET_VALUE=$2

    # Check if secret already exists
    if docker secret ls --filter name="$SECRET_NAME" -q | grep -q .; then
        echo "⚠️ Secret $SECRET_NAME already exists. Skipping creation."
        return
    fi

    # Use a secure temporary file
    local TMP_FILE
    TMP_FILE=$(mktemp)

    # Write secret securely and restrict permissions
    umask 077
    echo -n "$SECRET_VALUE" > "$TMP_FILE"

    # Create the secret
    if docker secret create "$SECRET_NAME" "$TMP_FILE"; then
        print_success "Docker secret $SECRET_NAME created!"
    else
        print_error_and_exit "Failed to create secret $SECRET_NAME."
    fi

    # Clean up
    if command -v shred >/dev/null; then
      shred -u "$TMP_FILE"
    else
      print_warning "'shred' not found. Used 'rm' instead (less secure)."
      rm -f "$TMP_FILE"
    fi
}

create_secrets(){

  if docker secret ls --filter name="$CLIENT_SECRET_NAME" -q | grep -q .; then
    print_info "Secret $CLIENT_SECRET_NAME already exists, skipping generation and injection."
    SKIP_KC_REALM_SECRET=true
  else
    SKIP_KC_REALM_SECRET=false
    if [[ -z "$CLIENT_SECRET_KEYCLOAK" ]]; then
      CLIENT_SECRET_KEYCLOAK=$(generate_secret)
      print_info "Generated CLIENT_SECRET_KEYCLOAK: $CLIENT_SECRET_KEYCLOAK"
    fi
    create_a_secret "$CLIENT_SECRET_NAME" "$CLIENT_SECRET_KEYCLOAK"
  fi

  secrets=(
  "$ADMIN_USER_SECRET_NAME"         "$KEYCLOAK_ADMIN_USER"
  "$ADMIN_PASSWORD_SECRET_NAME"     "$KEYCLOAK_ADMIN_PASSWORD"
  "$SNAP_API_CLIENT_SECRET_NAME"    "$SNAP_API_CLIENT_SECRET"
  "$CAPTCHA_KEY_NAME"               "$CAPTCHA_KEY"
)

  for ((i=0; i<${#secrets[@]}; i+=2)); do
    name="${secrets[i]}"
    value="${secrets[i+1]}"

    if [[ -z "$name" || -z "$value" ]]; then
      echo "⚠️ Skipping empty secret name or value: [$name] [$value]"
      continue
    fi

    if docker secret inspect "$name" &>/dev/null; then
      print_info "Removing existing secret: $name"
      docker secret rm "$name" >/dev/null
    else
      print_info "Secret $name not found, skipping removal."
    fi

    print_info "Creating secret $name"
    create_a_secret "$name" "$value"
  done

}

create_keycloak_conf(){
  if [ "$SKIP_KC_REALM_SECRET" = false ]; then
    print_title "Injecting secrets into realm.json"

    ESC_KC_SECRET=$(printf '%s\n' "$CLIENT_SECRET_KEYCLOAK" | sed 's/[&/\]/\\&/g')
    ESC_GOOGLE_ID=$(printf '%s\n' "$CLIENT_ID_GOOGLE" | sed 's/[&/\]/\\&/g')
    ESC_GOOGLE_SECRET=$(printf '%s\n' "$CLIENT_SECRET_GOOGLE" | sed 's/[&/\]/\\&/g')
    ESC_MS_ID=$(printf '%s\n' "$CLIENT_ID_MICROSOFT" | sed 's/[&/\]/\\&/g')
    ESC_MS_SECRET=$(printf '%s\n' "$CLIENT_SECRET_MICROSOFT" | sed 's/[&/\]/\\&/g')
    ESC_TENANT=$(printf '%s\n' "$MICROSOFT_TENANT" | sed 's/[&/\]/\\&/g')

    mkdir -p apps/keycloak

    sed -e "s/CLIENT_SECRET_KEYCLOAK/$ESC_KC_SECRET/g" \
        -e "s/CLIENT_ID_GOOGLE/$ESC_GOOGLE_ID/g" \
        -e "s/CLIENT_SECRET_GOOGLE/$ESC_GOOGLE_SECRET/g" \
        -e "s/CLIENT_ID_MICROSOFT/$ESC_MS_ID/g" \
        -e "s/CLIENT_SECRET_MICROSOFT/$ESC_MS_SECRET/g" \
        -e "s/MICROSOFT_TENANT/$ESC_TENANT/g" \
        apps/keycloak/realm.json.template > apps/keycloak/realm.json

    print_success "realm.json generated successfully"
  else
    print_info "Skipping realm.json update for Keycloak client secret."
  fi
}

build_images() {
  print_title "Building Docker Images"

  extra_args=""

  # Optional: Validate required variables for dev
  if [[ "$PROFILE" == "dev" || "$PROFILE" == "prod-dev" ]]; then
    extra_args=(--progress=plain)
    if [[ -z "${VITE_API_URL:-}" || -z "${VITE_REPORTS_API_URL:-}" ]]; then
      print_error_and_exit "Missing VITE_API_URL or VITE_REPORTS_API_URL for dev build."
    fi
  fi

  export DOCKER_BUILDKIT=1

  # Common image builds
  docker build -t my-backend-image -f apps/backend/Dockerfile . $extra_args

  case "$PROFILE" in
    "prod"|"prod-dev")
      print_info "Buiding front with VITE_API_URL=$VITE_API_URL"
      print_info "Buiding front with VITE_REPORTS_API_URL=$VITE_REPORTS_API_URL"

      docker build -t my-bff-image ./apps/bff $extra_args

      docker build \
        --build-arg VITE_API_URL="$VITE_API_URL" \
        --build-arg VITE_REPORTS_API_URL="$VITE_REPORTS_API_URL" \
        -f apps/frontend/Dockerfile \
        -t my-frontend-image . $extra_args

      docker build -t my-keycloak-image ./apps/keycloak $extra_args
      docker build -t my-kafka-image ./apps/kafka $extra_args
      docker build -t my-minio-image -f apps/minio/Dockerfile . $extra_args
      docker build -t my-spark-image -f apps/spark/Dockerfile . $extra_args
      ;;

    "dev")
      docker build \
        --build-arg VITE_API_URL="$VITE_API_URL" \
        --build-arg VITE_REPORTS_API_URL="$VITE_REPORTS_API_URL" \
        -f apps/frontend/Dockerfile \
        -t my-frontend-image . $extra_args
      ;;
    "back-dev")
    # intentionally no-op; handled elsewhere or not needed
    ;;
    *)
      print_error_and_exit "Unknown PROFILE: '$PROFILE'. Expected 'dev' 'back-dev' or 'prod'."
      ;;
  esac

  print_success "Docker images built successfully for profile: $PROFILE"
}

deploy_stack() {

  print_title "Deploying Docker Stack: $STACK_NAME (Environment: $PROFILE)"

  case "$PROFILE" in
  dev)
    # Define compose files for dev
    COMPOSE_FILES=(
      "docker-compose-backend.yml"
      "docker-compose-frontend.yml"
      "docker-compose-backend-dev.yml"
      "docker-compose-frontend-dev.yml"
    )
    docker network create --driver overlay --attachable "$NETWORK_NAME" || true
    ;;

  back-dev)
    # Define compose files for back-dev
    COMPOSE_FILES=(
      "docker-compose-backend.yml"
      "docker-compose-backend-dev.yml"
    )
    docker network create --driver overlay --attachable "$NETWORK_NAME" || true
    ;;

  prod)
    # Default: production stack
    COMPOSE_FILES=(
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose-backend.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-bff.yml"
      "docker-compose-frontend.yml"
      "docker-compose-ngrok.yml"
      "docker-compose-kafka.yml"
      "docker-compose-minio.yml"
      "docker-compose-spark.yml"
    )
    ;;
  prod-dev)

    COMPOSE_FILES=(
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose-backend.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-bff.yml"
      "docker-compose-frontend.yml"
      "docker-compose-ngrok.yml"
      "docker-compose-kafka.yml"
      "docker-compose-minio.yml"
      "docker-compose-spark.yml"
    )

#    docker compose --env-file $env_file $(printf " -f %s" "${COMPOSE_FILES[@]}") config > "merged-stack.yml"
#    COMPOSE_FILES=(
#      "merged-stack.yml"
#    )

#    docker compose --env-file .env -f docker-compose-spark.yml config > merged.yml
esac

    # Construct the deploy command
    local DEPLOY_CMD="docker stack deploy"
    for file in "${COMPOSE_FILES[@]}"; do
        DEPLOY_CMD+=" -c $file"
    done
    DEPLOY_CMD+=" $STACK_NAME"

    # Output the deploy command for clarity
    eval "$DEPLOY_CMD"
    print_success "Deployed successfully!"
}

docker_stack_cleanup() {
  print_title "Clearing stack before deployment: $STACK_NAME"

  # Remove stack if exists
  if docker stack ls | grep -q "$STACK_NAME"; then
    print_info "Removing existing Docker Stack: $STACK_NAME"
    docker stack rm "$STACK_NAME"

    print_info "Waiting for Docker stack '$STACK_NAME' to shut down..."
    while docker stack services "$STACK_NAME" 2>&1 | grep -vq "Nothing found in stack"; do
      print_info "Still shutting down..."; sleep 2
    done
    print_success "Stack '$STACK_NAME' is fully removed."
  else
    print_success "Stack '$STACK_NAME' does not exist."
  fi

  # Remove conflicting Docker network
  network_id=$(docker network ls --filter name="^$NETWORK_NAME$" -q)

  if [ -n "$network_id" ]; then
    print_info "Removing existing Docker network: $NETWORK_NAME"
    echo "⚠️ Found network $NETWORK_NAME with ID: $network_id"

    attached_containers=$(docker network inspect "$network_id" -f '{{range .Containers}}{{.Name}} {{end}}')

    if [ -n "$attached_containers" ]; then
      for container in $attached_containers; do
        print_info "Forcibly removing container $container from network"
        docker rm -f "$container" || true
      done
    fi

    print_info "🧹 Removing the network..."
    docker network rm "$network_id" && print_success "✅ Network removed." || print_info "❌ Failed to remove network."

    print_info "⏳ Waiting for network $NETWORK_NAME to be fully removed..."
    while docker network ls | grep -q "$network_id"; do
      print_info "Still removing network $NETWORK_NAME..."; sleep 2
    done
    print_success "Network $NETWORK_NAME fully removed."
  else
    print_success "✅ No existing $NETWORK_NAME network found."
  fi
}


argument_parse "$@"
print_args
validate_env

# Init Swarm
docker swarm init 2>/dev/null || true
# Remove stack
docker_stack_cleanup
create_secrets
create_keycloak_conf
build_images
deploy_stack
