import { Email, _Email } from "../model/Emails";

export const emailsMockCNPJ: Email = {
    detalhes: [
        {
            "value": {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            "label": "Email",
            "source": ["SNAP"],
            "is_deleted": false
        },
        {
            "value": {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            "label": "Email",
            "source": ["SNAP"],
            "is_deleted": false
        }
    ]
};