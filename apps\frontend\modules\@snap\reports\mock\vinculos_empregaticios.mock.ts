import { VinculoEmpregaticio } from "../model/VinculosEmpregaticios";

export const vinculoEmpregaticio1: VinculoEmpregaticio = {
    empresa_pagadora: {
        value: "STELLANTIS AUTOMOVEIS BRASIL LTDA",
        label: "Empresa Pagadora",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "16.701.716/0001-56",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "data admissao": {
            value: "23/04/2015",
            label: "Admiss<PERSON>",
            source: ["SNAP"],
            is_deleted: false
        },
        valor: {
            value: "R$ 8.500,00",
            label: "Remuneração (R$)",
            source: ["SNAP"],
            is_deleted: false
        },
        "razao social": {
            value: "STELLANTIS AUTOMOVEIS BRASIL LTDA",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        situacao: {
            value: "Ativo",
            label: "Situação",
            source: ["SNAP"],
            is_deleted: false
        },
        cargo: {
            value: "Analista de Sistemas",
            label: "Cargo",
            source: ["SNAP"],
            is_deleted: false
        }
    }
};

export const vinculoEmpregaticio2: VinculoEmpregaticio = {
    empresa_pagadora: {
        value: "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA",
        label: "Empresa Pagadora",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "03.492.162/0001-82",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "data admissao": {
            value: "01/11/2017",
            label: "Admissão",
            source: ["SNAP"],
            is_deleted: false
        },
        valor: {
            value: "R$ 2.089,92",
            label: "Remuneração (R$)",
            source: ["SNAP"],
            is_deleted: false
        },
        "razao social": {
            value: "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        situacao: {
            value: "Inativo",
            label: "Situação",
            source: ["SNAP"],
            is_deleted: false
        },
        cargo: {
            value: "Assistente Administrativo",
            label: "Cargo",
            source: ["SNAP"],
            is_deleted: false
        },
        "data demissao": {
            value: "15/03/2019",
            label: "Data de Demissão",
            source: ["SNAP"],
            is_deleted: false
        }
    }
};

export const vinculoEmpregaticio3: VinculoEmpregaticio = {
    empresa_pagadora: {
        value: "NAVI CARNES INDUSTRIA E COMERCIO LTDA",
        label: "Empresa Pagadora",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "02.982.267/0003-19",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "data admissao": {
            value: "08/02/2010",
            label: "Admissão",
            source: ["SNAP"],
            is_deleted: false
        },
        valor: {
            value: "R$ 540,60",
            label: "Remuneração (R$)",
            source: ["SNAP"],
            is_deleted: false
        },
        "razao social": {
            value: "NAVI CARNES INDUSTRIA E COMERCIO LTDA",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        situacao: {
            value: "Inativo",
            label: "Situação",
            source: ["SNAP"],
            is_deleted: false
        },
        cargo: {
            value: "Auxiliar de Produção",
            label: "Cargo",
            source: ["SNAP"],
            is_deleted: false
        },
        "data demissao": {
            value: "10/08/2010",
            label: "Data de Demissão",
            source: ["SNAP"],
            is_deleted: false
        }
    }
};

export const vinculoEmpregaticio4: VinculoEmpregaticio = {
    empresa_pagadora: {
        value: "MUNICIPIO DE ROCHEDO",
        label: "Empresa Pagadora",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "03.501.566/0001-95",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "data admissao": {
            value: "11/05/2010",
            label: "Admissão",
            source: ["SNAP"],
            is_deleted: false
        },
        valor: {
            value: "R$ 577,08",
            label: "Remuneração (R$)",
            source: ["SNAP"],
            is_deleted: false
        },
        "razao social": {
            value: "PREFEITURA MUNICIPAL DE ROCHEDO",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        situacao: {
            value: "Inativo",
            label: "Situação",
            source: ["SNAP"],
            is_deleted: false
        },
        cargo: {
            value: "Assistente Administrativo",
            label: "Cargo",
            source: ["SNAP"],
            is_deleted: false
        },
        "data demissao": {
            value: "30/12/2010",
            label: "Data de Demissão",
            source: ["SNAP"],
            is_deleted: false
        }
    }
};

export const vinculoEmpregaticio5: VinculoEmpregaticio = {
    empresa_pagadora: {
        value: "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA",
        label: "Empresa Pagadora",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "19.314.695/0001-04",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "data admissao": {
            value: "13/11/2014",
            label: "Admissão",
            source: ["SNAP"],
            is_deleted: false
        },
        valor: {
            value: "R$ 900,00",
            label: "Remuneração (R$)",
            source: ["SNAP"],
            is_deleted: false
        },
        "razao social": {
            value: "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        situacao: {
            value: "Inativo",
            label: "Situação",
            source: ["SNAP"],
            is_deleted: false
        },
        cargo: {
            value: "Vendedor",
            label: "Cargo",
            source: ["SNAP"],
            is_deleted: false
        },
        "data demissao": {
            value: "05/03/2015",
            label: "Data de Demissão",
            source: ["SNAP"],
            is_deleted: false
        }
    }
};

export const vinculosEmpregaticiosMock: VinculoEmpregaticio[] = [
    vinculoEmpregaticio1,
    vinculoEmpregaticio2,
    vinculoEmpregaticio3,
    vinculoEmpregaticio4,
    vinculoEmpregaticio5
];
