import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";
import path from "path";
import wasm from "vite-plugin-wasm";
import tailwindcss from '@tailwindcss/vite';

export default defineConfig(({ mode }) => {
  const isDevelopment = mode === 'development';
  
  const constantsPath = isDevelopment 
    ? path.resolve(__dirname, "../../constants")
    : path.resolve(__dirname, "./constants");

  return {
    build: {
      sourcemap: true,
    },
/*     esbuild: {
      pure: mode === 'production' ? ['console.log'] : [],
    }, */
    plugins: [react(), wasm(), tailwindcss()],
    worker: {
      inline: false,
      format: "es",
    },
    server: {
      port: 3000,
      host: true,
      watch: {
        usePolling: true,
      },
      headers: {
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "X-Content-Type-Options": "nosniff",
        "Referrer-Policy": "no-referrer",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
      },
    },
    resolve: {
      alias: {
        "~": path.resolve(__dirname, "./src"),
        "~constants": constantsPath, // Now using the computed value.
        root: path.resolve(__dirname, "./"),
      },
    },
    // Optionally, you can inject this value globally
    define: {
      __CONSTANTS_PATH__: JSON.stringify(constantsPath),
    },
    test: {
      globals: true,
      include: [
        "./src/__tests__/unit/**/*.{test,spec}.{ts,js}",
        "./src/__tests__/ui/**/*.{test,spec}.{ts,js,tsx,jsx}",
      ],
      coverage: {
        include: ["./src/**/*.{ts,js,tsx,jsx}"],
        all: true,
      },
      setupFiles: ["./setupTests.ts"],
      environment: "jsdom",
    },
  };
});
