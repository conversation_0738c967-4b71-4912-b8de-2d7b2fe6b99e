import { Button, Input, Text } from '@snap/design-system'
import { ArrowRight} from 'lucide-react'
import { useState } from 'react';
import { toast } from 'sonner';
import { Checkbox } from '~/components/ui/checkbox';
import { Label } from '~/components/ui/label';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import { REPORT_TYPES } from '~/helpers/constants';
import { useUserData } from '~/store/userStore'
import { ReportCredits } from '~/types/global';

const InviteUserAccount = () => {
  const userData = useUserData();
  const [email, setEmail] = useState("")
  const [userType, setUserType] = useState("investigador")
  const [originTypes, setOriginTypes] = useState({
    cpf: true,
    cnpj: false,
    email: false,
    telefone: false,
  })
  const [credits, setCredits] = useState<number>(100)
  const options = Object.values(REPORT_TYPES as ReportCredits | object).map(
    ([value]) => ({
      value: value,
      label: value.toUpperCase(),
    })
  );


  const handleOriginTypeChange = (type: string, checked: boolean) => {
    setOriginTypes((prev) => ({ ...prev, [type]: checked }))
  }

  return (
    <div className="flex flex-col gap-8 justify-center p-4">
      <Text variant="body-lg" className="font-semibold">
        Coloque o <span className='text-accent'> e-mail do usuário</span> que deseja convidar e defina seu tipo de acesso:
      </Text>

      <div className='flex gap-4 border border-border rounded-md p-8'>
        <div className="flex items-center flex-1 gap-4">
          <Input
            type="email"
            placeholder='Insira o e-mail aqui'
            variant='filled'
            onChange={(e) => setEmail(e.target.value)}
            className='rounded-none border-0 py-1.5 bg-background'
            wrapperClassName='flex-1 max-w-lg border-1 border-foreground'
          />

          <RadioGroup value={userType} onValueChange={setUserType} className="flex flex-1 gap-8">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="investigador" id="investigador" className="border-white" />
              <Label htmlFor="investigador" className="text-[18px]">
                Investigador
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="administrador" id="administrador" className="border-white" />
              <Label htmlFor="administrador" className="text-[18px]">
                Administrador
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="space-y-8">
        <Text variant="body-lg" className="font-semibold">
          Marque os <span className="text-accent">tipos de relatório</span> que esse usuário terá acesso:
        </Text>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {options.map((item) => {
            const checked = originTypes[item.value as keyof typeof originTypes]
            return (
              <label
                key={item.value}
                htmlFor={item.value}
                className={`
            flex items-center justify-between gap-2
            bg-radio-button py-2 px-3 rounded
            hover:opacity-100
            ${checked ? "" : "opacity-80"}
            cursor-pointer
          `}
              >
                <span className="font-mono text-[18px]">
                  {item.label}
                </span>

                <Checkbox
                  id={item.value}
                  checked={checked}
                  onCheckedChange={(c) =>
                    handleOriginTypeChange(item.value, c as boolean)
                  }
                  className="border-[#D9D9D9] data-[state=checked]:bg-white rounded-none"
                />
              </label>
            )
          })}
        </div>
      </div>

      <div className="flex items-center gap-8 border-t border-b border-dashed border-border py-5">
        <Text variant="body-lg" className="font-semibold">
          Defina a quantidade de <span className="text-accent">consultas</span> para uso desse usuário:
        </Text>

        <Input
          type="number"
          variant='filled'
          min={0}
          value={credits}
          onChange={(e) => setCredits(Number(e.target.value))}
          className='rounded-none border-0 py-1.5 bg-background'
          wrapperClassName='flex-1 max-w-[100px] border-1 border-foreground'
        />
      </div>

      <Button
      variant="outline"
        iconPosition="right"
        className='w-full max-w-[264px]'
        icon={<ArrowRight className="size-4" />}
      >
        ENVIAR CONVITE
      </Button>
    </div>
  )
}


export default InviteUserAccount