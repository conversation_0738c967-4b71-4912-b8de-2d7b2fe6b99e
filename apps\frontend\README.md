# SNAP Reports Frontend Documentation

## Setup and Running Instructions

### Prerequisites
- Node.js v22.13.1 (as specified in Volta config)
- pnpm v9.3.0 (as specified in Volta config)

### Installation
```bash
# Install dependencies
pnpm install
```

## Running the Application

### Development Mode
```bash
# Run in development mode localy with Vite
pnpm run dev
```

### Docker Development Mode (with backend api)
```bash
# Run in development mode with <PERSON><PERSON>, alongside backend api
./generate_and_deploy.sh --profile dev
```

### Production Mode
```bash
# Build the application
pnpm run build

# Preview production build
pnpm run preview
```

## Testing

### Unit Tests
```bash
# Run tests in watch mode
pnpm test

# Run tests once
pnpm run test:run

# Run tests for staged files
pnpm run test:staged

# Run tests with coverage
pnpm run test:ci
```

### E2E Tests
```bash
# Run E2E tests with UI
pnpm run test:e2e

# Run E2E tests in CI mode
pnpm run test:e2e:ci

# Install E2E test browser (Firefox)
pnpm run test:e2e:install

```

## Available Scripts

| Command | Description |
|---------|-------------|
| `pnpm run dev` | Start development server with Vite |
| `pnpm run build` | Build for production |
| `pnpm run preview` | Preview production build |
| `pnpm run lint` | Run ESLint with auto-fix |
| `pnpm run validate` | Run type checking and linting |
| `pnpm test` | Run Vitest in watch mode |
| `pnpm run test:run` | Run Vitest tests once |
| `pnpm run test:staged` | Run tests for staged files |
| `pnpm run test:ci` | Run tests with coverage |
| `pnpm run test:e2e` | Run Playwright E2E tests with UI |
| `pnpm run test:e2e:ci` | Run E2E tests in CI mode |
| `pnpm run test:e2e:install` | Install Playwright Firefox browser |
| `pnpm run precommit` | Run lint-staged pre-commit checks |
