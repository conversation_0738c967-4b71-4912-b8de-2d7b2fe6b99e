import logging

logger = logging.getLogger(__name__)
import json
from fastapi import APIRouter, Request, Depends, WebSocket, WebSocketDisconnect, Query, HTTPException, Response, FastAPI
from fastapi.encoders import jsonable_encoder
import sqlalchemy
from sqlalchemy.ext.asyncio import AsyncSession
from jose import jwt
import asyncio
from typing import Optional, List
import uuid

from services.report_service import (
    update_report_handler,
    get_one_report_handler,
    get_saved_reports_handler,
    merge_reports_handler,
    get_data_from_snap_api_handler,
    insert_report_handler,
    insert_verifier_handler,
    snap_status_ws,
    load_from_minio,
    delete_from_minio,
    try_send_websocket
)

from utils.report_utils import (create_blank_report_with_status, get_pending_reports_by_user,
                                get_data_to_pending_reports, get_number_of_report_type, update_error_report_to_pending,
                                update_error_report, spend_quota, extract_normalized_ngrams)

from core.config import settings
from database.db import get_db, get_database_async_engine
from services.auth_service import auth_guard
from schemas.report_schema import SnapApiRequest, InsertReport, InsertVerifier
from core.constants import CustomErrorMessages, DefaultReports, ReportStatus, SummaryReportStatus, ReportMockValidator
from core.jwt_utils import logout_user_using_token


router = APIRouter()

@router.websocket("/ws/snap-status/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    app = websocket.app
    manager = app.state.connection_manager

    logger.info(f"[websocket_endpoint][user({user_id})] Request connected.")

    await manager.connect(websocket, user_id)

    try:

        pending_reports = await get_pending_reports_by_user(user_id=user_id)
        pending_reports_no = len(pending_reports)

        for report in pending_reports:
            report_id, report_type, report_status = report

            result = await load_from_minio(
                bucket_name="processed-reports",
                object_name=f"{user_id}_{report_id}.json",
                user_id=user_id
            )

            if not result:
                logger.info(f"[websocket_endpoint][user({user_id})] Report {report_id} not "
                            f"found in minio bucket processed-reports.")
                continue

            message = {
                    "id": report_id,
                    "status_code": SummaryReportStatus.success,
                    "result": result,
                }
            safe_message = jsonable_encoder(message)

            try:
                await websocket.send_json(safe_message)

            except Exception as send_err:
                logger.warning(f"[websocket_endpoint] Failed to send WebSocket message: {send_err}")
                continue

            pending_reports_no -= 1
            logger.info(f"[websocket_endpoint][user({user_id})] Sent websocket reports result {report_id}")
            
            # await delete_from_minio(
            #             bucket_name="processed-reports",
            #             object_name=f"{user_id}_{report_id}.json",
            #             user_id=user_id
            #                 )
            
            # logger.info("[websocket_endpoint][user(%s)] Delete from minio buckets reports and processed-reports: %s", user_id, result)

        if pending_reports_no == 0:
            return

        # ✅ Keep socket alive for future Kafka event
        while True:
            try:
                # Wait for ping or a small sleep to avoid tight loop
                await asyncio.sleep(30)
                pending_reports = await get_pending_reports_by_user(user_id=user_id)
                if len(pending_reports) == 0:
                    break
                # await websocket.send_text("ping")
            except Exception as e:
                logger.warning("[websocket_endpoint][user(%s)] Keep-alive failed: %s", user_id, str(e))
                break

    except WebSocketDisconnect:
        logger.info("[websocket_endpoint][user(%s)] WebSocket disconnected for request %s.", user_id, user_reports_id)
    finally:
        await manager.disconnect(user_id)
        logger.info("[websocket_endpoint][user(%s)] Connection closed and cleaned up.", user_id)


@router.put("/insert-report/{user_reports_id}")
async def insert_report(request: InsertReport, user_reports_id: str, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):

    request_keys = list(request.model_dump().keys())
    logger.info(f"[insert_report][user({user.get('sub')})] Inserting report for {user_reports_id}")
    logger.info(f"[insert_report][user({user.get('sub')})] Received request keys: {request_keys}")

    await insert_report_handler(body=request, user=user, user_reports_id=user_reports_id, db=db)


@router.get("/get-one-report/{user_reports_id}")
async def get_one_report(user_reports_id: str, db: AsyncSession = Depends(get_db), user=Depends(auth_guard)):
    logger.info("[get_one_report][user(%s)] Fetching report %s.", user, user_reports_id)

    return await get_one_report_handler(user_reports_id, user=user, db=db)

#
# @router.post("/get-data-from-snap-api")
# async def get_data_from_snap_api(
#     request: Request,
#     user: dict = Depends(auth_guard),
#     db: AsyncSession = Depends(get_db)
# ):
#     raw_body = await request.body()
#     logger.info("[get_data_from_snap_api][user(%s)] Raw body received: %s", user, raw_body.decode())
#     client_id=''
#     user_id = user.get("sub")
#
#     try:
#         parsed_body = SnapApiRequest(**await request.json())
#     except Exception as e:
#         logger.error("[get_data_from_snap_api][user(%s)] Failed to parse SnapApiRequest: %s", user, e)
#         raise
#
#     logger.info("[get_data_from_snap_api][user(%s)] Starting Snap API data fetch.", user)
#     logger.info("[get_data_from_snap_api][user(%s)] Received request: %s", user, parsed_body.model_dump())
#
#     app = request.app
#     max_retries = 3
#     attempt = 0
#
#     while attempt < max_retries:
#         try:
#
#             if ReportMockValidator.is_test_case(parsed_body.report_type, parsed_body.report_input_value):
#                 client_id = str(uuid.uuid4())
#                 client = {'id': client_id}
#             else:
#                 client = await get_data_from_snap_api_handler(body=parsed_body, user=user, db=db)
#                 client_id = client['id']
#
#             user_id = user.get("sub")
#             report_status = {"status_report": ReportStatus.InProgress.pending_low_case, "snap_request_id": client_id}
#             report_search_args = {parsed_body.report_type: parsed_body.report_input_value}
#
#             await spend_quota(db, user_id, 1)
#             if parsed_body.user_reports_id is None:
#                 logger.info("[get_data_from_snap_api][user(%s)] Creating blank report for client %s.", user, client_id)
#                 user_reports_id = await create_blank_report_with_status(
#                     db=db,
#                     user_id=user_id,
#                     status_report=report_status,
#                     report_type=parsed_body.report_type,
#                     report_input_encrypted=parsed_body.report_input_encrypted
#                 )
#             else:
#                 user_reports_id = parsed_body.user_reports_id
#                 await update_error_report_to_pending(user_id=user_id, report_id=user_reports_id, status_report=report_status)
#
#             report_number = await get_number_of_report_type(user_id=user_id, report_type=parsed_body.report_type)
#             logger.info("[get_data_from_snap_api][user(%s)] Report number: %s", user_id, report_number)
#
#             # Composite task key using client_id and user_reports_id
#             task_key = (user_id, parsed_body.user_reports_id)
#             if task_key in app.state.running_snap_tasks:
#                 logger.info("[get_data_from_snap_api][user(%s)] Task already running for %s", user_id, task_key)
#
#                 return client
#
#             # Create and store task
#             task = asyncio.create_task(
#                 snap_status_ws(
#                     app=app,
#                     request_id=client_id,
#                     user_id=user_id,
#                     report_type=parsed_body.report_type,
#                     report_number=report_number,
#                     report_search_args=report_search_args,
#                     reports_id=user_reports_id
#                 )
#             )
#
#             def cleanup(_):
#                 app.state.running_snap_tasks.pop(task_key, None)
#
#             task.add_done_callback(cleanup)
#             app.state.running_snap_tasks[task_key] = task
#
#             return client
#
#         except Exception as e:
#             attempt += 1
#             logger.warning("[get_data_from_snap_api][user(%s)] Attempt %s failed: %s", user, attempt, str(e))
#
#             if isinstance(e, HTTPException) and e.status_code == 422:
#                 logger.error("[get_data_from_snap_api][user(%s)] Invalid input format. Not retrying.", user)
#                 raise HTTPException(**CustomErrorMessages.input_value_snap_wrong.to_dict())
#
#             if attempt == max_retries:
#                 logger.error("[get_data_from_snap_api][user(%s)] All %s attempts failed.", user, max_retries)
#
#                 if isinstance(e, HTTPException) and e.status_code == 500: #fail on snap api, create the report but with error
#                     report_status = {"status_report": SummaryReportStatus.error}
#                     user_reports_id = await create_blank_report_with_status(
#                         db=db,
#                         user_id=user_id,
#                         snap_request_id=client_id,
#                         status_report=report_status,
#                         report_type=parsed_body.report_type,
#                         report_input_encrypted=parsed_body.report_input_encrypted
#                         )
#
#                 else:
#                     await update_error_report(snap_request_id=client_id,user_id= user_id,report_id=user_reports_id)
#                     logger.info("[get_data_from_snap_api][user(%s)] Update status from pending to error on database to user_reports_id %s", user , user_reports_id)
#                     connection_manager = app.state.connection_manager
#
#                 await try_send_websocket(connection_manager, user_id, user_reports_id,{
#                         "id": user_reports_id,
#                         "status_code": SummaryReportStatus.error
#                         }
#                         )
#                 logger.info("[get_data_from_snap_api][user(%s)] Sent Websocket mesage of error to user_reports_id: %s", user , parsed_body.user_reports_id)
#                 raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_number_of_report_type.to_dict())
#
#             await asyncio.sleep(1)

@router.post("/get-data-from-snap-api")
async def get_data_from_snap_api(
        request: Request,
        user: dict = Depends(auth_guard),
        db: AsyncSession = Depends(get_db)
):
    user_id = user.get("sub")
    if not user_id:
        logger.error("Missing user ID in authentication token")
        raise HTTPException(status_code=401, detail="Invalid authentication token")

    # Log request information with proper redaction
    raw_body = await request.body()
    safe_body = f"Length: {len(raw_body)} bytes"  # Avoid logging potentially sensitive data
    logger.info(f"[get_data_from_snap_api][user({user_id})] Request received: {safe_body}")

    # Parse request body with proper error handling
    try:
        parsed_body = SnapApiRequest(**await request.json())
        logger.info(f"[get_data_from_snap_api][user({user_id})] Processing request type: {parsed_body.report_type}")
    except ValueError as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid request format: {str(e)}")
        raise HTTPException(status_code=422, detail="Invalid request format") from e
    except Exception as e:
        logger.error(f"[get_data_from_snap_api][user({user_id})] Failed to parse request: {str(e)}")
        raise HTTPException(status_code=400, detail="Failed to process request") from e

    # Create report status information
    report_status = {"status_report": ReportStatus.InProgress.pending_low_case}
    report_search_args = {parsed_body.report_type: parsed_body.report_input_value}
    user_reports_id = parsed_body.user_reports_id
    client_id = None
    app = request.app

    # Process the request with retry logic
    max_retries = 3
    for attempt in range(1, max_retries + 1):
        try:
            # Handle test cases differently from real API calls
            if ReportMockValidator.is_test_case(parsed_body.report_type, parsed_body.report_input_value):
                client_id = str(uuid.uuid4())
                client = {'id': client_id}
                logger.info(
                    f"[get_data_from_snap_api][user({user_id})] Processing test case, generated client_id: {client_id}")
            else:
                client = await get_data_from_snap_api_handler(body=parsed_body, user=user, db=db)
                client_id = client.get('id')
                if not client_id:
                    raise ValueError("Missing client ID in API response")

            # Update report status with client ID
            report_status["snap_request_id"] = client_id

            # Deduct quota for the API call
            await spend_quota(db, user_id, 1)

            # Create or update report based on whether user_reports_id was provided
            if user_reports_id is None:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Creating new report for client {client_id}")
                user_reports_id = await create_blank_report_with_status(
                    db=db,
                    user_id=user_id,
                    status_report=report_status,
                    report_type=parsed_body.report_type,
                    report_input_encrypted=parsed_body.report_input_encrypted
                )
            else:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Updating existing report {user_reports_id}")
                await update_error_report_to_pending(
                    user_id=user_id,
                    report_id=user_reports_id,
                    status_report=report_status
                )

            # Get report metrics
            report_number = await get_number_of_report_type(
                user_id=user_id,
                report_type=parsed_body.report_type
            )
            logger.info(f"[get_data_from_snap_api][user({user_id})] Report number: {report_number}")

            # Create unique task key and check if already running
            task_key = (user_id, user_reports_id)
            if task_key in app.state.running_snap_tasks:
                logger.info(f"[get_data_from_snap_api][user({user_id})] Task already running for {user_reports_id}")
                return client

            # Create and configure background task
            task = asyncio.create_task(
                snap_status_ws(
                    app=app,
                    request_id=client_id,
                    user_id=user_id,
                    report_type=parsed_body.report_type,
                    report_number=report_number,
                    report_search_args=report_search_args,
                    reports_id=user_reports_id
                )
            )

            # Add cleanup callback and store task reference
            task.add_done_callback(lambda _: app.state.running_snap_tasks.pop(task_key, None))
            app.state.running_snap_tasks[task_key] = task

            return client

        except HTTPException as http_ex:
            if http_ex.status_code == 422:
                # Don't retry validation errors
                logger.error(f"[get_data_from_snap_api][user({user_id})] Invalid input format: {http_ex.detail}")
                raise HTTPException(**CustomErrorMessages.input_value_snap_wrong.to_dict())

            # Log other HTTP exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed with HTTP status {http_ex.status_code}: {http_ex.detail}"
            )

            if attempt >= max_retries:
                await handle_max_retries_exceeded(
                    db, app, user_id, client_id, user_reports_id, parsed_body, http_ex
                )
                break

        except Exception as e:
            # Log general exceptions and potentially retry
            logger.warning(
                f"[get_data_from_snap_api][user({user_id})] Attempt {attempt} failed: {str(e)}"
            )

            if attempt >= max_retries:
                await handle_max_retries_exceeded(
                    db, app, user_id, client_id, user_reports_id, parsed_body, e
                )
                break

        # Wait before retry
        if attempt < max_retries:
            await asyncio.sleep(1 * attempt)  # Exponential backoff


async def handle_max_retries_exceeded(
        db, app, user_id, client_id, user_reports_id, parsed_body, exception
):
    """Handle case when max retries are exceeded during Snap API requests."""
    logger.error(
        f"[get_data_from_snap_api][user({user_id})] All retry attempts failed: {str(exception)}"
    )

    # Handle server errors from snap API differently
    if isinstance(exception, HTTPException) and exception.status_code == 500:
        # Create report with error status for server-side failures
        report_status = {"status_report": SummaryReportStatus.error}
        user_reports_id = await create_blank_report_with_status(
            db=db,
            user_id=user_id,
            snap_request_id=client_id,
            status_report=report_status,
            report_type=parsed_body.report_type,
            report_input_encrypted=parsed_body.report_input_encrypted
        )
    else:
        # Update existing report to error state
        if user_reports_id:
            await update_error_report(
                snap_request_id=client_id,
                user_id=user_id,
                report_id=user_reports_id
            )
            logger.info(
                f"[get_data_from_snap_api][user({user_id})] Updated status to error for report {user_reports_id}"
            )

    # Send websocket error notification if possible
    try:
        connection_manager = app.state.connection_manager
        await try_send_websocket(
            connection_manager,
            user_id,
            user_reports_id,
            {
                "id": user_reports_id,
                "status_code": SummaryReportStatus.error
            }
        )
        logger.info(
            f"[get_data_from_snap_api][user({user_id})] Sent error via WebSocket for report {user_reports_id}"
        )
    except Exception as ws_error:
        logger.error(
            f"[get_data_from_snap_api][user({user_id})] Failed to send WebSocket error: {str(ws_error)}"
        )

    # Raise appropriate exception to caller
    raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_number_of_report_type.to_dict())

@router.get("/get-saved-reports")
async def get_saved_reports(
    limit: int = Query(DefaultReports.DEFAULT_REPORTS_TO_FETCH),
    page: int = Query(1),
    order: str = Query("desc", description="Sort direction: 'asc' or 'desc'"),
    column_order: str = Query("created_at", description="Column to order by"),
    hmac_filter: Optional[str] = Query(None, description="Optional HMAC filter"),
    hmac_column: Optional[str] = Query(None, description="Column to filter hmac"),
    db: AsyncSession = Depends(get_db),
    user: dict = Depends(auth_guard)
):
    logger.info("[get_saved_reports][user(%s)] Fetching saved reports.", user)

    return await get_saved_reports_handler(db=db,
                user=user,
                limit=limit,
                page=page,
                order=order,
                column_order=column_order, 
                hmac_filter=hmac_filter,
                hmac_column=hmac_column)


@router.get("/auth/user")
async def get_authenticated_user(
    request: Request,
    response: Response,
    user: dict = Depends(auth_guard)
):
    user_id = user.get("sub")  # This assumes 'sub' is in your JWT claims

    if not user_id:
        logger.error("[get_authenticated_user][user(%s)] No user ID found in token.", user_id)
        raise HTTPException(**CustomErrorMessages.user_id_not_found_token.to_dict())

    logger.info("[get_authenticated_user][user(%s)] Fetching user data from DB.", user_id)

    engine = get_database_async_engine()
    query = """
        SELECT * FROM public.users
        WHERE user_id = :user_id;
    """

    async with engine.connect() as conn:
        result = await conn.execute(sqlalchemy.text(query), {"user_id": user_id})
        row = result.fetchone()

    if row:
        logger.info("[get_authenticated_user][user(%s)] User found.", user_id)
        return dict(row._mapping)
    else:
        logger.error("[get_authenticated_user][user(%s)] User not found.", user_id)
        raise HTTPException(**CustomErrorMessages.user_not_found.to_dict(user_id))


@router.post("/verifier")
async def insert_verifier(request: InsertVerifier, db: AsyncSession = Depends(get_db), user: dict = Depends(auth_guard)):
    logger.info("[insert_verifier][user(%s)] Inserting verifier for user %s.", user, user.get('sub'))
    logger.info("[insert_verifier][user(%s)] Received request: %s", user, request.model_dump())

    return await insert_verifier_handler(request, db, user)


@router.post("/logout")
async def logout_user(request: Request, response: Response):
    body = await request.body()
    logger.info("[logout_user] Received request: %s", body.decode("utf-8"))
    refresh_token = request.cookies.get("refresh_token")
    logger.info("[logout_user] Logging out user.")
    return await logout_user_using_token(refresh_token=refresh_token, response=response)


@router.post("/merge-reports")
async def merge_reports(request: Request, db: AsyncSession = Depends(get_db)):
    logger.info("[merge_reports] Merging reports.")
    return await merge_reports_handler(request, db)


@router.post("/update-report")
async def update_report(request: Request, db: AsyncSession = Depends(get_db)):
    logger.info("[update_report] Updating report.")
    return await update_report_handler(request, db)
