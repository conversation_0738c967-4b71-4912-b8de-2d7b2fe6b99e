"use client";
import { useMemo } from "react";
import { cn } from "~/lib/utils";
import { useReportSections } from "~/store/reportDetailStore";

type CategoryItem = {
    title: string;
    data_count: number;
};

export const ReportRecordsList = () => {
    const reportSections = useReportSections();
    const categories = reportSections
        ?.filter(section => !section.subsection)
        .map((section) => ({ title: section.title, data_count: section.data_count }));

    const calculateTotalRecords = (items: CategoryItem[]): number => {
        if (!items || items.length === 0) return 0;

        return items.reduce((sum, category) => {
            const count = Number(category.data_count) || 0;
            return sum + count;
        }, 0);
    };

    const totalRecords = useMemo(() => calculateTotalRecords(categories), [categories]);

    function getBadgeStyles(data_count: number): string {
        const maxCount = 100;
        const intensity = Math.min(Math.floor((data_count / maxCount) * 9), 9);

        switch (intensity) {
            case 0:
                return "bg-gray-900 text-gray-300 dark:bg-gray-900 dark:text-gray-300";
            case 1:
                return "bg-gray-800 text-gray-300 dark:bg-gray-800 dark:text-gray-300";
            case 2:
                return "bg-gray-700 text-gray-200 dark:bg-gray-700 dark:text-gray-200";
            case 3:
                return "bg-gray-600 text-gray-100 dark:bg-gray-600 dark:text-gray-100";
            case 4:
                return "bg-gray-500 text-gray-100 dark:bg-gray-500 dark:text-gray-100";
            case 5:
                return "bg-gray-400 text-gray-900 dark:bg-gray-400 dark:text-gray-900";
            case 6:
                return "bg-gray-300 text-gray-900 dark:bg-gray-300 dark:text-gray-900";
            case 7:
                return "bg-gray-200 text-gray-900 dark:bg-gray-200 dark:text-gray-900";
            case 8:
                return "bg-gray-200 text-gray-900 dark:bg-gray-100 dark:text-gray-900";
            case 9:
                return "bg-gray-100 text-gray-900 dark:bg-gray-100 dark:text-gray-900";
            default:
                return "bg-gray-800 text-gray-300 dark:bg-gray-800 dark:text-gray-300";
        }
    }

    return (
        <div className="max-h-[calc(100vh-300px)] overflow-y-auto overflow-x-hidden">
            <div className="py-3">
                <span className="text-xl font-bold">{totalRecords ? totalRecords : "Sem"}</span> registros encontrados
            </div>

            <div className="flex-1 overflow-y-auto [scrollbar-gutter:stable] border-t border-dashed py-3">
                <div className="">
                    {categories &&
                        categories?.map((category, index) => (
                            <div
                                key={`section-${category.title}-${index}`}
                                className="flex items-center justify-between p-1.5 hover:text-accent cursor-pointer group"
                            >
                                <span className="text-card-foreground group-hover:text-accent transition-colors">{category.title}</span>
                                <div
                                    className={cn(
                                        "flex h-6 w-8 items-center justify-center rounded-full text-xs font-medium ",
                                        getBadgeStyles(category.data_count)
                                    )}
                                >
                                    {category?.data_count?.toString()?.padStart(2, "0")}
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
};