import { Tabs } from '@snap/design-system'
import DefaultUserAccount from "./DefaultUserAccount";
import GeneralAccount from './GeneralAccount';
import InviteUserAccount from './InviteUserAccount';
import AdminUserAccount from './AdminUserAccount';
const TabContainerAccount = () => {

  const tabList = [
    {
      value: 'geral',
      label: 'Geral',
      children: <GeneralAccount />
    },
    {
      value: 'convidar_usuarios',
      label: 'Convidar Usuários',
      children: <InviteUserAccount />
    },
    {
      value: 'gerenciar_usuarios',
      label: 'Gerenciar Usuários',
      children: <AdminUserAccount />
    },
    {
      value: 'relatorios',
      label: 'RELATÓRIOS',
      children: <DefaultUserAccount />
    }
  ]


  return (
    <Tabs items={tabList} className='[&_[role=tab]]:cursor-pointer' /* [&_[role=tab]]:bg-modal-body [&_[role=tabpanel]]:bg-modal-body*/  />
  );
};

export default TabContainerAccount;
