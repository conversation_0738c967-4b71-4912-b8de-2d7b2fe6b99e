import { DadosPessoais } from "../model/DadosPessoais";

export const dadosPessoaisMock: DadosPessoais = {
  detalhes: {
    "full name": {
      value: "<PERSON>",
      label: "Nome Completo",
      source: ["SNAP"],
      is_deleted: false
    },
    "nome_da_mae": {
      value: "<PERSON> da <PERSON>",
      label: "Nome da Mãe",
      source: ["SNAP"],
      is_deleted: false
    },
    idade: {
      value: "35",
      label: "Idade",
      source: ["SNAP"],
      is_deleted: false
    },
    sexo: {
      value: "<PERSON><PERSON><PERSON><PERSON>",
      label: "Sexo",
      source: ["SNAP"],
      is_deleted: false
    },
    nacionalidade: {
      value: "Brasileira",
      label: "Nacionalidade",
      source: ["SNAP"],
      is_deleted: false
    },
    "pais do passaporte": {
      value: "Brasil",
      label: "País do Passaporte",
      source: ["SNAP"],
      is_deleted: false
    },
    "data nascimento": {
      value: "10/05/1989",
      label: "Data de Nascimento",
      source: ["SNAP"],
      is_deleted: false
    },
    cpf: {
      value: "123.456.789-10",
      label: "CPF",
      source: ["SNAP"],
      is_deleted: false
    },
    identidade: {
      value: "MG-12.345.678",
      label: "Identidade",
      source: ["SNAP"],
      is_deleted: false
    },
    estadocivil: {
      value: "Solteiro",
      label: "Estado Civil",
      source: ["SNAP"],
      is_deleted: false
    },
    "status receita": {
      value: "Ativo",
      label: "Status na Receita",
      source: ["SNAP"],
      is_deleted: false
    },
    "info restricao": {
      value: "Sem restrições",
      label: "Informação de Restrição",
      source: ["SNAP"],
      is_deleted: false
    },
    "titulo de eleitor": {
      value: "1234 5678 9012",
      label: "Título de Eleitor",
      source: ["SNAP"],
      is_deleted: false
    },
    "pis/pasep": {
      value: "123.45678.90-1",
      label: "PIS/PASEP",
      source: ["SNAP"],
      is_deleted: false
    },
    ctps: {
      value: "123456 - SP",
      label: "CTPS",
      source: ["SNAP"],
      is_deleted: false
    },
    procon: {
      value: "Nenhum",
      label: "Procon",
      source: ["SNAP"],
      is_deleted: false
    },
    escolaridade: {
      value: "Superior Completo",
      label: "Escolaridade",
      source: ["SNAP"],
      is_deleted: false
    },
    grauinstrucao: {
      value: "Graduação",
      label: "Grau de Instrução",
      source: ["SNAP"],
      is_deleted: false
    },
    "descricao cbo": {
      value: "Desenvolvedor de Software",
      label: "Descrição CBO",
      source: ["SNAP"],
      is_deleted: false
    },
    cbo: {
      value: "2123-05",
      label: "CBO",
      source: ["SNAP"],
      is_deleted: false
    },
    "renda estimada": {
      value: "R$ 5.000,00",
      label: "Renda Estimada",
      source: ["SNAP"],
      is_deleted: false
    },
    "renda presumida": {
      value: "R$ 5.000,00",
      label: "Renda Presumida",
      source: ["SNAP"],
      is_deleted: false
    },
    "data de admissao": {
      value: "2020-01-15",
      label: "Data de Admissão",
      source: ["SNAP"],
      is_deleted: false
    },
  },
};
