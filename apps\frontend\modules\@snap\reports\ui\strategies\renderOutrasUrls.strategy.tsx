import React from "react";
import { Url } from "../../model/Urls";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { ReadOnlyInputField } from "@snap/design-system";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export class RenderOutrasUrls implements RenderStrategy<Url> {
  validateKeys = (keys: Array<keyof Url>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (urls?: Url) => React.ReactElement | null
  > = {
      detalhes: (urls?: Url) => {
        if (!urls?.detalhes?.length) return null;

        return (
          <CustomGridContainer cols={1} className="">
            <GridItem fullWidth>
              <CustomGridContainer cols={2}>
                {urls.detalhes.map((detalhe, index) => (
                  <GridItem key={`url-detalhe-column-${index}`} cols={1}>

                    <div className="">
                      {Object.entries(detalhe.value).map(
                        ([key, valueObj]) => {
                          return (

                            <GridItem
                              key={`detalhe-${index}-${key}`}
                              cols={1}
                              className="py-2 group"
                            >
                              <CustomReadOnlyInputField
                                label={`${(detalhe.label || key).toUpperCase()} ${index + 1}`}
                                value={String(
                                  typeof valueObj === 'object' && valueObj?.value
                                    ? valueObj.value
                                    : valueObj
                                )}
                                tooltip={renderSourceTooltip(valueObj.source)}
                              />
                            </GridItem>

                          )
                        }
                      )}
                    </div>

                  </GridItem>
                ))}
              </CustomGridContainer>
            </GridItem>
          </CustomGridContainer>
        );
      },
    };

  validateData = (data?: Url): boolean => {
    try {
      if (!data || typeof data !== 'object') {
        console.warn('Invalid image data received:', data);
        return false;
      }
      return true;
    } catch (error) {
      console.warn('Error validating image data:', error);
      return false;
    }
  };

  render = (data: Url): React.ReactElement[] => {
    try {
      if (!this.validateData(data)) {
        console.warn('Invalid data structure received');
        return [];
      }

      const keys = Object.keys(data!) as Array<keyof Url>;

      if (!this.validateKeys(keys)) {
        console.warn('Invalid keys in data:', keys);
        return [];
      }

      return keys
        .map((chave) => this.formatByKey[chave]?.(data))
        .filter((el): el is React.ReactElement => el !== null);
    } catch (error) {
      console.warn('Error validating image data:', error);
      return [];
    }

  };
}
