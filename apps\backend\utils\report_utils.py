import logging
from sqlalchemy import select, update, delete, func, literal, and_, or_, distinct, asc, desc, case
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, Request
import uuid
from datetime import datetime, timezone
import unicodedata
import re
from typing import Dict, Any, List, Tuple

from core.constants import Fields, ReportStatus, CustomErrorMessages, SummaryReportStatus
from models.report_model import UserReports
from models.user_columns_hmac import UserColumnsHmac
from models.user_model import Users
from database.db import get_db, async_session

logger = logging.getLogger(__name__)


def get_field_from_request(request: Request, field_name: str, raise_error=True, default_value=None):
    logger.info("[get_field_from_request] Searching for field in request.")
    try:
        this_field = request.get(field_name)
        logger.info("[get_field_from_request] Found field '%s': %s", field_name, this_field)
        return this_field
    except (Attribute<PERSON>rror, TypeError, KeyError):
        logger.warning("[get_field_from_request] Field '%s' missing.", field_name)
        if raise_error:
            raise HTTPException(status_code=400, detail="Missing field: %s" % field_name)
        else:
            return default_value



def make_default_report_name(report_type: str, search_args: str):
    logger.info("[make_default_report_name] Creating report name.")
    caps_titles = ['cpf', 'cnpj']
    if report_type.lower() in caps_titles:
        report_type = report_type.upper()
    else:
        report_type = report_type.title()

    all_args_list = [item for value in search_args.values() for item in value]
    report_name = '%s %s' % (report_type, ", ".join(all_args_list))
    logger.info("[make_default_report_name] Report name generated: %s", report_name)
    return report_name


async def get_report(db: AsyncSession, user_id: str, report_id: str):
    logger.info("[get_report] Fetching report.")
    try:
        user_uuid = uuid.UUID(user_id)
        report_uuid = uuid.UUID(report_id)
    except ValueError as e:
        logger.error("[get_report] Invalid UUID provided: %s", e)
        return None

    result = await db.execute(
        select(UserReports)
        .where(UserReports.user_id == user_uuid)
        .where(UserReports.user_reports_id == report_uuid)
    )
    report = result.scalars().first()
    logger.info("[get_report] Fetched report: %s", report)
    return report



async def delete_report(db: AsyncSession, user_id: str, report_id: str):
    logger.info("[delete_report] Deleting report.")
    try:
        user_uuid = uuid.UUID(user_id)
        report_uuid = uuid.UUID(report_id)

        query = (
            delete(UserReports)
            .where(UserReports.user_id == user_uuid)
            .where(UserReports.user_reports_id == report_uuid)
        )

        response = await db.execute(query)
        await db.commit()
        logger.info("[delete_report] Report deleted successfully.")
        return response.rowcount > 0
    except SQLAlchemyError as e:
        logger.error("[delete_report] Database error: %s", e)
        await db.rollback()
        return None


async def spend_quota(db: AsyncSession, user_id: str, report_cost: int):
    logger.info("[spend_quota][user(%s)] Spending user credits.", user_id)
    try:
        query = select(Users).where(Users.user_id == user_id)
        result = await db.execute(query)
        user = result.scalars().first()

        if not user:
            logger.error("[spend_quota][user(%s)] User not found.", user_id)
            raise HTTPException(**CustomErrorMessages.spend_quota_failed.to_dict())

        available_credits = getattr(user, Fields.total_credits, 0)
        new_credits_total = available_credits - report_cost

        await db.execute(
            update(Users)
            .where(Users.user_id == user_id)
            .values({Fields.total_credits: new_credits_total})
        )
        await db.commit()
        logger.info("[spend_quota][user(%s)] Updated credits. New balance: %s", user_id, new_credits_total)

    except Exception as e:
        logger.error("[spend_quota][user(%s)] Error: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.spend_quota_failed.to_dict())



async def insert_verifier_on_db(db: AsyncSession, user_id: str, verifier: dict):
    logger.info("[insert_verifier_on_db][user(%s)] Inserting verifier.", user_id)
    try:
        await db.execute(
            update(Users)
            .where(Users.user_id == user_id)
            .values({Fields.verifier: verifier})
        )
        await db.commit()
        logger.info("[insert_verifier_on_db][user(%s)] Verifier inserted successfully.", user_id)
        return {"verifier": verifier, "status": "só sucesso"}
    except Exception as e:
        logger.error("[insert_verifier_on_db][user(%s)] Error: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.verifier_cannot_be_update.to_dict(user_id))


async def update_blank_report(db: AsyncSession, user_uuid: uuid.UUID, user_reports_id: str,
    report_name: dict, report_type: str, report_status: dict, report_search_args: dict,
    subject_name: dict, subject_mother_name: dict, subject_age: int, subject_sex: dict,
    creation_at: str, modified_at: str, omitted_notes: dict, data: dict):
    
    logger.info("[update_blank_report][user(%s)] Updating blank report.", user_uuid)
    try:
        update_dict = {
            Fields.report_name: report_name,
            Fields.report_type: report_type,
            Fields.report_status: report_status,
            Fields.report_search_args: report_search_args,
            Fields.subject_name: subject_name,
            Fields.subject_mother_name: subject_mother_name,
            Fields.subject_age: subject_age,
            Fields.subject_sex: subject_sex,
            Fields.created_at: creation_at,
            Fields.modified_at: modified_at,
            Fields.omitted_notes: omitted_notes,
            Fields.data: data,
        }

        await db.execute(
            update(UserReports)
            .where(UserReports.user_reports_id == user_reports_id)
            .where(UserReports.user_id == user_uuid)
            .values(**update_dict)
        )
        await db.commit()
        logger.info("[update_blank_report][user(%s)] Blank report updated successfully.", user_uuid)
    except Exception as e:
        logger.error("[update_blank_report][user(%s)] Error: %s", user_uuid, e)
        await db.rollback()



async def save_merged_report(db: AsyncSession, user_id: str, merged_report_credits, last_modified_date, report_dict, report_type: str, report_name=None):
    logger.info("[save_merged_report][user(%s)] Saving merged report.", user_id)
    try:
        user_uuid = uuid.UUID(user_id)
    except ValueError:
        logger.error("[save_merged_report][user(%s)] Invalid user_id UUID.", user_id)
        return None

    if report_name is None:
        report_name = make_default_report_name(report_type, report_dict[Fields.search_args])

    report_dict.update({
        Fields.report_name: report_name,
        Fields.credits_used: merged_report_credits,
        Fields.created_at: last_modified_date,
        Fields.modified_at: last_modified_date,
        Fields.report_status: ReportStatus.complete,
        Fields.report_type: report_type,
        Fields.user_id: user_uuid,
    })

    result = await db.execute(insert(UserReports).values(**report_dict).returning(UserReports))
    await db.commit()

    inserted = result.mappings().first()
    logger.info("[save_merged_report][user(%s)] Merged report saved successfully.", user_id)

    if inserted:
        response_create_merged = dict(inserted)
        response_create_merged_dict = response_create_merged["UserReports"]
        report_dict = {key: getattr(response_create_merged_dict, key) for key in UserReports.__table__.columns.keys()} if response_create_merged_dict else None

    return {
        Fields.report_name: report_name,
        Fields.user_reports_id: report_dict["userId"]
    }



async def update_existing_report(db: AsyncSession, report_id: str, omitted_notes=None, report_name=None):
    logger.info("[update_existing_report] Updating report %s.", report_id)
    try:
        report_uuid = uuid.UUID(report_id)
    except ValueError:
        logger.error("[update_existing_report] Invalid UUID format.")
        return None

    update_data = {Fields.modified_at: datetime.now(timezone.utc)}
    if report_name:
        update_data[Fields.report_name] = report_name

    try:
        await db.execute(update(UserReports).where(UserReports.user_reports_id == report_uuid).values(**update_data))
        await db.commit()
        logger.info("[update_existing_report] Report updated successfully.")
    except Exception as e:
        logger.error("[update_existing_report] Error: %s", e)


async def get_user_report_types_and_credits(db: AsyncSession, user_id: str):
    logger.info("[get_user_report_types_and_credits][user(%s)] Fetching user credits and types.", user_id)
    try:
        result = await db.execute(select(Users.user_id, Users.report_types, Users.credits).where(Users.user_id == user_id))
        user = result.fetchone()
        if user:
            return user.report_types, user.credits
        else:
            logger.warning("[get_user_report_types_and_credits][user(%s)] No user found.", user_id)
            return {}, 0
    except Exception as e:
        logger.error("[get_user_report_types_and_credits][user(%s)] Error: %s", user_id, e)
        return {}, 0



async def get_data_to_pending_reports(user_id: str, request_snap_id: str):
    logger.info("[get_data_to_pending_reports][user(%s)] Getting data to pending report.", user_id)
    db_gen = get_db()
    try:
        db = await anext(db_gen)
    except Exception as e:
        logger.error("[get_data_to_pending_reports][user(%s)] Failed to open DB: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_pending_reports.to_dict())

    try:
        report_status = {"status_report": SummaryReportStatus.pending, "snap_request_id": request_snap_id}

        result = await db.execute(
            select(UserReports.report_type, UserReports.user_reports_id, UserReports.report_status)
            .where(UserReports.report_status == literal(report_status, type_=JSONB))
            .where(UserReports.user_id == user_id)
        )

        user_reports = result.fetchone()

        if user_reports:
            return user_reports.report_type, user_reports.user_reports_id, user_reports.report_status
        else:
            logger.warning("[get_data_to_pending_reports][user(%s)] No report pending found.", user_id)
            return '', '', {}

    except Exception as e:
        logger.error("[get_data_to_pending_reports][user(%s)] Error: %s", user_id, e)
        return '', '', {}
    finally:
        await db_gen.aclose()


async def get_pending_reports_by_user(user_id: str) -> List[Tuple[str, str, dict]]:
    logger.info(f"[get_pending_reports_by_user][user({user_id})] Fetching pending reports...")

    db_gen = get_db()
    try:
        db = await anext(db_gen)
    except Exception as e:
        logger.error(f"[get_pending_reports_by_user][user({user_id})] DB connection failed: {e}")
        raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_pending_reports.to_dict())

    try:
        report_status = {"status_report": SummaryReportStatus.pending}

        result = await db.execute(
            select(
                UserReports.user_reports_id,
                UserReports.report_type,
                UserReports.report_status
            )
            .where(UserReports.user_id == user_id)
            .where(UserReports.report_status.contains(report_status))
        )

        reports = result.fetchall()

        if not reports:
            logger.info(f"[get_pending_reports_by_user][user({user_id})] No pending reports found.")
            return []

        logger.info(f"[get_pending_reports_by_user][user({user_id})] Found {len(reports)} pending reports.")
        return reports  # List of Row objects with user_reports_id, report_type, report_status

    except Exception as e:
        logger.exception("[get_pending_reports_by_user][user(%s)] Failed to fetch reports: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_pending_reports.to_dict())
    finally:
        await db_gen.aclose()

async def get_number_of_report_type(user_id: str, report_type: str):
    logger.info("[get_number_of_report_type][user(%s)] Getting number of types of a report for a user.", user_id)
    try:
        db_gen = get_db()
        db = await anext(db_gen)
    except Exception as e:
        logger.error("[get_number_of_report_type][user(%s)] Failed to open DB: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_number_of_report_type.to_dict())

    result = await db.execute(
        select(func.count())
        .where(UserReports.user_id == user_id)
        .where(UserReports.report_type == report_type)
    )
    count = result.scalar_one_or_none() or 0

    return str(count)


async def create_blank_report_with_status(db: AsyncSession, user_id: str, status_report: dict, report_type: str, report_input_encrypted: dict):
    logger.info("[create_blank_report_with_status][user(%s)] Creating blank report.", user_id)

    try:
        new_report = UserReports(
            user_id=user_id,
            created_at= datetime.now(timezone.utc),
            report_status=status_report,
            report_type=report_type,
            report_search_args=report_input_encrypted
        )
        db.add(new_report)
        await db.commit()
        await db.refresh(new_report)
        logger.info("[create_blank_report_with_status][user(%s)] Blank report created successfully.", user_id)
        return new_report.user_reports_id
    except SQLAlchemyError as e:
        logger.error("[create_blank_report_with_status][user(%s)] Database error: %s", user_id, e)
        await db.rollback()
        raise HTTPException(**CustomErrorMessages.fail_create_empty_report.to_dict())



async def update_error_report_to_pending(user_id: str, report_id: str, status_report: dict):
    logger.info("[update_error_report_to_pending][user(%s)] Updating error report.", user_id)

    db_gen = get_db()
    db = await anext(db_gen)

    try:
       
        update_query = (
                update(UserReports)
                .where(UserReports.user_reports_id == report_id)
                .where(UserReports.user_id == user_id)
                .values({Fields.report_status: status_report, 
                         Fields.created_at: datetime.now(timezone.utc)})
            )
        
        await db.execute(update_query)
        await db.commit()
        logger.info("[update_error_report_to_pending][user(%s)] Updated error report to pending.", user_id)
    except SQLAlchemyError as e:
        logger.error("[update_error_report_to_pending][user(%s)] Database error: %s", user_id, e)
        await db.rollback()
        raise HTTPException(**CustomErrorMessages.fail_create_empty_report.to_dict())
    finally:
        await db_gen.aclose()




async def insert_started_snap_status(user_id: str, status_report: dict):
    logger.info("[insert_started_snap_status][user(%s)] Inserting snap_status_ws already started.", user_id)
    db_gen = get_db()
    try:
        db = await anext(db_gen)
    except Exception as e:
        logger.error("[insert_started_snap_status][user(%s)] Failed to open DB: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.fail_to_access_data_to_get_pending_reports.to_dict())

    try:
        query = select(UserReports.user_reports_id).where(
            UserReports.report_status == literal(status_report, type_=JSONB),
            UserReports.user_id == user_id
        )
        result = await db.execute(query)
        user_report_id = result.scalar_one_or_none()

        if user_report_id:
            logger.info("[insert_started_snap_status][user(%s)] Report found, updating report_status...", user_id)

            new_status = {**status_report, "running": True}

            update_query = (
                update(UserReports)
                .where(UserReports.user_reports_id == user_report_id)
                .values(report_status=new_status)
            )
            await db.execute(update_query)
            await db.commit()
        else:
            logger.warning("[insert_started_snap_status][user(%s)] No matching report found.", user_id)

    except Exception as e:
        logger.error("[insert_started_snap_status][user(%s)] Error: %s", user_id, e)
    finally:
        await db_gen.aclose()



async def update_error_report(snap_request_id: str, user_id: str, report_id: str):
    logger.info("[update_error_report][user(%s)] Updating report to error status.", user_id)

    try:
        async with async_session() as db:
            report_status = {
                "status_report": SummaryReportStatus.error,
                "snap_request_id": snap_request_id
            }

            await db.execute(
                update(UserReports)
                .where(UserReports.user_reports_id == report_id)
                .where(UserReports.user_id == user_id)
                .values({Fields.report_status: literal(report_status, type_=JSONB), 
                         Fields.created_at: datetime.now(timezone.utc)})
            )
            await db.commit()

            logger.info("[update_error_report][user(%s)] Report updated to error.", user_id)
    except Exception as e:
        logger.error("[update_error_report][user(%s)] Failed to update report: %s", user_id, e)
        raise HTTPException(**CustomErrorMessages.fail_update_error_report.to_dict())


async def list_user_reports(db: AsyncSession, user_id: str,
                            limit: int = None, page: int = 1,
                            order: str = "desc", column_order: str = "created_at",
                            hmac_filter: str = None, hmac_column:str = None, offset: int = None) -> list[dict]:

    logger.info("[list_user_reports][user(%s)] Listing user reports.", user_id)

    user_reports_ids = False

    if hmac_filter:
        try:
            user_reports_ids = await filter_using_hmac(db=db, hmac_filter=hmac_filter, hmac_column=hmac_column)
        except Exception as e:
            logger.error("[list_user_reports][user(%s)] Error while filtering with HMAC: %s", user_id, e)
            raise

    query = select(
        UserReports.report_name,
        UserReports.report_status,
        UserReports.modified_at,
        UserReports.created_at,
        UserReports.report_type,
        UserReports.report_search_args,
        UserReports.user_reports_id,
        UserReports.subject_age,
        UserReports.subject_mother_name,
        UserReports.subject_name,
        UserReports.subject_sex
    )

    if user_reports_ids is False:
        logger.info(f"[list_user_reports][user({user_id})] No HMAC filters applied.")
        query = query.where(UserReports.user_id == user_id)

    elif not user_reports_ids:
        return []
    else:
        logger.info("[list_user_reports][user(%s)] Applying user_reports_id filter: %s", user_id, user_reports_ids)
        query = query.where(
            and_(
                UserReports.user_id == user_id,
                UserReports.user_reports_id.in_(user_reports_ids)
            )
        )

    page = max(1, page)
    if offset is None or offset < 0:
        offset = (page - 1) * limit if limit else None

    column_map = {
        "created_at": UserReports.created_at,
        "modified_at": UserReports.modified_at,
        "report_name": UserReports.report_name,
    }
    if column_order not in column_map:
        logger.warning("[list_user_reports][user(%s)] Invalid column_order: '%s', defaulting to 'created_at'", user_id, column_order)

    sort_column = column_map.get(column_order, UserReports.created_at)
    sort_func = asc if order.lower() == "asc" else desc

    # Use CASE WHEN to prioritize 'pending' report_status (when it's a valid JSON)
    pending_first = case(
        (UserReports.report_status["status_report"].astext == "pending", 0),
        else_=1
    )

    # Apply ordering: pending first, then user-defined sorting
    query = query.order_by(pending_first, sort_func(sort_column))

    if limit:
        query = query.limit(limit)
        if offset:
            query = query.offset(offset)
        logger.info("[list_user_reports][user(%s)] Limit: %s | Page: %s | Offset: %s", user_id, limit, page, offset)

    try:
        result = await db.execute(query)
        rows = result.mappings().all()
        logger.info("[list_user_reports][user(%s)] Found %s reports.", user_id, len(rows))
        return [dict(r) for r in rows]
    except Exception as e:
        logger.error("[list_user_reports][user(%s)] Error executing query: %s", user_id, e)
        raise


async def filter_using_hmac(db: AsyncSession, hmac_filter: str, hmac_column: str):
    user_reports_ids = []

    if hmac_filter:
        filters = []
        if hmac_filter:
            filters.append(UserColumnsHmac.hmac == hmac_filter)
        if hmac_column:
            filters.append(UserColumnsHmac.column_name == hmac_column)

        try:
            stmt = select(distinct(UserColumnsHmac.user_reports_id)).where(and_(*filters))
            result = await db.execute(stmt)
            user_reports_ids = [row[0] for row in result.fetchall()]
            logger.info("[filter_using_hmac] Fetched report_ids using hmac filter: %s | column: %s => %s", hmac_filter, hmac_column, user_reports_ids)

            if not user_reports_ids:
                logger.warning("[filter_using_hmac] No user_reports_id found with given filters: hmac=%s, column=%s", hmac_filter, hmac_column)
                return []

        except Exception as e:
            logger.error("[filter_using_hmac] Error executing HMAC filter query: %s", e)
            raise

    return user_reports_ids

async def insert_hmacs(db: AsyncSession, user_uuid: uuid.UUID, user_reports_id: str, hmac: dict):
    
    try:
        logger.info("[insert_hmacs] Inserting hmacs for user_reports_id: %s", user_reports_id)

        rows_to_insert = []
        for column_name, hmac_list in hmac['n-grams'].items():
            for hmac_value in set(hmac_list):
                rows_to_insert.append({
                    'hmac': hmac_value,
                    'column_name': column_name,
                    'user_reports_id': user_reports_id
                })


        if rows_to_insert:
            stmt = insert(UserColumnsHmac).values(rows_to_insert).on_conflict_do_nothing()
            await db.execute(stmt)
            await db.commit()

        logger.info("[insert_hmacs] Successfully inserted hmacs for user_reports_id: %s", user_reports_id)

    except Exception as e:
        logger.error("[insert_hmacs] Error inserting hmacs for user_reports_id %s: %s", user_reports_id, e)
        await db.rollback()
        raise



def normalize_text(text: str) -> str:
    """
    Lowercase, remove accents, strip special characters (except space).
    """
    logger.info("[normalize_text] Normalizing input text: %s", text)
    text = text.lower()
    text = ''.join(
        c for c in unicodedata.normalize('NFKD', text)
        if not unicodedata.combining(c)
    )
    text = re.sub(r'[^a-z0-9\s]', '', text)
    logger.info("[normalize_text] Result after normalization: %s", text)
    return text


def generate_ngrams(text: str, min_n: int = 3) -> List[str]:
    """
    Generate all n-grams of minimum length `min_n`.
    """
    logger.info("[generate_ngrams] Generating n-grams for: '%s' with min_n=%s", text, min_n)
    ngrams = []
    for n in range(min_n, len(text) + 1):
        for i in range(len(text) - n + 1):
            ngrams.append(text[i:i + n])
    logger.info("[generate_ngrams] Total %s n-grams generated.", len(ngrams))
    return ngrams


def extract_normalized_ngrams(json_data: Dict[str, Any], fields: List[str]) -> Dict[str, Dict[str, List[str]]]:
    """
    Process selected fields and return: {'n-grams': {field: [unique ngrams...]}}
    """
    logger.info("[extract_normalized_ngrams] Starting n-gram extraction for fields: %s", fields)
    result = {}

    for field in fields:
        value = json_data.get(field)
        logger.info("[extract_normalized_ngrams] Processing field: '%s' with value: %s", field, value)
        ngrams = []

        if isinstance(value, str):
            norm = normalize_text(value)
            ngrams.extend(generate_ngrams(norm))

        elif isinstance(value, dict):
            for v in value.values():
                if isinstance(v, str):
                    norm = normalize_text(v)
                    ngrams.extend(generate_ngrams(norm))
                elif isinstance(v, list):
                    for item in v:
                        if isinstance(item, str):
                            norm = normalize_text(item)
                            ngrams.extend(generate_ngrams(norm))

        unique_ngrams = list(set(ngrams))
        logger.info("[extract_normalized_ngrams] %s unique n-grams extracted for field '%s'.", len(unique_ngrams), field)
        result[field] = unique_ngrams

    return {"n-grams": result}

