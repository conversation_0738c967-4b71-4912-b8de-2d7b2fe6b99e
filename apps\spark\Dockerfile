FROM bitnami/spark:3.5.5
LABEL authors="gbrabelo"

ARG DEV_MODE=true
ARG INSTALL_PATH=/opt/spark-app/reports_processor/
ENV WAIT_ON_KAFKA=${INSTALL_PATH}/scripts/wait_until_kafka_alive.sh

COPY ./apps/spark/ ${INSTALL_PATH}
COPY ./scripts/wait_until_kafka_alive.sh ${INSTALL_PATH}/scripts/
WORKDIR $INSTALL_PATH
RUN pip install -r requirements.txt

COPY ./constants/* ${INSTALL_PATH}/reports_processor/


# precisaria rodar em outra imagem
#RUN pip install --upgrade pip setuptools wheel && \
#    if [ ${DEV_MODE} = "true" ]; then \
#      pip install -e .; \
#    else \
#      pip install .; \
#    fi

#CMD ["tail", "-f", "/dev/null"]
CMD ["/bin/bash", "scripts/spark_init.sh"]
