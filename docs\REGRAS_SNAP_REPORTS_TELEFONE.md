# REGRAS RELATÓRIO TELEFONE

## Seções existentes para o tipo de relatório "TELEFONE" e suas respectivas fontes de dados:

1. <PERSON><PERSON><PERSON>
     - <PERSON><PERSON>s de dados: "IRBIS"

2. <PERSON><PERSON>:
     - <PERSON><PERSON><PERSON> de dados: "SNAP"

3. Imagens
     <PERSON> <PERSON><PERSON>s de dados: "IRBISLuna"

4. Emails
     - <PERSON><PERSON>s de dados: "IRBIS", "IRBISLuna"

5. Endereços
     - Fontes de dados: "SNAP", "IRBISLuna"

6. Parentes
     <PERSON> <PERSON><PERSON>s de dados: "SNAP"

7. Empresas Relacionadas
     - Fontes de dados: "IRBISLuna"

8. Nomes de Usuário
     - Fontes de dados: "IRBISLuna"

9. Telefones
     - Fontes de dados: "IRBISLuna"

10. <PERSON><PERSON><PERSON> Redes Sociais
      - Fontes de dados: "IRBIS", "IRBISLuna"

11. Outras URLs
      - Fontes de dados: "IRBISLuna"

12. Possíveis Contatos
      - Fontes de dados: "IRBIS", "IRBISLuna"

13. Possíveis Pessoa<PERSON> Relacionadas
      - Fontes de dados: "IRBIS", "SNAP

14. Possíveis Contas em Sites
      - Fontes de dados: "ProvedorDeAplicacacaoDaInternettelefone"

## SEÇÃO: "Contatos Salvos"

- No resultado da chave **"IRBIS"**, buscar a lista com a chave **"alias"** (IRBIS[0].alias).
  - Não adicionar as propriedades:
    - "phone number",
    - "country code"
  - Somente adicionar objetos que tenham a propriedade "alias"

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Contatos Salvos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Contatos Salvos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
 "IRBIS": [
    {
        "alias": [
            {
                "alias": "Aversa Mp Csi Cibernetica",
                "country code": "BR",
                "origin": "getcontact",
                "phone number": "5521999891661"
            },
            {
                "alias": "João Aversa Mprj",
                "origin": "tc"
            },
            {
                "alias": "João Bernardo Dedit",
                "origin": "drupe"
            },
            {
                "origin": "eyecon"
            },
            {
                "origin": "hiya"
            },
            {
                "alias": "joão aversa",
                "origin": "callapp"
            }
        ]
    }
],
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Contatos Salvos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS"],
        "data_count": 4,
        "data": [
            {
                "alias": {
                    "value": "Aversa Mp Csi Cibernetica",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "detalhes": {
                    "origin": {
                        "value": "getcontact",
                        "label": "Origem",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                }
            },
            {
                "alias": {
                    "value": "João Aversa Mprj",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "detalhes": {
                    "origin": {
                        "value": "tc",
                        "label": "Origem",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                }
            },
            {
                "alias": {
                    "value": "João Bernardo Dedit",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "detalhes": {
                    "origin": {
                        "value": "drupe",
                        "label": "Origem",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                }
            },
            {
                "alias": {
                    "value": "joão aversa",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "detalhes": {
                    "origin": {
                        "value": "callapp",
                        "label": "Origem",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Dados Pessoais"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.

- Adicionar as propriedades (chave e valor) deste objeto na seção "Dados Pessoais", sem listas e objetos, apenas os valores primitivos. Existem algumas propriedades que não devem ser adicionadas nesse processo. São elas:
  - "bookmark"
  - "first names"
  - "surname"
  - "pessoa" (já que se trata de uma lista)

- No objeto com propriedade **"bookmark": 4**, buscar na lista com chave **"pessoa"** um objeto com a propriedade **"label default key"** com valor que contenha o termo **"mae"**.
  - Adicionar a propriedade **"full name"** deste objeto à seção "Dados Pessoais", porém trocando a chave para **"nome_da_mae"**. Este passo é necessário pois o nome da mãe pode ser determinante para identificar e diferenciar as pessoas de interesse.

- Modificar valores de propriedades específicas:
  - "sexo" retorna apenas a inicial, sendo necesário atribuir a palavra completa (ex.: "F" --> "Feminino", "M" --> "Masculino")

**Regras para a formatação da seção:**

- Ordenar as propriedades inseridas na seção "Dados Pessoais" colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "full name"
  - "nome_da_mae" (inserido no passo anterior)
  - "sexo"
  - "nacionalidade" OU "pais do passaporte"
  - "data nascimento" OU "dt_nascimento"
  - "cpf"
  - "identidade"
  - restante das propriedades...

- O objeto que constitui a seção **"Dados Pessoais"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Dados Pessoais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
 "SNAP": [
            {
                "endpoint": "SNAP",
                "pessoa": [
                    {
                        "bookmark": 4,
                        "cpf": "10475224701",
                        "data nascimento": "1984-10-21",
                        "first names": "BIANCA",
                        "full name": "BIANCA PARREIRA CABRAL LOPES",
                        "location": [
                            {
                                "area": "RJ",
                                "bairro": "FREGUESIA ILHA DO GOVERNADOR",
                                "cep ou zipcode": "21911170",
                                "city": "RIO DE JANEIRO",
                                "complemento": "CS",
                                "endereco": "RUA DO ROMANCISTA",
                                "nome": "RUA DO ROMANCISTA"
                            }
                        ],
                        "pessoa": [
                            {
                                "full name": "NORMA PERREIRA",
                                "label default key": "parente MAE"
                            }
                        ],
                        "phonenumber": [
                            {
                                "phone number": "5521999891661"
                            }
                        ],
                        "sexo": "F",
                        "surname": "PARREIRA CABRAL LOPES"
                    }
                ]
            }
        ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Dados Pessoais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": ,
        "data": [
            {
             "detalhes": {
                    "full name": {
                        "value": "BIANCA PARREIRA CABRAL LOPES",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "nome_da_mae": {
                        "value": "NORMA PERREIRA",
                        "label": "Nome da Mãe",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cpf": {
                        "value": "104.752.247-01",
                        "label": "CPF",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "sexo": {
                        "value": "Feminino",
                        "label": "Sexo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "data nascimento": {
                        "value": "1984-10-21",
                        "label": "Data de Nascimento",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                }
            }
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Imagens"

- No resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"imagem"**  (IRBISLuna[0].pessoa[n].imagem).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Imagens"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Imagens"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBISLuna"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "endpoint": "IRBISLuna",
            "pessoa": [
                {
                    "bookmark": 4,
                    "imagem": [
                        {
                            "url": "https://facebook.com/ihsauhdd.jpg"
                        }
                    ]
                }
            ]
            //outros resultados}
        }
    ],

}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Imagens",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBISLuna"],
        "data_count": 1,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "url": {
                                "value": "https://facebook.com/ihsauhdd.jpg",
                                "label": "URL da Imagem",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>


## SEÇÃO: "Emails":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> chave **"phonenumber"** (IRBIS[0].phonenumber).
  - propriedade: "e-mail"

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"pessoa"** -> lista com chave **"emailaddress"** (IRBISLuna[0].pessoa[n].emailaddress).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.
  - propriedades:
    - "email address"
    - "tipo"
    - "email valido"
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Emails"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Emails"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBIS", "IRBISLuna"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
        {
            "endpoint": "IRBIS",
            "phonenumber":[
                {
                    "cidade": "Rio de Janeiro",
                    "data nascimento": "21/10/1984",
                    "e-mail": "<EMAIL>",
                    "estado ou regiao": "BR_MG",
                    "first names": "Bianca",
                    "full name": "BIANCA PARREIRA CABRAL-LOPES",
                    "pais": "BR",
                    "phone number": "5521999891661",
                    "sexo": "F",
                    "surname": "PARREIRA CABRAL-LOPES"
                }      
            ]
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Emails",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS", "IRBISLuna"],
        "data_count": 1,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "email address": {
                                "value": "<EMAIL>",
                                "label": "Email",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                        },
                        "label": "Email",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                ]
            }
            
        ]
    }
    //outras seções
]
```
</div> 
</div>


## SEÇÃO: "Endereços"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"** ->  lista com a chave **"location"** (SNAP[0].location).  
- No resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"endereco"** (IRBISLuna[0].pessoa[n].endereco).   

- Adicionar todos os objetos provenientes dessas listas no **"data"** da seção **"Endereços"**.

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "label default key"


**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Endereços"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Endereços"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "IRBISLuna"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
         {
            "endpoint": "SNAP",
            "location": [
                    {
                        "area": "RJ",
                        "bairro": "FREGUESIA ILHA DO GOVERNADOR",
                        "cep ou zipcode": "21911170",
                        "city": "RIO DE JANEIRO",
                        "complemento": "CS",
                        "endereco": "RUA DO ROMANCISTA",
                        "nome": "RUA DO ROMANCISTA"
                    }
            ]
        }
    ],
    "IRBIS": [
        {
            "pessoa": [
                {
                    "endereco": [
                        {
                            "cep ou zipcode": "21910087",
                            "cidade": "Rio de Janeiro",
                            "complemento": "C",
                            "estado ou regiao": "BR_RJ",
                            "pais": "BR",
                            "logradouro": "RUA AMÉLIA THRETAS"
                        }
                    ],
                }
            ],
            "endpoint": "IRBIS"
        }
    ]          
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Endereços",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "IRBISLuna"], // listar todas as chaves usadas como fonte
        "data_count": 2,
        "data": [
           {
                "detalhes": [
                    {
                        "value": {
                            "logradouro": {
                                "value": "RUA DO ROMANCISTA",
                                "label": "Logradouro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "complemento": {
                                "value": "CS",
                                "label": "Complemento",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "FREGUESIA ILHA DO GOVERNADOR",
                                "label": "Bairro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cidade": {
                                "value": "RIO DE JANEIRO",
                                "label": "Cidade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "estado ou regiao": {
                                "value": "BR_RJ",
                                "label": "Estado",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "21911170",
                                "label": "CEP",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Endereço",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "logradouro": {
                                "value": "RUA AMÉLIA THRETAS",
                                "label": "Logradouro",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "complemento": {
                                "value": "C",
                                "label": "Complemento",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "FREGUESIA ILHA DO GOVERNADOR",
                                "label": "Bairro",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "city": {
                                "value": "RIO DE JANEIRO",
                                "label": "Cidade",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "21910087",
                                "label": "CEP",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "area": {
                                "value": "RJ",
                                "label": "Estado",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            }
                        },
                        "label": "Endereço",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Parentes"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"**, buscar a lista com a chave **"pessoa"** (SNAP[0].pessoa).
- Buscar no resultado da chave **"SNAP"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.
  - Buscar a lista com chave **"pessoa"** deste objeto (SNAP[0].pessoa["pessoa com bookmark 4"].pessoa).

- Adicionar os objetos provenientes dessas listas no **"data"** da seção **"Parentes"** SE o objeto se enquadrar em alguma das regras abaixo:
  - A propriedade **"label default key"** OU **"grau de parentesco"** CONTEM algum dos seguintes termos: ["MAE", "IRMA", "PAI", "TIO", "AVOS", "FILHO", "CONJUGE"]
    - Alterar em cada objeto que cair nessa regra, a propriedade **"label default key"** OU **"grau de parentesco"** para **"parentesco"**  

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "bookmark"
   - "surname"
   - "first names"
   - "credilink label"
   - "pessoa"
   - "location"
   - "phonenumber"

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Parentes"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Parentes"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção. **Segue abaixo o formato correto para cada objeto:**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "parentesco"
- "detalhes"
- "telefones"
- "enderecos"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": 
    [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cpf": "10475224701",
                    "data nascimento": "1984-10-21",
                    "first names": "BIANCA",
                    "full name": "BIANCA PARREIRA CABRAL LOPES",
                    "location": [
                        {
                            "area": "RJ",
                            "bairro": "FREGUESIA ILHA DO GOVERNADOR",
                            "cep ou zipcode": "21911170",
                            "city": "RIO DE JANEIRO",
                            "complemento": "CS",
                            "endereco": "RUA DO ROMANCISTA",
                            "nome": "RUA DO ROMANCISTA"
                        }
                    ],
                    "pessoa": [
                        {
                            "full name": "NORMA PERREIRA",
                            "label default key": "parente MAE"
                        }
                    ]
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Parentes",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": 1,
        "data": [
           {
                "parentesco": {
                    "value": "parente MAE",
                    "label": "Parentesco",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "full name": {
                        "value": "NORMA PERREIRA",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                }
           }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Empresas Relacionadas"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"pessoa"** -> lista com chave **"empresa"** (IRBISLuna[0].pessoa[n].empresa).
   - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**

- Adicionar todos os objetos das lista acima no **"data"** da seção **"Empresas Relacionadas"**.
  
**Regras para a formatação da seção:**

- Ordenar as propriedades de cada objeto inserido na seção "Empresas Relacionadas", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "razao social"
  - "rotulo"
  - "cargo"
  - "formacao educacional"
  - "data de inicio"
  - "data_inicio"
  - "data inicio"
  - "data de termino"
  - "data termino"
  - "data_termino"
  - ...restante das propriedades...


- O objeto que constitui a seção **"Empresas Relacionadas"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Empresas Relacionadas"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBISLuna"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "razao_social",
- "detalhes",
- "enderecos",
- "telefones",
- "emails"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "endpoint": "IRBIS",
            "pessoa": [
                {
                    "bookmark": 4,
                    "empresa": [
                        {
                            "razao social": "SENAC-RIO",
                            "vinculo": [
                                {
                                    "formacao educacional": "Redes de Computadores",
                                    "rotulo": "Vinculo Educacional"
                                }
                            ]
                        },
                        {
                            "razao social": "Universidade Federal do Rio de Janeiro",
                            "vinculo": [
                                {
                                    "data inicio": "01/01/2003",
                                    "data termino": "31/12/2007",
                                    "formacao educacional": "Analise de Sistema",
                                    "rotulo": "Vinculo Educacional"
                                }
                            ]
                        }
                    ],
                }
            ]
        }
    ]
    //outros retornos
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Empresas Relacionadas",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBISLuna"],
        "data_count": 2,
        "data": [
            {
                "razao_social": {
                    "value": "SENAC-RIO",
                    "label": "Razão Social",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
                "detalhes": {
                    "formacao educacional": {
                        "value": "Redes de Computadores",
                        "label": "Formação Educacional",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "rotulo": {
                        "value": "Vinculo Educacional",
                        "label": "Vínculo",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                }
            },
            {
                "razao_social": {
                    "value": "Universidade Federal do Rio de Janeiro",
                    "label": "Razão Social",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data_inicio": {
                        "value": "01/01/2003",
                        "label": "Data de Início",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "data_termino": {
                        "value": "31/12/2007",
                        "label": "Data de Término",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "formacao_educacional": {
                        "value": "Analise de Sistema",
                        "label": "Formação Educacional",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "rotulo": {
                        "value": "Vinculo Educacional",
                        "label": "Vínculo",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Nomes de Usuário":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"pessoa"** -> lista com chave **"alias"** (IRBISLuna[0].pessoa[n].alias).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Nomes de Usuário"**.
- Trocar o valor de todas as chaves **"alias"** para **"nome de usuario"** mais o index + 1.
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Nomes de Usuário"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Nomes de Usuário"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBISLuna"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "endpoint": "IRBISLuna",
            "pessoas": {
               "alias": [
                    {
                        "alias": "bianca.parreira"
                    },
                    {
                        "alias": "Bianca_Parreira_123"
                    },
                    {
                        "alias": "bianca-765343"
                    }
                ]
            }
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Nomes de Usuário",
        "subtitle": "Dados consultados na API SNAP.",
        "data_count": 3,
        "source": ["IRBISLuna"],
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "alias": {
                                "value": "bianca.parreira",
                                "label": "Nome de Usuário",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Nome de Usuário",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "alias": {
                                "value": "Bianca_Parreira_123",
                                "label": "Nome de Usuário",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Nome de Usuário",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "alias": {
                                "value": "bianca-765343",
                                "label": "Nome de Usuário",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Nome de Usuário",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                ]
            }
            //outros objetos da seção
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Telefones":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"pessoa"** -> lista com chave **"phonenumber"** (IRBISLuna[0].pessoa[n].phonenumber).
  - Somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Telefones"**.
- Trocar o valor de todas as chaves **"phone number"** para **"Email"** mais o index + 1.
  
**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Telefones"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Telefones"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["IRBISLuna"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "endpoint": "IRBISLuna",
            "phonenumber": [
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521987511944"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521999891661"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521973931057"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "5521996761661"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "552133960539"
                },
                {
                    "area code": "51",
                    "country code": "55",
                    "phone number": "555135624382"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "552135461661"
                },
                {
                    "area code": "21",
                    "country code": "55",
                    "phone number": "552139232200"
                }
            ],
            //outras propriedades
        }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Telefones",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBISLuna"],
        "data_count": 7,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "phone number": {
                                "value": "5521987511944",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5521999891661",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5521973931057",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5521996761661",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "552133960539",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {  
                            "phone number": {
                                "value": "555135624382",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "552135461661",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "552139232200",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Perfis Redes Sociais":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"skype"** (IRBIS[0].skype).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"telegram"** (IRBIS[0].telegram).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"whatsapp"** (IRBIS[0].whatsapp).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"viber"** (IRBIS[0].viber).
- Buscar no resultado da chave **"IRBIS"** -> lista com a chave **"perfil"** (IRBIS[0].perfil).
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"facebook"** (IRBISLuna[0].pessoa[n].facebook), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"instagram"** (IRBISLuna[0].pessoa[n].instagram), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"linkedin"** (IRBISLuna[0].pessoa[n].linkedin), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"skype"** (IRBISLuna[0].pessoa[n].skype), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"snapchat"** (IRBISLuna[0].pessoa[n].snapchat), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"telegram"** (IRBISLuna[0].pessoa[n].telegram), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"whatsapp"** (IRBISLuna[0].pessoa[n].whatsapp), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"googleplus"** (IRBISLuna[0].pessoa[n].googleplus), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.
- Buscar no resultado da chave **"IRBISLuna"** -> lista com a chave **"pessoa"** -> lista com a chave **"perfil"** (IRBISLuna[0].pessoa[n].perfil), somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Perfis Redes Sociais"** e que será utilizado para a construção dinâmica de sua interface precisa conter as seguintes propriedades e valores:
  - **"title":** "Perfis Redes Sociais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS", "IRBISLuna"]
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "telegram"
- "whatsapp"
- "googleplus"
- "perfil"
- "facebook"
- "instagram"
- "linkedin"
- "skype"
- "snapchat"
- "viber"
- outras redes...

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBIS": [
         {
            "endpoint": "IRBIS",
            "telegram": [
                {
                    "nome": "Bianca Parreira",
                    "nome no perfil": "BiancaParreira"
                }
            ],
            "linkedin": [
                {
                    "imagem de perfil": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/", // ... imagem em base64
                    "nome": "Bianca Parreira"
                }
            ]
            //outras propriedades            
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Perfis Redes Sociais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS", "IRBISLuna"], // listar todas as chaves usadas como fonte
        "data": [
           {
                "telegram": [
                    {
                        "value": {
                            "nome": {
                                "value": "Bianca Parreira",
                                "label": "Nome",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "nome no perfil": {
                                "value": "BiancaParreira",
                                "label": "Nome no Perfil",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            }
                        },
                        "label": "Telegram",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    },
                ],
                "linkedin": [
                    {
                        "value": {
                            "imagem de perfil": {
                                "value": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/", // ... imagem em base64
                                "label": "Imagem de Perfil",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            },
                            "nome": {
                                "value": "Bianca Parreira",
                                "label": "Nome",
                                "source": ["IRBIS"],
                                "is_deleted": false
                            }
                        },
                        "label": "Linkedin",
                        "source": ["IRBIS"],
                        "is_deleted": false
                    },
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Outras URLs":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"url"** (IRBISLuna[0].pessoa[n].url) **com condição**: somente adicionar objeto que tenham a propriedade **"bookmark"** igual a **4**.
- Adicionar todos os objetos desta lista no **"data"** da seção **"Outras URLs"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Outras URLs"** e que será utilizado para a construção dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Outras URLs"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBISLuna"]
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "pessoa": [
                {
                    "bookmark": 4,
                    "url": [
                        {
                            "url": "example.com",
                            "dominio url1": "example1.com",
                            "dominio url2": "example2.com"
                            // ... outras propriedades
                        }
                    ]
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Outras URLs",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBISLuna"],
        "data": [
           {
                "detalhes": [
                    {
                        "value": {
                            "url": {
                                "value": "example.com",
                                "label": "URL",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false,
                    },
                    {
                        "value": {
                            "url": {
                                "value": "example1.com",
                                "label": "URL",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false,
                    },
                    {
                        "value": {
                            "url": {
                                "value": "example2.com",
                                "label": "URL",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false,
                    }
                ]
           }
        ]
    }
]

```
</div> 
</div>




## SEÇÃO: "Possíveis Contatos":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBIS"** -> lista com chave **"telefone"** (IRBIS[0].telefone).

- Buscar no resultado da chave **"IRBISLuna"** -> dentro da lista com chave **"pessoa"**, buscar a lista com chave **"pessoa"** (IRBISLuna[0].pessoa[n].pessoa) **com condição**: somente adicionar objetos que tenham a propriedade **"bookmark"** igual a **4**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contatos"** e que será utilizado para a construção dinâmica da interface precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contatos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS", "IRBISLuna"]
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "nome_completo"
- "detalhes"
- "telefones"
- "enderecos"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
        {
            "pessoa": [
                {
                    "bookmark": 4,
                    "pessoa": [
                        {
                            "data nascimento": "06/02/1992",
                            "full name": "Hayra Alias Pereira",
                            "label default key": "family Niece",
                            "phonenumber": [
                                {
                                    "area code": "47",
                                    "country code": "55",
                                    "phone number": "5547988208342"
                                }
                            ]
                        },
                        {
                            "data nascimento": "21/11/1969",
                            "full name": "Nara Pereira",
                            "label default key": "family Brother",
                            "phonenumber": [
                                {
                                    "area code": "47",
                                    "country code": "55",
                                    "phone number": "5547988118802"
                                }
                            ]
                        },
                        {
                            "data nascimento": "06/07/1988",
                            "full name": "Soraia Pereira Brasil",
                            "label default key": "family Brother",
                            "phonenumber": [
                                {
                                    "area code": "92",
                                    "country code": "55",
                                    "phone number": "5592992957554"
                                }
                            ]
                        },
                        {
                            "data nascimento": "13/12/1945",
                            "full name": "Norma Perreira",
                            "label default key": "family Mother",
                            "phonenumber": [
                                {
                                    "area code": "51",
                                    "country code": "55",
                                    "phone number": "5551984703292"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}

```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contatos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBIS", "IRBISLuna"],
        "data_count": 7,
        "data": [
            {
                "nome_completo": {
                    "value": "Hayra Alias Pereira",
                    "label": "Nome Completo",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
                "detalhes": [
                    {
                        "value": {
                            "data nascimento": {
                                "value": "06/02/1992",
                                "label": "Data de Nascimento",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Detalhes",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "label default key": {
                                "value": "family Niece",
                                "label": "Parentesco",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Detalhes",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                ],
                "telefones": [
                    {
                        "value": {
                            "phone number": {
                                "value": "5547988208342",
                                "label": "Número do Telefone",
                                "source": ["IRBISLuna"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                ],
            },
            // Outros objetos da seção      
        
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Possíveis Pessoas Relacionadas":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"IRBISLuna"** -> lista com chave **"pessoa"** (IRBISLuna[0].pessoa)  
  - Condição: somente adicionar objetos que atendam a **"bookmark!=4"** ou que tenham **"bookmark"** como indefinido.

- Buscar no resultado da chave **"SNAP"** -> lista com chave **"pessoa"** (SNAP[0].pessoa)  
  - Condições: somente adicionar objetos que satisfaçam todas as seguintes restrições:
    - A propriedade **"label default key"** não contenha nenhum dos seguintes termos:  
      ["parente MAE", "parente IRMA(O)", "parente PAI", "parente TIO(A)", "parente AVOS", "parente FILHO(A)", "parente CONJUGE"].
    - A propriedade **"grau de parentesco"** não contenha "MAE".
    - A propriedade **"bookmark"** não seja igual a **4**.

- Não adicionar as seguintes propriedades dos objetos processados:  
  "bookmark", "surname", "first names", "credilink label", "label default key", "pessoa".

- Adicionar todos os objetos destas listas no **"data"** da seção **"Possíveis Pessoas Relacionadas"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Pessoas Relacionadas"** e que será utilizado para a construção dinâmica da sua interface deverá conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Pessoas Relacionadas"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["IRBIS", "SNAP"]  // lista com as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "nome_completo"
- "detalhes"
- "telefones"
- "imagens" (retorno somente para o IRBISLuna)
- "enderecos"
- "redes_sociais" (retorno somente para o IRBISLuna)

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "IRBISLuna": [
       {
            "endpoint": "IRBISLuna",
            "pessoa": [
                {
                    "data nascimento": "26/12/1984",
                    "descricao": "contact_details",
                    "first names": "JOAO",
                    "full name": "JOAO AVERSA",
                    "full name1": "JOAO BERNARDO DEDIT",
                    "location": [
                        {
                            "pais": "BR"
                        },
                        {
                            "pais": "BR"
                        }
                    ],
                    "phonenumber": [
                        {
                            "area code": "21",
                            "country code": "55",
                            "phone number": "5521999891661"
                        },
                        {
                            "area code": "21",
                            "country code": "55",
                            "phone number": "5521985592439"
                        },
                        {
                            "area code": "21",
                            "country code": "55",
                            "phone number": "5521999891661"
                        }
                    ],
                    "surname": "AVERSA"
                },
                {
                    "descricao": "personal_profiles",
                    "dominio url": "http://www.facebook.com/people/_/100001296267978",
                    "facebook": [
                        {
                            "id": "100001296267978"
                        },
                        {
                            "link": "http://www.facebook.com/joao.aversa.3",
                            "nome de usuario": "joao.aversa.3"
                        }
                    ],
                    "twitter": [
                        {
                            "link": "http://www.twitter.com/joao_aversa",
                            "nome de usuario": "joao_aversa"
                        }
                    ],
                    "first names": "JOAO",
                    "full name": "JOAO AVERSA",
                    "sexo": "Masculino",
                    "surname": "AVERSA"
                },
                {
                    "descricao": "contact_details",
                    "descricao1": "personal_profiles",
                    "dominio url": "https://plus.google.com/106701897403823646843/about",
                    "first names": "JOAO",
                    "full name": "JOAO BERNARDO AVERSA",
                    "imagem": [
                        {
                            "url": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720"
                        }
                    ],
                    "location": [
                        {
                            "pais": "BR"
                        }
                    ],
                    "perfil": [
                        {
                            "id": "106701897403823646843"
                        }
                    ],
                    "phonenumber": [
                        {
                            "area code": "21",
                            "country code": "55",
                            "phone number": "5521996761661"
                        }
                    ],
                    "sexo": "Masculino",
                    "surname": "BERNARDO AVERSA"
                }
            ]
       }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Pessoas Relacionadas",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["IRBISLuna", "SNAP"],
        "data_count": 7,
        "data": [
                {
                    "nome_completo": {
                        "value": "JOAO AVERSA",
                        "label": "Nome Completo",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "detalhes": [
                        {
                            "value": {
                                "data nascimento": {
                                    "value": "26/12/1984",
                                    "label": "Data de Nascimento",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                            },
                            "label": "Detalhes",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        }
                    ],
                    "telefones": [
                        {
                            "value": {
                                "phone number": {
                                    "value": "5521999891661",
                                    "label": "Número do Telefone",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                            },
                            "label": "Telefone",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        },
                        {
                            "value": {
                                "phone number": {
                                    "value": "5521985592439",
                                    "label": "Número do Telefone",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                            },
                            "label": "Telefone",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        },
                        {
                            "value": {
                                "phone number": {
                                    "value": "5521999891661",
                                    "label": "Número do Telefone",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                            },
                            "label": "Telefone",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        }
                    ],
                    "enderecos": [
                        {
                            "value": {
                                "logradouro": {
                                    "value": "AVENIDA PAULISTA",
                                    "label": "Logradouro",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "numero": {
                                    "value": "1000",
                                    "label": "Número",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "bairro": {
                                    "value": "BELA VISTA",
                                    "label": "Bairro",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "city": {
                                    "value": "SÃO PAULO",
                                    "label": "Cidade",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "area": {
                                    "value": "SP",
                                    "label": "Área",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "cep ou zipcode": {
                                    "value": "01310100",
                                    "label": "CEP",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                }
                            },
                            "label": "Endereço",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        }
                    ],
                    "redes_sociais": {
                        "facebook": {
                            "value": {
                                "link": {
                                    "value": "http://www.facebook.com/joao.aversa.3",
                                    "label": "Link",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "nome de usuario": {
                                    "value": "joao.aversa.3",
                                    "label": "Nome de Usuário",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "id": {
                                    "value": "100001296267978",
                                    "label": "ID",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                }
                            },
                            "label": "Facebook",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        },
                        "twitter": {
                            "value": {
                                "link": {
                                    "value": "http://www.twitter.com/joao_aversa",
                                    "label": "Link",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                },
                                "nome de usuario": {
                                    "value": "joao_aversa",
                                    "label": "Nome de Usuário",
                                    "source": ["IRBISLuna"],
                                    "is_deleted": false
                                }
                            },
                            "label": "Twitter",
                            "source": ["IRBISLuna"],
                            "is_deleted": false
                        },
                    }
            }
        ]   
    }
]
```
</div> 
</div>


## SEÇÃO: "Possíveis Contas em Sites":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"ProvedorDeAplicacacaoDaInternettelefone"** -> lista **"provedor de aplicacoes de internet"** (ProvedorDeAplicacacaoDaInternettelefone[0]["provedor de aplicacoes de internet"])
    - Alterar o nome das seguintes chaves:
      - "found" --> "conta encontrada?"
      - "alerta" --> "alvo alertado?"
      - "tipo_alerta" --> "tipo de alerta"
  
  - Adicionar propriedade em cada objeto com o valor do Telefone de entrada e com a chave "Entrada TELEFONE". Ex.: {"entrada_telefone": "5521999891661"}

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contas em Sites"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contas em Sites"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["ProvedorDeAplicacacaoDaInternettelefone"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- O objeto principal contém uma propriedade `detalhes` que é um array de itens
- Cada item contém:
  - `site`: Informação sobre o site (aplicação)
  - `detalhes`: Objeto com informações detalhadas (found, entrada_cpf, alerta, tipo_alerta)

### Renderização:
- Os sites são renderizados em uma grade de duas colunas
- Cada site é exibido com um número sequencial (Site 1, Site 2, etc.)
- Um ícone verde (check) é exibido quando a conta foi encontrada, vermelho (error) quando não
- Os detalhes são exibidos abaixo do nome do site
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "data": {
        "cpf": {
            "ProvedorDeAplicacacaoDaInternettelefone": 
            [
                {
                    "endpoint": "ProvedorDeAplicacacaoDaInternettelefone",
                    "provedor de aplicacoes de internet": [
                        {
                            "alerta": "Não",
                            "aplicacao": "casasbahia",
                            "found": "Não",
                            "tipo_alerta": "Não se Aplica"
                        },
                        {
                            "alerta": "Não",
                            "aplicacao": "Estacionamento Digital",
                            "found": "Sim",
                            "tipo_alerta": "Não se Aplica"
                        }
                    ]
                }
            ],
            //outros resultados}
        }
    },
    "searchArgs": {
        "cpf": [
            "10748896732"
        ]
    }
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contas em Sites",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
        "data_count": 2,
        "data": [
            {
                "site": {
                    "value": "casasbahia",
                    "label": "Site",
                    "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
                    "is_deleted": false
                },
                "detalhes": {
                    "entrada_telefone": {
                        "value": "5521999891661",
                        "label": "Entrada TELEFONE",
                        "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
                        "is_deleted": false
                    },
                    "found": {
                        "value": "Não",
                        "label": "Conta encontrada?",
                        "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
                        "is_deleted": false
                    },
                    "alerta": {
                        "value": "Não",
                        "label": "Alvo alertado?",
                        "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
                        "is_deleted": false
                    },
                    "tipo_alerta": {
                        "value": "Não se Aplica",
                        "label": "Tipo de Alerta",
                        "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>