import React from 'react';
import { View, Text, StyleSheet, Svg, Rect, Image } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { isValidUrl, isBase64Image } from '../helpers.strategy';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintRedesSociaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      [socialMedia: string]: Array<{
        value: Record<string, ValueWithSource>;
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const RenderPrintRedesSociais: React.FC<RenderPrintRedesSociaisProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((socialMediaData, dataIndex) => (
        <View key={`data-${dataIndex}`} style={styles.socialMediaContainer}>
          {Object.entries(socialMediaData).map(([platform, profiles]) => {
            if (!Array.isArray(profiles) || profiles.length === 0) return null;

            const platformName = platform.charAt(0).toUpperCase() + platform.slice(1);

            return (
              <View key={`platform-${platform}`} style={styles.platformContainer}>
                <View style={styles.platformTitleContainer}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.platformTitle}>{platformName.toUpperCase()}</Text>
                </View>

                <View style={styles.profilesGrid}>
                  {profiles
                    .filter(profile => !profile.is_deleted)
                    .map((profile, profileIndex) => (
                      <View key={`profile-${profileIndex}`} style={styles.profileBlock} >
                        <View style={styles.profileHeaderContainer}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill='#889EA3' />
                          </Svg>
                          <Text style={styles.profileTitle}>
                            {(profile.label || platformName).toUpperCase()} {profileIndex + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {profile.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>

                        <View style={styles.fieldsContainer}>
                          {Object.entries(profile.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => {
                              const imageUrl = String(fieldValue.value || "");
                              //const isHttpUrl = isValidUrl(imageUrl) && !isBase64Image(imageUrl);
                              
                              return (
                                <View key={`field-${fieldIndex}`} style={styles.fieldItem}>
                                  <View style={styles.infoContainer}>
                                    <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                      <Rect width="8" height="8" fill='#CCCCCC' />
                                    </Svg>
                                    <Text style={styles.label}>
                                      {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                    </Text>
                                    <Text style={styles.sourceText}>
                                      {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                    </Text>
                                  </View>
                                  
                                  <Text style={styles.value}>
                                    {String(fieldValue.value || "")}
                                  </Text>
                                  
                                 {/*  {isHttpUrl && (
                                    <Text style={styles.urlText}>{imageUrl}</Text>
                                  )} */}
                                  
                                  {isValidUrl(imageUrl) && !isBase64Image(imageUrl) && (
                                    <View style={styles.imageContainer} >
                                      <Image src={imageUrl} style={styles.image} />
                                    </View>
                                  )}
                                  {/* {isBase64Image(imageUrl) && (
                                    <View style={styles.imageContainer}>
                                      <Text style={styles.imageNotice}>[Imagem em base64 disponível]</Text>
                                    </View>
                                  )} */}
                                </View>
                              );
                            })}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            );
          })}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  imageNotice: {
    fontSize: 9,
    color: '#889EA3',
    fontStyle: 'italic',
  },
  searchIcon: { 
    width: 4, 
    height: 4, 
    marginRight: 4, 
    marginTop: 1 
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  socialMediaContainer: {
    marginBottom: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  platformContainer: {
    marginBottom: 12,
    padding: 8,
  },
  platformTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  platformTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  profilesGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  profileBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  profileHeaderContainer: {
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
    marginBottom: 4,
  },
  profileTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  fieldsContainer: {
    paddingLeft: 8,
    paddingTop: 4,
  },
  fieldItem: {
    marginBottom: 6,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  urlText: {
    paddingLeft: 8,
    fontSize: 8,
    marginBottom: 4,
    color: '#889EA3',
    maxWidth: '100%',
  },
  imageContainer: {
    paddingLeft: 8,
    marginTop: 4,
    alignItems: 'flex-start',
  },
  image: {
    width: 'auto',
    height: 'auto',
    maxWidth: '100%',
    maxHeight: 200,
    alignSelf: 'flex-start',
  }
});