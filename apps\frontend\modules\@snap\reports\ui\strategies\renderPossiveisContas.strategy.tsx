import React from "react";
import { PossivelConta } from "../../model/PossiveisContas";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel, Icon, GridItem } from "@snap/design-system";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderPossiveisContas implements RenderStrategy<PossivelConta> {
  validateKeys = (keys: Array<keyof PossivelConta>): boolean => {
    return keys.some((campo) => {
      return campo in this.formatByKey;
    });
  };

  renderSiteCard = (possiveisContas: PossivelConta) => {
    if (!possiveisContas?.detalhes || !possiveisContas?.site) return null;

    const existeValue = parseValue(possiveisContas.detalhes.existe.value || "") || parseValue(possiveisContas.detalhes.found.value || "");
    const imageIcon = existeValue === "Sim" ? "/icons/icone_check.svg" : "/icons/icone_error.svg";
    const imageColor = existeValue === "Sim" ? "text-green-400" : "text-red-500";

    return (
      <div className="mb-4">
        <CustomReadOnlyInputField
          label={`${(possiveisContas.site?.label || "Site").toUpperCase()}`}
          colorClass="bg-accent"
          labelTextClass="text-accent"
          value={""}
          tooltip={renderSourceTooltip(possiveisContas.site.source)}
        />
        <div className="flex items-center gap-2 pt-2">
          {
            possiveisContas.detalhes?.existe?.value && (
              <Icon
                src={imageIcon}
                className={imageColor}
              />
            )
          }
          <CustomReadOnlyInputField
            value={String(possiveisContas.site?.value || "")}
            tooltip={renderSourceTooltip(possiveisContas.site.source)}
          />
        </div>
        <div className="pl-5">
          {Object.entries(possiveisContas.detalhes).map(
            ([key, value]) => (
              <GridItem
                key={`conta-${key}`}
                cols={1}
                className="py-2 group"
              >
                <CustomReadOnlyInputField
                  label={`${(value.label || translatePropToLabel(key)).toUpperCase()}`}
                  value={parseValue(String(value.value || ""))}
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  tooltip={renderSourceTooltip(value.source)}
                />
              </GridItem>
            )
          )}
        </div>
      </div>
    );
  };

  formatByKey: Record<
    string,
    (possiveisContas?: PossivelConta) => React.ReactElement | null
  > = {
      detalhes: (possiveisContas?: PossivelConta) => {
        if (!possiveisContas?.detalhes || !possiveisContas?.site) return null;

        return (

          this.renderSiteCard(possiveisContas)

        );
      }
    };

  render = (data: PossivelConta | PossivelConta[]): React.ReactElement[] => {
    if (Array.isArray(data)) {
      return [
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "1rem", width: "100%" }}>
          {data.map((site, index) => (
            <div key={`site-${index}`} style={{ gridColumn: "span 1" }}>
              {this.renderSiteCard(site)}
            </div>
          ))}
        </div>
      ];
    }

    const keys = Object.keys(data) as Array<keyof PossivelConta>;
    const orderedKeys: Array<keyof PossivelConta> = ['site', 'detalhes'];
    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => {
        if (chave in this.formatByKey) {
          return this.formatByKey[chave]?.(data);
        }
        return null;
      })
      .filter((el): el is React.ReactElement => el !== null);
  };
}
