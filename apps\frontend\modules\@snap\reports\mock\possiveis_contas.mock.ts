import { PossivelConta } from "../model/PossiveisContas";

// Mock for individual items
const possivelContaPAIs1: PossivelConta = {
    site: {
        value: "casasbahia",
        label: "Site",
        source: ["PAIscpf"],
        is_deleted: false
    },
    detalhes: {
        "entrada_cpf": {
            "value": "10748896732",
            "label": "Entrada CPF",
            "source": ["PAIscpf"],
            is_deleted: false
        },
        "found": {
            value: "Não",
            label: "Conta encontrada?",
            source: ["PAIscpf"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["PAIscpf"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["PAIscpf"],
            is_deleted: false
        }
    }
};

const possivelContaPAIs2: PossivelConta = {
    site: {
        value: "Estacionamento Digital",
        label: "Site",
        source: ["PAIscpf"],
        is_deleted: false
    },
    detalhes: {
        "entrada_cpf": {
            "value": "10748896732",
            "label": "Entrada CPF",
            "source": ["PAIscpf"],
            is_deleted: false
        },
        "found": {
            value: "Sim",
            label: "Conta encontrada?",
            source: ["PAIscpf"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["PAIscpf"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["PAIscpf"],
            is_deleted: false
        }
    }
};

// Mock for PAIsSNAP
const possivelContaPAIsSNAP1: PossivelConta = {
    site: {
        value: "Facebook",
        label: "Site",
        source: ["ProvedorDeAplicacacaoDaInternetcpf"],
        is_deleted: false
    },
    detalhes: {
        "entrada_cpf": {
            "value": "10748896732",
            "label": "Entrada CPF",
            "source": ["PAIscpf"],
            is_deleted: false
        },
        "found": {
            value: "Sim",
            label: "Conta encontrada?",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        },
        "alerta": {
            value: "Sim",
            label: "Alvo alertado?",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Notificação",
            label: "Tipo de Alerta",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        }
    }
};

const possivelContaPAIsSNAP2: PossivelConta = {
    site: {
        value: "Instagram",
        label: "Site",
        source: ["ProvedorDeAplicacacaoDaInternetcpf"],
        is_deleted: false
    },
    detalhes: {
        "entrada_cpf": {
            "value": "10748896732",
            "label": "Entrada CPF",
            "source": ["PAIscpf"],
            is_deleted: false
        },
        "found": {
            value: "Sim",
            label: "Conta encontrada?",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["ProvedorDeAplicacacaoDaInternetcpf"],
            is_deleted: false
        }
    }
};

// Create the complete mock with array of items
export const possiveisContasMock: PossivelConta[] = [
    possivelContaPAIs1,
    possivelContaPAIs2,
    possivelContaPAIsSNAP1,
    possivelContaPAIsSNAP2
];
