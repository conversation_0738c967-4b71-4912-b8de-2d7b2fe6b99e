from typing import Optional, List, Any
from pydantic import BaseModel, Field
from uuid import UUID
from datetime import datetime, timezone


# Used in: update_report_handler (request)
class UpdateReportRequest(BaseModel):
    user_reports_id: UUID
    omitted_notes: List[str]
    report_name: Optional[str] = None
    user_id: UUID


# Used in: get_data_from_snap_api_handler (request)
class SnapApiRequest(BaseModel):
    report_type: str
    report_input_value: str
    report_input_encrypted: dict
    user_reports_id: Optional[str] = None



# Used in: get_data_from_snap_api_handler (response)
class SnapApiResponse(BaseModel):
    report_name: str
    report_type: str
    user_reports_id: str
    report_status: str
    report_search_args: dict
    subject_name: Optional[str] = None
    subject_mother_name: Optional[str] = None
    subject_age: Optional[int] = None
    subject_sex: Optional[str] = None
    creation_at: str
    modified_at: str
    omitted_notes: List[Any]
    data: dict


class InsertReport(BaseModel):
    report_name: Optional[dict] = None
    report_type: str
    report_status: Optional[dict] = None
    report_search_args: Optional[dict] = None
    subject_name: Optional[dict] = None
    subject_mother_name: Optional[dict] = None
    subject_age: Optional[int] = None
    subject_sex: Optional[dict] = None
    creation_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    modified_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    omitted_notes: Optional[dict] = None
    data: Optional[dict] = None
    hmac: Optional[dict] = None


class InsertVerifier(BaseModel):
    verifier: dict