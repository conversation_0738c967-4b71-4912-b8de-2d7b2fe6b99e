export class BaseError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code?: string,
    public errors?: any[]
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export class AuthenticationError extends BaseError {
  constructor(message = "Authentication failed") {
    super(message, 401, "AUTHENTICATION_ERROR");
  }
}

export class AuthorizationError extends BaseError {
  constructor(message = "Not authorized to perform this action") {
    super(message, 403, "AUTHORIZATION_ERROR");
  }
}

export class ValidationError extends BaseError {
  constructor(message = "Validation failed", errors?: any[]) {
    super(message, 400, "VALIDATION_ERROR", errors);
  }
}

export class NotFoundError extends BaseError {
  constructor(resource = "Resource") {
    super(`${resource} not found`, 404, "NOT_FOUND_ERROR");
  }
}

export class ConflictError extends BaseError {
  constructor(message = "Resource already exists") {
    super(message, 409, "CONFLICT_ERROR");
  }
}

export class RateLimitError extends BaseError {
  constructor(message = "Too many requests") {
    super(message, 429, "RATE_LIMIT_ERROR");
  }
}

export class BadRequestError extends BaseError {
  constructor(message = "Bad request") {
    super(message, 400, "BAD_REQUEST_ERROR");
  }
}

export class InternalServerError extends BaseError {
  constructor(message = "Internal server error") {
    super(message, 500, "INTERNAL_SERVER_ERROR");
  }
}

export class ServiceUnavailableError extends BaseError {
  constructor(message = "Service temporarily unavailable") {
    super(message, 503, "SERVICE_UNAVAILABLE_ERROR");
  }
}

export class TokenExpiredError extends AuthenticationError {
  constructor(message = "Token has expired") {
    super(message);
    this.code = "TOKEN_EXPIRED";
  }
}

export class InvalidTokenError extends AuthenticationError {
  constructor(message = "Invalid token") {
    super(message);
    this.code = "INVALID_TOKEN";
  }
}
