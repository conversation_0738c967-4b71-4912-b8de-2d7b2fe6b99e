import type {
  ReportData,
  ReportStatusProps,
  ReportStatusTypes,
} from "~/types/global";
import { REPORT_CONSTANTS } from "~/helpers/constants";

function isReportStatusProps(x: unknown): x is ReportStatusProps {
  const statusFieldKey = REPORT_CONSTANTS.new_report.status_report;
  return x !== null && typeof x === "object" && statusFieldKey in x;
}

export function getReportStatus(
  report: ReportData
): ReportStatusTypes | null {
  const statusObjKey = REPORT_CONSTANTS.new_report.report_status;
  const maybeStatus = (report as any)[statusObjKey];

  if (!isReportStatusProps(maybeStatus)) {
    return null;
  }

  const statusReportKey = REPORT_CONSTANTS.new_report.status_report;
  return maybeStatus[statusReportKey] as ReportStatusTypes;
}

export function isPendingReport(report: ReportData): boolean {
  return getReportStatus(report) === REPORT_CONSTANTS.status.pending;
}

export function isErrorReport(report: ReportData): boolean {
  return getReportStatus(report) === REPORT_CONSTANTS.status.error;
}
