import { Sociedade } from "../model/Sociedades";

export const sociedadeoMock1: Sociedade = {
    razao_social: {
        value: "Empresa ABC Ltda.",
        label: "Razão Social",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "12.345.678/0001-90",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        procon: {
            value: "Não consta",
            label: "PROCON",
            source: ["SNAP"],
            is_deleted: false
        },
        "info restricao": {
            value: "Nada consta",
            label: "Info Restrição",
            source: ["SNAP"],
            is_deleted: false
        },
        "status receita": {
            value: "Ativo",
            label: "Status na Receita",
            source: ["SNAP"],
            is_deleted: false
        },
        "data de fundacao": {
            value: "01/01/2000",
            label: "Data de Fundação",
            source: ["SNAP"],
            is_deleted: false
        },
    },
    enderecos: [
        {
            value: {
                logradouro: {
                    value: "<PERSON><PERSON> das Flores",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "123",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                complemento: {
                    value: "Apto 101",
                    label: "Complemento",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "Jardim Primavera",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "São Paulo",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "Zona Sul",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "01234-567",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "area code": {
                    value: "11",
                    label: "Código de Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "telefone relacionado": {
                    value: "98765-4321",
                    label: "Telefone Relacionado",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "label default key": {
                    value: "Residencial",
                    label: "Tipo",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Endereço",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                logradouro: {
                    value: "Avenida Paulista",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "1578",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                complemento: {
                    value: "Sala 304",
                    label: "Complemento",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "Bela Vista",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "São Paulo",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "Centro",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "01310-200",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "area code": {
                    value: "11",
                    label: "Código de Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "telefone relacionado": {
                    value: "3456-7890",
                    label: "Telefone Relacionado",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "label default key": {
                    value: "Comercial",
                    label: "Tipo",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Endereço",
            source: ["SNAP"],
            is_deleted: false
        },
    ],
    telefones: [
        {
            value: {
                "phone number": {
                    value: "(11) 98765-4321",
                    label: "Número de Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "(11) 3456-7890",
                    label: "Número de Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        },
    ],
    emails: [
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email",
            source: ["SNAP"],
            is_deleted: false
        },
    ],

};

export const sociedadeoMock2: Sociedade = {
    razao_social: {
        value: "Tech Inovação S.A.",
        label: "Razão Social",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "98.765.432/0001-10",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        procon: {
            value: "Não consta",
            label: "PROCON",
            source: ["SNAP"],
            is_deleted: false
        },
        "info restricao": {
            value: "Nada consta",
            label: "Info Restrição",
            source: ["SNAP"],
            is_deleted: false
        },
        "status receita": {
            value: "Ativo",
            label: "Status na Receita",
            source: ["SNAP"],
            is_deleted: false
        },
        "data de fundacao": {
            value: "15/03/2015",
            label: "Data de Fundação",
            source: ["SNAP"],
            is_deleted: false
        },
        data_abertura: {
            value: "15/03/2015",
            label: "Data de Abertura",
            source: ["SNAP"],
            is_deleted: false
        },
        sequencial: {
            value: "9876543210",
            label: "Sequencial",
            source: ["SNAP"],
            is_deleted: false
        },
        porte: {
            value: "GRANDE",
            label: "Porte",
            source: ["SNAP"],
            is_deleted: false
        },
        "tipo de imposto": {
            value: "LUCRO REAL",
            label: "Tipo de Imposto",
            source: ["SNAP"],
            is_deleted: false
        },
        "total de funcionarios": {
            value: "250",
            label: "Total de Funcionários",
            source: ["SNAP"],
            is_deleted: false
        },
        "quantidade de funcionarios acima de 5 salarios": {
            value: "75",
            label: "Funcionários acima de 5 salários",
            source: ["SNAP"],
            is_deleted: false
        },
        "quantidade de funcionarios abaixo de 5 salarios": {
            value: "175",
            label: "Funcionários abaixo de 5 salários",
            source: ["SNAP"],
            is_deleted: false
        },
        cnae: {
            value: "6209-1/00",
            label: "CNAE",
            source: ["SNAP"],
            is_deleted: false
        },
        "descricao do cnae": {
            value: "Suporte técnico, manutenção e outros serviços em tecnologia da informação",
            label: "Descrição do CNAE",
            source: ["SNAP"],
            is_deleted: false
        },
        cnae1: {
            value: "6209-1/00",
            label: "CNAE 1",
            source: ["SNAP"],
            is_deleted: false
        },
        cnae2: {
            value: "6311-9/00",
            label: "CNAE 2",
            source: ["SNAP"],
            is_deleted: false
        },

        "cnae secundario": {
            value: "6311-9/00",
            label: "CNAE Secundário",
            source: ["SNAP"],
            is_deleted: false
        },
        "cnae secundario2": {
            value: "6202-3/00",
            label: "CNAE Secundário 2",
            source: ["SNAP"],
            is_deleted: false
        }
    },
    enderecos: [
        {
            value: {
                logradouro: {
                    value: "Avenida Paulista",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "1500",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                complemento: {
                    value: "Andar 15",
                    label: "Complemento",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "Bela Vista",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "São Paulo",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "Centro",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "01310-200",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "area code": {
                    value: "11",
                    label: "Código de Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "telefone relacionado": {
                    value: "3456-7890",
                    label: "Telefone Relacionado",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "label default key": {
                    value: "Matriz",
                    label: "Tipo",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Endereço Matriz",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                logradouro: {
                    value: "Rua da Tecnologia",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "789",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                complemento: {
                    value: "Bloco B",
                    label: "Complemento",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "Alphaville",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "Barueri",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "Grande São Paulo",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "06454-000",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "area code": {
                    value: "11",
                    label: "Código de Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "telefone relacionado": {
                    value: "4567-8901",
                    label: "Telefone Relacionado",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "label default key": {
                    value: "Filial",
                    label: "Tipo",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Endereço Filial",
            source: ["SNAP"],
            is_deleted: false
        },
    ],
    telefones: [
        {
            value: {
                "phone number": {
                    value: "(11) 3456-7890",
                    label: "Número de Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Telefone Matriz",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "(11) 4567-8901",
                    label: "Número de Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Telefone Filial",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "(11) 99876-5432",
                    label: "Número de Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Telefone Celular",
            source: ["SNAP"],
            is_deleted: false
        },
    ],
    emails: [
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email Comercial",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email Financeiro",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email RH",
            source: ["SNAP"],
            is_deleted: false
        },
    ],

};

export const sociedadesMock: Sociedade[] = [sociedadeoMock1, sociedadeoMock2];
