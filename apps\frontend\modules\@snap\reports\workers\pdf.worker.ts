import type { ReportDocumentProps } from '../ui/components/ReportDocument';
import './workerShim.js';
let log = console.log;

self.addEventListener('message', async (event) => {
  const { id, type, payload } = event.data;
  
  if (type === 'renderPDF') {
    try {
      log('Renderizando PDF no worker...');
      const { renderReportPDF } = await import('../helpers/render-report-pdf.helper.js');
      const blob = await renderReportPDF(payload);
      const url = URL.createObjectURL(blob);

      self.postMessage({ id, type: 'renderPDFResult', payload: url });
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      log("Erro ao renderizar PDF:", err);
      self.postMessage({ 
        id, 
        type: 'renderPDFError', 
        payload: { message: err.message, stack: err.stack } 
      });
    }
  } else if (type === 'setLogger') {
    log = payload ? console.log : () => {};
    self.postMessage({ id, type: 'loggerSet', payload: !!payload });
  }
});

export type WorkerType = {
  renderPDFInWorker: (props: ReportDocumentProps) => Promise<string>;
  onProgress: (enabled: boolean) => void;
};