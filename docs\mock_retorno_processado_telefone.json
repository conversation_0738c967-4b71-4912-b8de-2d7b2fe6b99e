{"user_reports_id": "6f6e06e9-af51-4067-a53f-03c8c17c4da8", "report_status": "success", "report_type": "telefone", "report_search_args": {"telefone": "21999891661"}, "report_name": "telefone 21999891661", "creation_at": "2025-05-19T20:20:56.920580+00:00", "modified_at": "2025-05-19T20:20:56.920580+00:00", "subject_name": "BIANCA PARREIRA CABRAL-LOPES", "subject_mother_name": "NORMA PERREIRA", "subject_age": null, "subject_sex": "F", "data": [{"title": "Contatos Salvos", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBIS"], "data_count": 4, "data": [{"detalhes": [{"alias": {"value": "Aversa Mp Csi Cibernetica", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "detalhes": {"origin": {"value": "getcontact", "label": "Origem", "source": ["IRBIS"], "is_deleted": false}}}, {"alias": {"value": "<PERSON> Mprj", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "detalhes": {"origin": {"value": "tc", "label": "Origem", "source": ["IRBIS"], "is_deleted": false}}}, {"alias": {"value": "<PERSON>", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "detalhes": {"origin": {"value": "drupe", "label": "Origem", "source": ["IRBIS"], "is_deleted": false}}}, {"alias": {"value": "<PERSON><PERSON><PERSON> a<PERSON>a", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "detalhes": {"origin": {"value": "callapp", "label": "Origem", "source": ["IRBIS"], "is_deleted": false}}}]}]}, {"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 5, "data": [{"detalhes": {"full name": {"value": "BIANCA PARREIRA CABRAL LOPES", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "NORMA PERREIRA", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "104.752.247-01", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "F", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}, "data nascimento": {"value": "1984-10-21", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Imagens", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 2, "data": [{"detalhes": [{"value": {"url": {"value": "https://images.unsplash.com/photo-1642290460481-76a5f2e18c3e?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", "label": "URL da Imagem", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}, {"value": {"url": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "URL da Imagem", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Emails", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBIS", "IRBISLuna"], "data_count": 1, "data": [{"detalhes": [{"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["IRBIS"], "is_deleted": false}}, "label": "Email", "source": ["IRBIS"], "is_deleted": false}]}]}, {"title": "Endereços", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "IRBISLuna"], "data_count": 2, "data": [{"detalhes": [{"value": {"logradouro": {"value": "RUA DO ROMANCISTA", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "CS", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "FREGUESIA ILHA DO GOVERNADOR", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "cidade": {"value": "RIO DE JANEIRO", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "estado ou regiao": {"value": "BR_RJ", "label": "Estado", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "21911170", "label": "CEP", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}, {"value": {"logradouro": {"value": "RUA AMÉLIA THRETAS", "label": "Logradouro", "source": ["IRBIS"], "is_deleted": false}, "complemento": {"value": "C", "label": "Complemento", "source": ["IRBIS"], "is_deleted": false}, "bairro": {"value": "FREGUESIA ILHA DO GOVERNADOR", "label": "Bairro", "source": ["IRBIS"], "is_deleted": false}, "city": {"value": "RIO DE JANEIRO", "label": "Cidade", "source": ["IRBIS"], "is_deleted": false}, "cep ou zipcode": {"value": "21910087", "label": "CEP", "source": ["IRBIS"], "is_deleted": false}, "area": {"value": "RJ", "label": "Estado", "source": ["IRBIS"], "is_deleted": false}}, "label": "Endereço", "source": ["IRBIS"], "is_deleted": false}]}]}, {"title": "Parentes", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 1, "data": [{"parentesco": {"value": "parente MAE", "label": "Parentesco", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"full name": {"value": "NORMA PERREIRA", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Empresas Relacionadas", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBISLuna"], "data_count": 2, "data": [{"razao_social": {"value": "SENAC-RIO", "label": "Razão Social", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"formacao educacional": {"value": "Redes de Computadores", "label": "Formação Educacional", "source": ["IRBISLuna"], "is_deleted": false}, "rotulo": {"value": "Vinculo Educacional", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["IRBISLuna"], "is_deleted": false}}}, {"razao_social": {"value": "Universidade Federal do Rio de Janeiro", "label": "Razão Social", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"data_inicio": {"value": "01/01/2003", "label": "Data de Início", "source": ["IRBISLuna"], "is_deleted": false}, "data_termino": {"value": "31/12/2007", "label": "Data de Término", "source": ["IRBISLuna"], "is_deleted": false}, "formacao_educacional": {"value": "<PERSON><PERSON><PERSON>", "label": "Formação Educacional", "source": ["IRBISLuna"], "is_deleted": false}, "rotulo": {"value": "Vinculo Educacional", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["IRBISLuna"], "is_deleted": false}}}]}, {"title": "Nomes de Usuário", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBISLuna"], "data_count": 3, "data": [{"detalhes": [{"value": {"alias": {"value": "bianca.parreira", "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"alias": {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>_123", "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"alias": {"value": "bianca-765343", "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}]}]}, {"title": "Telefones", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBISLuna"], "data_count": 8, "data": [{"detalhes": [{"value": {"phone number": {"value": "5521987511944", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "5521999891661", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "5521973931057", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "5521996761661", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "552133960539", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "555135624382", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "552135461661", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "552139232200", "label": "Número do Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}]}]}, {"title": "<PERSON><PERSON><PERSON> Redes Socia<PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBIS", "IRBISLuna"], "data_count": 2, "data": [{"telegram": [{"value": {"nome": {"value": "<PERSON>", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "nome no perfil": {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Nome no Perfil", "source": ["IRBIS"], "is_deleted": false}}, "label": "Telegram", "source": ["IRBIS"], "is_deleted": false}, {"value": {"nome": {"value": "<PERSON>", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}, "nome no perfil": {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Nome no Perfil", "source": ["IRBIS"], "is_deleted": false}}, "label": "Telegram", "source": ["IRBIS"], "is_deleted": false}], "linkedin": [{"value": {"imagem de perfil": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "<PERSON><PERSON>", "source": ["IRBIS"], "is_deleted": false}, "nome": {"value": "<PERSON>", "label": "Nome", "source": ["IRBIS"], "is_deleted": false}}, "label": "Linkedin", "source": ["IRBIS"], "is_deleted": false}]}]}, {"title": "Outras URLs", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBISLuna"], "data_count": 3, "data": [{"detalhes": [{"value": {"url": {"value": "http://www.mp.rj.gov.br/portal/page/portal/Internet/Imprensa/Em_Destaque/Noticia?caid=293&iditem=8308973", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"url": {"value": "http://www.facebook.com/people/_/100001296267978", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"url": {"value": "https://www.linkedin.com/in/jo%C3%A3o-aversa-29234320", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}]}]}, {"title": "Possíveis <PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "IRBISLuna"], "data_count": 4, "data": [{"nome_completo": {"value": "<PERSON><PERSON>", "label": "Nome <PERSON>to", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"nome_da_mae": {"value": "<PERSON>", "label": "Nome Completo da Mãe", "source": ["IRBISLuna"], "is_deleted": false}, "data nascimento": {"value": "06/02/1992", "label": "Data de Nascimento", "source": ["IRBISLuna"], "is_deleted": false}, "label default key": {"value": "family Niece", "label": "Parentesco", "source": ["IRBISLuna"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "5547988208342", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON><PERSON>", "label": "Nome <PERSON>to", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"data nascimento": {"value": "21/11/1969", "label": "Data de Nascimento", "source": ["IRBISLuna"], "is_deleted": false}, "label default key": {"value": "family Brother", "label": "Parentesco", "source": ["IRBISLuna"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "5547988118802", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON><PERSON><PERSON>", "label": "Nome <PERSON>to", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"data nascimento": {"value": "06/07/1988", "label": "Data de Nascimento", "source": ["IRBISLuna"], "is_deleted": false}, "label default key": {"value": "family Brother", "label": "Parentesco", "source": ["IRBISLuna"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "5592992957554", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": {"data nascimento": {"value": "13/12/1945", "label": "Data de Nascimento", "source": ["IRBISLuna"], "is_deleted": false}, "label default key": {"value": "family Mother", "label": "Parentesco", "source": ["IRBISLuna"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "5551984703292", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}]}]}, {"title": "Possíveis Pessoas Relacionadas", "subtitle": "Dados consultados na API SNAP.", "source": ["IRBISLuna"], "data_count": 1, "data": [{"nome_completo": {"value": "JOAO AVERSA", "label": "Nome <PERSON>to", "source": ["IRBISLuna"], "is_deleted": false}, "detalhes": [{"value": {"data nascimento": {"value": "26/12/1984", "label": "Data de Nascimento", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["IRBISLuna"], "is_deleted": false}], "telefones": [{"value": {"phone number": {"value": "5521999891661", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "5521985592439", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}, {"value": {"phone number": {"value": "5521999891661", "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Telefone", "source": ["IRBISLuna"], "is_deleted": false}], "imagens": [{"value": {"url": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}, {"value": {"url": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}, {"value": {"url": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}, {"value": {"url": {"value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", "label": "URL", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "URL", "source": ["SNAP"], "is_deleted": false}], "enderecos": [{"value": {"logradouro": {"value": "AVENIDA PAULISTA", "label": "Logradouro", "source": ["IRBISLuna"], "is_deleted": false}, "numero": {"value": "1000", "label": "Número", "source": ["IRBISLuna"], "is_deleted": false}, "bairro": {"value": "BELA VISTA", "label": "Bairro", "source": ["IRBISLuna"], "is_deleted": false}, "city": {"value": "SÃO PAULO", "label": "Cidade", "source": ["IRBISLuna"], "is_deleted": false}, "area": {"value": "SP", "label": "Á<PERSON>", "source": ["IRBISLuna"], "is_deleted": false}, "cep ou zipcode": {"value": "01310100", "label": "CEP", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Endereço", "source": ["IRBISLuna"], "is_deleted": false}], "redes_sociais": {"facebook": {"value": {"link": {"value": "http://www.facebook.com/joao.aversa.3", "label": "Link", "source": ["IRBISLuna"], "is_deleted": false}, "nome de usuario": {"value": "joao.aversa.3", "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}, "id": {"value": "100001296267978", "label": "ID", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Facebook", "source": ["IRBISLuna"], "is_deleted": false}, "twitter": {"value": {"link": {"value": "http://www.twitter.com/joao_aversa", "label": "Link", "source": ["IRBISLuna"], "is_deleted": false}, "nome de usuario": {"value": "joao_aversa", "label": "Nome de Usuário", "source": ["IRBISLuna"], "is_deleted": false}}, "label": "Twitter", "source": ["IRBISLuna"], "is_deleted": false}}}]}, {"title": "Possíveis Contas em Sites", "subtitle": "Dados consultados na API SNAP.", "source": ["ProvedorDeAplicacacaoDaInternettelefone"], "data_count": 5, "data": [{"site": {"value": "BrasilBitcoin", "label": "Site", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "detalhes": {"existe": {"value": "false", "label": "existe", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "termo procurado": {"value": "110.452.316-73", "label": "termo procurado", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}}}, {"site": {"value": "CasasBahia", "label": "Site", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "detalhes": {"existe": {"value": "Erro: A aplicação bloqueou a requisicao.", "label": "existe", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "termo procurado": {"value": "110.452.316-73", "label": "termo procurado", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}}}, {"site": {"value": "Digipare", "label": "Site", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "detalhes": {"existe": {"value": "false", "label": "existe", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "termo procurado": {"value": "110.452.316-73", "label": "termo procurado", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}}}, {"site": {"value": "Extra", "label": "Site", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "detalhes": {"existe": {"value": "true", "label": "existe", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "termo procurado": {"value": "110.452.316-73", "label": "termo procurado", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}}}, {"site": {"value": "PontoFrio", "label": "Site", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "detalhes": {"existe": {"value": "false", "label": "existe", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}, "termo procurado": {"value": "110.452.316-73", "label": "termo procurado", "source": ["Provedor de Aplicacação da Internet"], "is_deleted": false}}}]}]}