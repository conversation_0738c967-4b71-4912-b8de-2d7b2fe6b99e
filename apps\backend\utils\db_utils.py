from sqlalchemy.exc import OperationalError
import asyncio
import logging

logger = logging.getLogger(__name__)

async def retry_db_operation(operation_callable, max_retries=3, backoff=2):
    for attempt in range(1, max_retries + 1):
        try:
            return await operation_callable()
        except OperationalError as e:
            logger.warning("DB operation failed. Retrying (%d/%d)... Error: %s", attempt, max_retries, e)
            await asyncio.sleep(backoff * attempt)
    logger.error("All DB retry attempts failed.")
    raise
