import { ValueWithSource } from "./ValueWithSource";
import { _Endereco } from "./Enderecos";
import { _Telefone } from "./Telefones";

interface _ContatoPessoaBase {
    "nome1"?: string;
    "full name"?: string;
    cpf?: string;
    "titulo de eleitor"?: string;
    "data nascimento"?: string;
    sexo?: string;
    procon?: string;
    "label default key"?: string;
    "first names"?: string;
    surname?: string;
}

interface _ContatoEmpresaBase {
    "razao social"?: string;
    cnpj?: string;
    "label default key"?: string;
}

export type _Contato = _ContatoPessoaBase | _ContatoEmpresaBase;

export interface ContatoPessoa {
    nome_completo: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
    telefones?: Array<ValueWithSource<_Telefone>>;
    enderecos?: Array<ValueWithSource<_Endereco>>;
}

export interface ContatoEmpresa {
    razao_social: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
    telefones?: Array<ValueWithSource<_Telefone>>;
    enderecos?: Array<ValueWithSource<_Endereco>>;
}

export type PossivelContato = ContatoPessoa | ContatoEmpresa;
