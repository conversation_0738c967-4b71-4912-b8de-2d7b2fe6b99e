import React from "react";
import { Socio } from "../../model/Socios";
import { RenderStrategy } from "./RenderStrategy";
import { renderValidArray } from "./helpers.strategy";

export class RenderSocios implements RenderStrategy<Socio> {
  validateKeys = (keys: Array<keyof Socio>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (socios?: Socio) => React.ReactElement | null
  > = {
      socio: (socios?: Socio) => {
        if (!socios?.detalhes?.length) return null;

        return renderValidArray('detalhes', socios || {}, [], true /* hasLabel */, false /* hasTitle */);
      },
    };

  render = (data: Socio): React.ReactElement[] => {
    const elementos: React.ReactElement[] = [];

    // Iterate through all keys in the dictionary
    Object.keys(this.formatByKey).forEach((chave) => {
      const elemento = this.formatByKey[chave](data);
      if (elemento) {
        elementos.push(
          <React.Fragment key={`fragment-${chave}`}>{elemento}</React.Fragment>
        );
      }
    });

    return elementos;
  };
}