import { ValueWithSource } from "./ValueWithSource";

export interface _Doacao {
    valor: string;
    data: string;
    documento: string;
    recibo: string;
    descricao: string;
    especie: string;
    natureza: string;
    "natureza estimavel": string;
    origem: string;
    fonte: string;
    Tipo: string;
}

export interface _Empresa {
    "razao social": string;
    cnpj: string;
    cnae: string;
}

export interface _Pessoa {
    "full name": string;
    cpf: string;
}

export interface DoacaoRecebidaBase {
    detalhes: Record<string, ValueWithSource>;
    doacoes: Array<ValueWithSource>;
}

export interface DoacaoRecebidaEmpresa extends DoacaoRecebidaBase {
    razao_social: ValueWithSource<_Empresa["razao social"]>;
}

export interface DoacaoRecebidaPessoa extends DoacaoRecebidaBase {
    nome_completo: ValueWithSource<_Pessoa["full name"]>;
}

export type DoacaoRecebida = DoacaoRecebidaEmpresa | DoacaoRecebidaPessoa;

// Type guard to check if a donation is from a company
export function isDoacaoRecebidaEmpresa(doacao: DoacaoRecebida): doacao is DoacaoRecebidaEmpresa {
    return 'razao_social' in doacao;
}

// Type guard to check if a donation is from a person
export function isDoacaoRecebidaPessoa(doacao: DoacaoRecebida): doacao is DoacaoRecebidaPessoa {
    return 'nome_completo' in doacao;
}
