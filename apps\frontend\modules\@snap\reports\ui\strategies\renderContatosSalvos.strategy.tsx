import React from "react";
import { ContatoSalvo } from "../../model/ContatosSalvos";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { getIconImage, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderContatosSalvos implements RenderStrategy<ContatoSalvo> {

    validateKeys = (keys: Array<keyof ContatoSalvo>): boolean => {
        return keys.some((campo) => {
            return campo in this.formatByKey;
        });
    };

    formatByKey: Record<
        string,
        (contatosSalvos?: ContatoSalvo) => React.ReactElement | null
    > = {
            detalhes: (contatosSalvos?: ContatoSalvo) => {
                if (!contatosSalvos?.detalhes?.length) return null;

                return (
                    <CustomGridContainer cols={1} className="">
                        <GridItem fullWidth>
                            <CustomGridContainer cols={2}>
                                {contatosSalvos.detalhes.map((contato, contaIndex) => {
                                    const originImage = getIconImage(contato.detalhes.origin.value);
                                    return (
                                        <GridItem key={`contato-column-${contaIndex}`} cols={1}>
                                            <div className="mb-2">
                                                {/* <CustomLabel
                                                    label={`${(contato.alias.label || "Nome").toUpperCase()} ${contaIndex + 1}`}
                                                    colorClass="bg-white"
                                                /> */}
                                                <div className="flex items-center gap-2 pt-2">
                                                    <img src={originImage || ""} alt={contato.detalhes.origin.value} className="w-4 h-4" />
                                                    <CustomReadOnlyInputField
                                                        value={String(contato.alias.value || "")}
                                                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                                        tooltip={renderSourceTooltip(contato.alias.source)}
                                                    />
                                                </div>
                                                <div className="pl-5">
                                                    {Object.entries(contato.detalhes).map(
                                                        ([key, value]) => (
                                                            <GridItem
                                                                key={`contato-${contaIndex}-${key}`}
                                                                cols={1}
                                                                className="py-2"
                                                            >
                                                                <CustomReadOnlyInputField
                                                                    label={`${(value.label || translatePropToLabel(key)).toUpperCase()}`}
                                                                    value={parseValue(value.value || "")}
                                                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                                                    tooltip={renderSourceTooltip(value.source)}
                                                                />
                                                            </GridItem>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        </GridItem>
                                    )
                                })}
                            </CustomGridContainer>
                        </GridItem>
                    </CustomGridContainer>
                );
            }
        };

    render = (contatosSalvos: ContatoSalvo): React.ReactElement[] => {
        const keys = Object.keys(contatosSalvos) as Array<keyof ContatoSalvo>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof ContatoSalvo> = [
            'detalhes'
        ];

        // Filter the keys to only include those that exist in the contatosSalvos object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(contatosSalvos);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
