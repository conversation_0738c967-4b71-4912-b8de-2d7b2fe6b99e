import { useEffect } from "react";
import { useAuth } from "~/hooks/useAuth";
import { eventBus } from "~/helpers/eventBus.helper";

export const AuthEventHandler = () => {
  const { logoutMutation } = useAuth();

  useEffect(() => {
    const unsubscribe = eventBus.subscribe("UNAUTHORIZED_ERROR", async () => {
      try {
        await logoutMutation.mutateAsync();
      } catch (error) {
        console.error("Logout mutation failed:", error);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [logoutMutation]);

  return null;
};
