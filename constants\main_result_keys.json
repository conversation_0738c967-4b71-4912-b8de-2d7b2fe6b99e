{"snap": "SNAP", "banco_prisoes": "BancoNacionalDeMonitoramentoDePrisoes", "sintegra_ma": "SintegraMA", "sintegra_mt_cpf": "SintegraMTCPF", "sintegra_pb_cpf": "SintegraPBCPF", "sintegra_se_cpf": "SintegraSECPF", "sintegra_pr_cpf": "SintegraPRCPF", "sintegra_mg_cpf": "SintegraMGCPF", "cadastro_nacional_pj": "CadastroNacionalPJ", "jucesp_cnpj": "JUCESPCNPJ", "escavador": "Escavador", "portal_transparencia_mg": "PortalDaTransparenciaDeMinasGerais", "portal_transparencia_mg_cnpj": "PortalDaTransparenciaDeMinasGeraisCNPJ", "portal_transparencia_amazonas": "PortalDaTransparenciaDoAmazonas", "portal_transparencia_amazonas_cnpj": "PortalDaTransparenciaDoAmazonasCNPJ", "portal_transparencia_sc": "TransparenciaSC", "portal_transparencia_sc_cnpj": "TransparenciaSCCNPJ", "portal_transparencia_ma_cpf": "TransparenciaManausCPF", "portal_transparencia_ma_cnpj": "TransparenciaManausCNPJ", "portal_transparencia_df": "TransparenciaDF", "portal_transparencia_df_cnpj": "TransparenciaDFCNPJ", "portal_transparencia_pr_nome": "TransparenciaPRNome", "portal_transparencia_pr_cnpj": "TransparenciaPRCNPJ", "escavador_do_cpf": "EscavadorDOCPF", "escavador_do_nome": "EscavadorDONome", "escavador_do_cnpj": "EscavadorDOCNPJ", "querido_diario_cpf": "QueridoDiarioCPF", "querido_diario_nome": "QueridoDiarioNome", "tse_filiacao_partidaria": "TSEFiliacaoPartidaria", "tse_doacoes": "TSEDoacoes", "tse_fornecimento": "TSEFornecimento", "pais_cpf": "PAIscpf", "provedor_de_aplicacacao_da_internet_cpf": "ProvedorDeAplicacacaoDaInternetcpf", "irbis": "IRBIS", "irbis_luna": "IRBISLuna"}