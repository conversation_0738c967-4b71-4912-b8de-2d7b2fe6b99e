import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, getSingular, getPlural, translateSource } from '../../../helpers';
import { getFieldLabel, getFieldValue } from '../helpers.strategy';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintProcessosProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      numero?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: {
        [key: string]: {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }
      };
      movimentações?: {
        value: string[];
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintProcessos: React.FC<RenderPrintProcessosProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((processo, processoIndex) => (
        <View key={`processo-${processoIndex}`} style={styles.processoContainer} >
          {/* Número do Processo */}
          {processo.numero && !processo.numero.is_deleted && (
            <View style={styles.numeroContainer}>
              <View style={styles.numeroLabelContainer}>
                <Text style={styles.numeroLabel}>
                  {(processo.numero.label || "Número do Processo").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {processo.numero.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.numeroValue}>{processo.numero.value}</Text>
            </View>
          )}

          {/* Detalhes do Processo */}
          {processo.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(processo.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Movimentações */}
          {processo["movimentações"] && !processo["movimentações"].is_deleted && (
            <View style={styles.movimentacoesContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>
                  {translatePropToLabel(processo["movimentações"].label || "Movimentações").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {processo["movimentações"].source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <View style={styles.movimentacoesContent}>
                <Text style={styles.movimentacoesText} wrap>
                  {processo["movimentações"].value}
                </Text>
              </View>
            </View>
          )}

          {/* LISTAS DE PARTICIPANTES*/}
          {Object.entries(processo)
            .filter(([key, value]) =>
              Array.isArray(value)
            )
            .map(([key, participants]) => (
              <View key={`${key}-container`} style={styles.participantsContainer}>
                <View style={styles.subtitleContainer} >
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>{getPlural(translatePropToLabel(key)).toUpperCase()}</Text>
                </View>
                <View style={styles.grid}>
                  {(participants as any[])
                    .filter(participant => !participant.is_deleted)
                    .map((participant, participantIndex) => (
                      <View key={`${key}-${participantIndex}`} style={styles.participantBlock} >
                        <View style={styles.listContainer} wrap={false}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill='#889EA3' />
                          </Svg>
                          <Text style={styles.participantTitle}>
                            {translatePropToLabel(getSingular(participant.label) || key).toUpperCase()} {participantIndex + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {participant.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.participantFields} >
                          {Object.entries(participant.value)
                            .filter(([_, field]: [string, any]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue]: [string, any], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell} wrap={false}>
                                <View style={styles.infoContainer}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue?.source?.map((src: string) => `| ${src}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(getFieldValue(fieldValue) || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            ))}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8, // fix para não sobrepor o header
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  numeroLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  processoContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  numeroContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  numeroLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  numeroValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  participantsContainer: {
    marginBottom: 8,
  },
  participantBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  participantTitle: {
    fontSize: 9,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  participantFields: {
    paddingLeft: 8,
  },
  movimentacoesContainer: {
    padding: 8,
    marginBottom: 12,
    borderRadius: 4,
    backgroundColor: '#F5F5F5',
  },
  movimentacoesContent: {
    paddingLeft: 8,
  },
  movimentacoesText: {
    fontSize: 9,
    lineHeight: 1.4,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});