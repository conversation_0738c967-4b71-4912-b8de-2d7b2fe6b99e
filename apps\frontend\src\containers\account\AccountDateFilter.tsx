import { CustomLabel, Text } from "@snap/design-system";
import { Calendar } from "lucide-react";
import { formatIsoDate } from "~/helpers";

const AccountDateFilter = () => {
  return (
    <div className="pb-2 flex gap-4 justify-between border-b border-border border-dashed">
      <div className="flex flex-col">
        <Text className="text-[28px] leading-none">Relatório de uso</Text>
        <div className="flex items-center gap-2">
          <Text className="font-bold text-[34px] leading-none text-accent">Setembro</Text>
          <Text className="text-[34px] leading-none">/ 2025</Text>
          <Calendar size={30} />
        </div>
      </div>
      <div className="flex flex-col gap-2 items-end justify-end">
        <Text className="text-accent font-semibold">Escolha um intervalo personalizado</Text>
        <div className="flex items-center gap-8">
          <div className="flex items-center gap-2">
            <CustomLabel label="INÍCIO:" colorClass="bg-border" />
            <Text variant="body-md">10/09/2025</Text>
            <Calendar />
          </div>
          <div className="flex items-center gap-2">
            <CustomLabel label="FINAL:" colorClass="bg-border" />
            <Text variant="body-md">{formatIsoDate(new Date().toISOString(), true)}</Text>
            <Calendar />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountDateFilter