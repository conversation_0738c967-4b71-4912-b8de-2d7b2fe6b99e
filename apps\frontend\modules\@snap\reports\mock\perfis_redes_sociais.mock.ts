import { PerfilRedeSocial } from "../model/PerfisRedesSociais";

export const perfisRedesSOciaisMock: PerfilRedeSocial = {
    "telegram": [
        {
            "value": {
                "nome": {
                    "value": "<PERSON> Parreira",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "nome no perfil": {
                    "value": "BiancaParreira",
                    "label": "Nome no Perfil",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            },
            "label": "Telegram",
            "source": ["IRBIS"],
            "is_deleted": false
        },
        {
            "value": {
                "nome": {
                    "value": "Bianca Parreira",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "nome no perfil": {
                    "value": "BiancaParreira",
                    "label": "Nome no Perfil",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            },
            "label": "Telegram",
            "source": ["IRBIS"],
            "is_deleted": false
        },

    ],
    "linkedin": [
        {
            "value": {
                "imagem de perfil": {
                    "value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720", // ... imagem em base64
                    "label": "Imagem de Perfil",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "nome": {
                    "value": "Bianca Parreira",
                    "label": "Nome",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            },
            "label": "Linkedin",
            "source": ["IRBIS"],
            "is_deleted": false
        }
    ]
}