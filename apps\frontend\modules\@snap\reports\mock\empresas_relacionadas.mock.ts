import { Sociedade } from "../model/Sociedades";

export const empresasRelacionadasMock: Sociedade[] = [
    {
        "razao_social": {
            "value": "SENAC-RIO",
            "label": "Razão Social",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        "detalhes": {
            "formacao educacional": {
                "value": "Redes de Computadores",
                "label": "Formação Educacional",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            "rotulo": {
                "value": "Vinculo Educacional",
                "label": "Vínculo",
                "source": ["IRBISLuna"],
                "is_deleted": false
            }
        }
    },
    {
        "razao_social": {
            "value": "Universidade Federal do Rio de Janeiro",
            "label": "Razão Social",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        "detalhes": {
            "data_inicio": {
                "value": "01/01/2003",
                "label": "Data de Início",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            "data_termino": {
                "value": "31/12/2007",
                "label": "Data de Término",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            "formacao_educacional": {
                "value": "Analise de Sistema",
                "label": "Formação Educacional",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            "rotulo": {
                "value": "Vinculo Educacional",
                "label": "Vínculo",
                "source": ["IRBISLuna"],
                "is_deleted": false
            }
        }
    }
]