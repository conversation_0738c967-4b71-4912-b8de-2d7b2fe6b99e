import { create } from "zustand";

interface SecretKeyState {
  secretKey: string | null;
  actions: {
    setSecretKey: (key: string) => void;
    clearSecretKey: () => void;
  };
}

const useSecretKeyStore = create<SecretKeyState>((set) => ({
  secretKey: null,
  actions: {
    setSecretKey: (key) => set({ secretKey: key }),
    clearSecretKey: () => set({ secretKey: null }),
  },
}));

export const useSecretKey = () => useSecretKeyStore((state) => state.secretKey);
export const useSecretKeyActions = () =>
  useSecretKeyStore((state) => state.actions);
