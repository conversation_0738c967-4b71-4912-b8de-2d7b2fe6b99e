import { Text } from "@snap/design-system";
import { Column, DataTable } from "~/components/Table";
import AccountToolbar from "./AccountToolbar";
import AccountDateFilter from "./AccountDateFilter";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}


const mockLogUserRows = [
  { id: 1, date: "10/09/2025", time: "10:34:00", day: "Segunda-feira", reportType: "CPF", details: "" },
  { id: 2, date: "10/09/2025", time: "10:34:00", day: "Terça-feira", reportType: "CPF", details: "" },
  { id: 3, date: "10/09/2025", time: "10:34:00", day: "Quarta-feira", reportType: "COMBINADO", details: "(CPF, E-MAIL, TELEFONE)" },
  { id: 4, date: "11/09/2025", time: "08:15:23", day: "Quinta-feira", reportType: "CNPJ", details: "" },
  { id: 5, date: "11/09/2025", time: "09:45:12", day: "Quinta-feira", reportType: "E-MAIL", details: "" },
  { id: 6, date: "11/09/2025", time: "14:22:45", day: "Quinta-feira", reportType: "TELEFONE", details: "" },
  { id: 7, date: "12/09/2025", time: "11:05:33", day: "Sexta-feira", reportType: "COMBINADO", details: "(CPF, E-MAIL)" },
  { id: 8, date: "12/09/2025", time: "16:30:18", day: "Sexta-feira", reportType: "COMBINADO", details: "(CNPJ, TELEFONE)" },
  { id: 9, date: "13/09/2025", time: "09:10:27", day: "Sábado", reportType: "CPF", details: "" },
  { id: 10, date: "13/09/2025", time: "13:45:52", day: "Sábado", reportType: "COMBINADO", details: "(CPF, E-MAIL, TELEFONE)" },
  { id: 11, date: "14/09/2025", time: "10:20:15", day: "Domingo", reportType: "CNPJ", details: "" },
  { id: 12, date: "15/09/2025", time: "08:05:42", day: "Segunda-feira", reportType: "COMBINADO", details: "(E-MAIL, TELEFONE)" },
  { id: 13, date: "15/09/2025", time: "11:30:09", day: "Segunda-feira", reportType: "E-MAIL", details: "" },
  { id: 14, date: "16/09/2025", time: "09:25:37", day: "Terça-feira", reportType: "TELEFONE", details: "" },
  { id: 15, date: "16/09/2025", time: "14:50:21", day: "Terça-feira", reportType: "COMBINADO", details: "(CPF, E-MAIL)" },
  { id: 16, date: "17/09/2025", time: "10:15:48", day: "Quarta-feira", reportType: "CPF", details: "" },
  { id: 17, date: "17/09/2025", time: "15:40:33", day: "Quarta-feira", reportType: "COMBINADO", details: "(CNPJ, E-MAIL)" },
  { id: 18, date: "18/09/2025", time: "09:55:19", day: "Quinta-feira", reportType: "CNPJ", details: "" },
  { id: 19, date: "18/09/2025", time: "13:20:47", day: "Quinta-feira", reportType: "COMBINADO", details: "(CPF, TELEFONE)" },
  { id: 20, date: "19/09/2025", time: "08:45:36", day: "Sexta-feira", reportType: "E-MAIL", details: "" },
  { id: 21, date: "19/09/2025", time: "16:10:28", day: "Sexta-feira", reportType: "COMBINADO", details: "(CNPJ, TELEFONE)" },
  { id: 22, date: "20/09/2025", time: "10:30:54", day: "Sábado", reportType: "TELEFONE", details: "" },
  { id: 23, date: "20/09/2025", time: "14:55:17", day: "Sábado", reportType: "COMBINADO", details: "(CPF, E-MAIL)" },
  { id: 24, date: "21/09/2025", time: "11:25:39", day: "Domingo", reportType: "COMBINADO", details: "(CNPJ, E-MAIL, TELEFONE)" }
]

const userColumns: Column[] = [
  {
    key: "dateTime",
    header: "Data da consulta",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row.date}</Text>
        <Text variant="body-md" >{row.time}</Text>
        <Text className="opacity-80">{row.day}</Text>
      </div>
    ),
  },
  {
    key: "reportType",
    header: "Tipo de report",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row.reportType}</Text>
        <Text className="opacity-80">{row.details}</Text>
      </div>
    ),
  }
]

const DefaultUserAccount = ({ }: UserConfigDialogContentProps) => {

  return (
    <div className="flex flex-col flex-4/5">
      <AccountDateFilter />
      <div>
        <AccountToolbar />
        <div className="overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-456px)]">
          <DataTable columns={userColumns} data={mockLogUserRows} />
        </div>
      </div>
    </div>
  );
}

export default DefaultUserAccount;