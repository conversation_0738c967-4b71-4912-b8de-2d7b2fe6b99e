{"dadospessoais": {"titulo": "<PERSON><PERSON>", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].pessoa", "value": ["bookmark=4"], "rule": "", "loop": "once", "meta": "data consulta", "fields": [{"name": "Nome <PERSON>to", "position": "flow", "display": "", "fields": ["full name"], "additional": [{"attr": "nome1", "display": ""}]}]}, {"name": "SNAP", "attr": "SNAP[0].pessoa[0].pessoa", "value": ["label default key=parente MAE"], "rule": "||", "loop": "once", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON>", "position": "flow", "display": "", "fields": ["full name"], "additional": [{"attr": "full name1", "display": ""}]}]}, {"name": "SNAP", "attr": "SNAP[0].pessoa", "value": ["bookmark=4"], "rule": "", "loop": "once", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON>", "position": "flow", "display": "concat:' anos'", "fields": ["idade"]}, {"name": "Sexo", "position": "flow", "display": "rewrite:F=Feminino,M=Masculino", "fields": ["sexo"]}, {"name": "Nacionalidade", "position": "flow", "display": "full", "fields": ["nacionalidade", "pais do passaporte"]}, {"name": "Data de Nascimento", "position": "flow", "display": "full", "fields": ["data nascimento", "dt_nascimento"]}, {"name": "CPF", "position": "flow", "display": "mask:000.000.000-00", "fields": ["cpf"]}, {"name": "RG", "position": "flow", "display": "full", "fields": ["identidade"]}, {"name": "Estado Civil", "position": "flow", "display": "full", "fields": ["estadocivil"]}, {"name": "Status Receita", "position": "flow", "display": "full", "fields": ["status receita"]}, {"name": "Info Restrição", "position": "flow", "display": "full", "fields": ["info restricao"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": ["titulo de eleitor"]}, {"name": "PIS/PASEP", "position": "flow", "display": "full", "fields": ["pis/pasep"]}, {"name": "CTPS", "position": "flow", "display": "full", "fields": ["ctps"]}, {"name": "PROCON", "position": "flow", "display": "full", "fields": ["procon"]}, {"name": "Escolaridade", "position": "flow", "display": "full", "fields": ["escolaridade", "grauinstrucao"]}, {"name": "<PERSON><PERSON> (CBO)", "position": "flow", "display": "mask:0 (1)", "fields": ["descricao cbo", "cbo"]}, {"name": "<PERSON><PERSON> Pre<PERSON>mi<PERSON>", "position": "flow", "display": "mask:'R$0'", "fields": ["renda estimada", "renda presumida"]}, {"name": "Data de Admissão", "position": "flow", "display": "full", "fields": ["data de admissao"]}]}]}, "mandados": {"titulo": "Mandados de Prisão", "descricao": "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP).", "data": [{"name": "BancoNacionalDeMonitoramentoDePrisoes", "attr": "BancoNacionalDeMonitoramentoDePrisoes[0].mandado", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Número do Mandado", "position": "flow", "display": "full", "fields": ["numero do mandado de prisao"], "additional": [{"attr": "tipo de mandado", "display": "Tipo de Mandado"}, {"attr": "situacao", "display": "Situação"}, {"attr": "numero do processo", "display": "Número do Processo"}, {"attr": "tipificacoes penais", "display": "Tipificaçõ<PERSON>"}, {"attr": "data de expedicao", "display": "Data de Expedição"}, {"attr": "data de validade", "display": "Data de Validade"}, {"attr": "especie de prisao", "display": "Espécie de Prisão"}, {"attr": "municipio", "display": "Município"}, {"attr": "orgao expedidor", "display": "Órgão Expedidor"}, {"attr": "magistrado", "display": "<PERSON><PERSON><PERSON><PERSON>"}], "dicts": [{"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "", "rule": "", "loop": "once", "fields": [{"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "pena imposta"}, {"name": "Recaptura", "position": "flow", "display": "full", "fields": "recaptura"}]}]}]}]}, "telefones": {"titulo": "Telefones", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].phonenumber", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Telefone", "position": "flow", "display": "mask:'+00 (00) 00000-0000'", "fields": ["phone number"]}]}, {"name": "SintegraMA", "attr": "SintegraMACPF[0].phonenumber", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": ["phone number"]}]}]}, "emails": {"titulo": "Emails", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].emailaddress", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Email", "position": "flow", "display": "full", "fields": ["email address"]}]}]}, "enderecos": {"titulo": "Endereços", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].location", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["logradouro"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "label default key", "display": "Telefone"}]}]}, {"name": "SNAP", "attr": "SNAP[0].location", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["endereco", "nome"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}, {"name": "SintegraMT", "attr": "SintegraMTCPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["endereco", "nome", "logradouro"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cidade", "display": "Cidade"}, {"attr": "estado ou regiao", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}], "dicts": [{"name": "Email", "attr": "emailaddress", "value": "", "rule": "", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "email address"}]}]}]}, {"name": "SintegraPB", "attr": "SintegraPBCPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["endereco", "nome", "logradouro"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cidade", "display": "Cidade"}, {"attr": "estado ou regiao", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}]}]}, {"name": "SintegraSE", "attr": "SintegraSECPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["logradouro"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cidade", "display": "Cidade"}, {"attr": "estado ou regiao", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}]}]}, {"name": "SintegraMA", "attr": "SintegraMACPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["logradouro"], "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cidade", "display": "Cidade"}, {"attr": "estado ou regiao", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "nome", "display": "Razão Social Associada"}]}]}, {"name": "SintegraPR", "attr": "SintegraPRCPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": ["nome"], "additional": [{"attr": "logradouro", "display": "Logradouro"}, {"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cidade", "display": "Cidade"}, {"attr": "estado ou regiao", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}]}]}]}, "genealogia": {"titulo": "Parentes", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].pessoa[0].pessoa", "value": ["label default key=parente MAE", "label default key=parente IRMA(O)", "label default key=parente PAI", "label default key=parente TIO(A)", "label default key=parente AVOS", "label default key=parente FILHO(A)", "label default key=parente CONJUGE", "grau de parentesco=MAE"], "rule": "||", "loop": "each", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "", "fields": ["full name"], "additional": [{"attr": "nome1", "display": ""}, {"attr": "label default key", "display": "Parentesco"}, {"attr": "cpf", "display": "CPF"}, {"attr": "titulo de eleitor", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"attr": "data nascimento", "display": "Data de Nascimento"}, {"attr": "sexo", "display": "Sexo"}, {"attr": "procon", "display": "PROCON"}], "dicts": [{"name": "<PERSON><PERSON><PERSON>", "attr": "pessoa", "value": "label=parente MAE", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "label=parente PAI", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereço", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}, {"name": "SNAP", "attr": "SNAP[0].pessoa", "value": ["label default key=parente MAE", "label default key=parente IRMA(O)", "label default key=parente PAI", "label default key=parente TIO(A)", "label default key=parente AVOS", "label default key=parente FILHO(A)", "label default key=parente CONJUGE", "grau de parentesco=MAE"], "rule": "||", "loop": "each", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "", "fields": ["full name"], "additional": [{"attr": "nome1", "display": ""}, {"attr": "label default key", "display": "Parentesco"}, {"attr": "cpf", "display": "CPF"}, {"attr": "titulo de eleitor", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"attr": "data nascimento", "display": "Data de Nascimento"}, {"attr": "sexo", "display": "Sexo"}, {"attr": "procon", "display": "PROCON"}], "dicts": [{"name": "<PERSON><PERSON><PERSON>", "attr": "pessoa", "value": "label=parente MAE", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "label=parente PAI", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereço", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}, {"name": "BancoNacionalDeMonitoramentoDePrisoes", "attr": "BancoNacionalDeMonitoramentoDePrisoes[0].mandado[n].pessoa", "value": ["pessoa=array"], "rule": "", "loop": "once", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": ["nome pai"]}]}]}, "empresas": {"titulo": "Sociedades", "descricao": "Dados consultados na API SNAP, SINTEGRA e Dados Abertos do Governo Federal.", "data": [{"name": "SNAP", "attr": "SNAP[0].empresa", "value": ["label default key=socio", "credilink label=socio"], "rule": "||", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sociedade", "position": "flow", "display": "concat:' '", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "entradasociedade", "display": "Entrada Sociedade"}, {"attr": "participacaosociedade", "display": "Participação (%)"}], "dicts": [{"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereço", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}, {"name": "Cadastro Nacional PJ", "attr": "CadastroNacionalPJ[0].pessoa[0].empresa", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sociedade", "position": "flow", "display": "full", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "qualificação societária", "display": "Qualificação Societária"}, {"attr": "situacao atual", "display": "Situação Atual"}, {"attr": "data da situacao na unidade federativa", "display": "Data da Situação na UF"}, {"attr": "data registro", "display": "Registro"}, {"attr": "data inicio de atividade", "display": "Início de Atividade"}, {"attr": "matriz", "display": "Matriz / Filial"}, {"attr": "natureza juridica", "display": "Natureza Jurídica"}, {"attr": "capital", "display": "Capital"}, {"attr": "porte", "display": "Porte"}, {"attr": "empresa mei", "display": "Empresa MEI"}, {"attr": "simples nacional", "display": "Simples Nacional"}, {"attr": "data inclusão simples nacional", "display": "Inclusão no Simples Nacional"}, {"attr": "data exclusão simples nacional", "display": "Exclusão no Simples Nacional"}, {"attr": "data inicio de atividade", "display": "Início de Atividade"}, {"attr": "cnae secundario", "display": "CNAE Secundário"}, {"attr": "ultima situacao", "display": "Última Situação"}, {"attr": "data situacao cadastral", "display": "Data Situação Cadastral"}, {"attr": "sublimite do simples nacional", "display": "Sublimite do Simples Nacional"}, {"attr": "forma de tributacao na 1a operacao", "display": "Forma de Tributação na 1ª operação"}, {"attr": "simples nacional", "display": "Simples Nacional"}]}], "dicts": [{"name": "Endereços", "attr": "endereco", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": "logradouro", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "complemento", "display": "Complemento"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "cep ou zipcode", "display": "CEP"}]}]}, {"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number", "additional": [{"attr": "area code", "display": "DDD"}]}]}, {"name": "Emails", "attr": "emailaddress", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Email", "position": "flow", "display": "full", "fields": "email address"}]}]}, {"name": "SintegraMT", "attr": "SintegraMTCPF[0].empresa", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sociedade", "position": "flow", "display": "concat:' '", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "inscricao estadual", "display": "Inscrição Estadual"}, {"attr": "numero de inscricao estadual", "display": "Inscrição Estadual"}, {"attr": "data de abertura", "display": "Data de Abertura"}, {"attr": "cnae", "display": "CNAE"}, {"attr": "cnae secundario", "display": "CNAE Secundário"}, {"attr": "ultima situacao", "display": "Última Situação"}, {"attr": "data situacao cadastral", "display": "Data Situação Cadastral"}, {"attr": "sublimite do simples nacional", "display": "Sublimite do Simples Nacional"}, {"attr": "forma de tributacao na 1a operacao", "display": "Forma de Tributação na 1ª operação"}, {"attr": "simples nacional", "display": "Simples Nacional"}]}]}, {"name": "SintegraMA", "attr": "SintegraMACPF[0].empresa", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sociedade", "position": "flow", "display": "concat:' '", "fields": ["razao social"], "additional": [{"attr": "inscricao estadual", "display": "Inscrição Estadual"}, {"attr": "cnae", "display": "CNAE"}, {"attr": "cnae1", "display": "CNAE"}, {"attr": "cnae2", "display": "CNAE"}, {"attr": "cnae3", "display": "CNAE"}, {"attr": "cnae4", "display": "CNAE"}, {"attr": "cnae5", "display": "CNAE"}, {"attr": "cnae6", "display": "CNAE"}, {"attr": "ultima situacao", "display": "Última Situação"}, {"attr": "data situacao cadastral", "display": "Data Situação Cadastral"}, {"attr": "nota fiscal eletronica", "display": "Nota Fiscal Eletrônica"}, {"attr": "regime de apuracao", "display": "Regime de Apuração"}]}]}, {"name": "SintegraMG", "attr": "SintegraMGCPF[0].endereco", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sociedade", "position": "flow", "display": "concat:' '", "fields": ["razao social"], "additional": [{"attr": "inscricao estadual", "display": "Inscrição Estadual"}, {"attr": "tipo de ie", "display": "Tipo de Inscrição Estadual"}, {"attr": "situacao inscricao estadual", "display": "Situação Inscrição Estadual"}, {"attr": "informacao incricao estadual", "display": "Informação de Inscrição Estadual"}, {"attr": "porte", "display": "Porte"}, {"attr": "regime de tributacao", "display": "Regime de Tributação"}, {"attr": "tipo produtor", "display": "T<PERSON>o Produtor"}, {"attr": "credito presumido", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"attr": "data inicio atividade", "display": "Data de Início de Atividade"}], "dicts": [{"name": "Endereço", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Endereço", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "cod ibge municipio", "display": "Código do IBGE para Município"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}]}]}]}]}]}, "remuneracao": {"titulo": "<PERSON><PERSON><PERSON><PERSON>", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].remuneracao", "value": ["empresa=array"], "rule": "||", "loop": "each", "meta": "data consulta", "fields": [{"name": "Empres<PERSON>", "position": "flow", "display": "full", "fields": ["empresa pagadora"], "additional": [{"attr": "data admissao", "display": "Ad<PERSON><PERSON>ão"}, {"attr": "valor", "display": "Remuneração (R$)"}], "dicts": [{"name": "CNPJ", "attr": "empresa", "value": "", "rule": "", "loop": "each", "fields": [{"name": "CNPJ", "position": "flow", "display": "full", "fields": "cnpj"}]}]}]}]}, "processos": {"titulo": "Processos", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "Escavador", "attr": "Escavador[0].processos", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Número do Processo", "position": "flow", "display": "full", "fields": ["numero do processo"], "additional": [{"attr": "orgao", "display": "<PERSON><PERSON><PERSON>"}, {"attr": "data instauracao", "display": "Data de Instauração"}, {"attr": "data da remessa", "display": "<PERSON> da Remessa"}, {"attr": "instancia", "display": "Instância"}, {"attr": "movimentacoes", "display": "Movimentações"}], "dicts": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "attr": "advogado", "value": "", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON>gado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}, {"attr": "oab", "display": "OAB"}]}]}, {"name": "Autores - Pessoa Física", "attr": "pessoa", "value": "label default key=Autor", "rule": "", "loop": "each", "fields": [{"name": "Autor(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Autores - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Autor", "rule": "", "loop": "each", "fields": [{"name": "Autor(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Réus - Pessoa Física", "attr": "pessoa", "value": "label default key=Reu", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "<PERSON><PERSON><PERSON>", "attr": "empresa - Pessoa <PERSON>", "value": "label default key=Reu", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Requeridos - Pessoa Física", "attr": "pessoa", "value": "label default key=Requerido", "rule": "", "loop": "each", "fields": [{"name": "Requerido(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Requeridos - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Requerido", "rule": "", "loop": "each", "fields": [{"name": "Requerido(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Executados - Pessoa Física", "attr": "pessoa", "value": "label default key=Executado", "rule": "", "loop": "each", "fields": [{"name": "Executado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Executados - Pessoa Jurídica", "attr": "empresa", "value": "label default key=Executado", "rule": "", "loop": "each", "fields": [{"name": "Executado(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Juizo <PERSON>re<PERSON> - Pessoa Jurí<PERSON>", "attr": "empresa", "value": "label default key=<PERSON><PERSON><PERSON>", "rule": "", "loop": "each", "fields": [{"name": "Juizo De<PERSON>recan<PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Juizo Deprecante - Pessoa Física", "attr": "pessoa", "value": "label default key=<PERSON><PERSON><PERSON>", "rule": "", "loop": "each", "fields": [{"name": "Juizo De<PERSON>recan<PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Deprecados - Pessoa Física", "attr": "pessoa", "value": "label default key=Deprecado", "rule": "", "loop": "each", "fields": [{"name": "Deprecado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Deprecados - Pessoa Jurídica", "attr": "empresa", "value": "label default key=Deprecado", "rule": "", "loop": "each", "fields": [{"name": "Deprecado", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Recorrentes - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Recorrente", "rule": "", "loop": "each", "fields": [{"name": "Recorrente", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Recorrentes - Pessoa Física", "attr": "pessoa", "value": "label default key=Recorrente", "rule": "", "loop": "each", "fields": [{"name": "Recorrente", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Outros - Pessoa Física", "attr": "pessoa", "value": "label default key=Outro", "rule": "", "loop": "each", "fields": [{"name": "Parte", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Outros - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Outro", "rule": "", "loop": "each", "fields": [{"name": "Parte", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Impetrantes - Pessoa Física", "attr": "pessoa", "value": "label default key=Impetrante", "rule": "", "loop": "each", "fields": [{"name": "Impetrante", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Impetrantes - <PERSON>ess<PERSON>", "attr": "empresa", "value": "label default key=Impetrante", "rule": "", "loop": "each", "fields": [{"name": "Impetrante", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Pacientes - Pessoa Física", "attr": "pessoa", "value": "label default key=Paciente", "rule": "", "loop": "each", "fields": [{"name": "Paciente", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Pacientes - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Paciente", "rule": "", "loop": "each", "fields": [{"name": "Paciente", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Correus - Pessoa Física", "attr": "pessoa", "value": "label default key=<PERSON>rreu", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Correus - <PERSON>ess<PERSON>", "attr": "empresa", "value": "label default key=<PERSON>rreu", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "label default key=Juiz", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "Impetrados - Pessoa Física", "attr": "pessoa", "value": "label default key=Impetrado", "rule": "", "loop": "each", "fields": [{"name": "Impetrado", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Impetrado - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Impetrado", "rule": "", "loop": "each", "fields": [{"name": "Impetrado", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Interessados - Pessoa Física", "attr": "pessoa", "value": "label default key=Interessado", "rule": "", "loop": "each", "fields": [{"name": "Interessado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Interessados - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Interessado", "rule": "", "loop": "each", "fields": [{"name": "Interessado", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Apelantes - Pessoa Física", "attr": "pessoa", "value": "label default key=Apelante", "rule": "", "loop": "each", "fields": [{"name": "Apelante", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Apelantes - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Apelante", "rule": "", "loop": "each", "fields": [{"name": "Apelante", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "A<PERSON><PERSON>s(as) - Pessoa Física", "attr": "pessoa", "value": "label default key=Apelado", "rule": "", "loop": "each", "fields": [{"name": "Apelado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>(as) - <PERSON><PERSON><PERSON>", "attr": "empresa", "value": "label default key=Apelado", "rule": "", "loop": "each", "fields": [{"name": "Apelado(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Agravados(as) - Pessoa Física", "attr": "pessoa", "value": "label default key=Agravado", "rule": "", "loop": "each", "fields": [{"name": "Agravado(a)", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Agravados - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Agravado", "rule": "", "loop": "each", "fields": [{"name": "Agravado(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Agravantes - Pessoa Física", "attr": "pessoa", "value": "label default key=Agravante", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Agravantes - <PERSON>ess<PERSON>", "attr": "empresa", "value": "label default key=Agravante", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Requerentes - Pessoa Física", "attr": "pessoa", "value": "label default key=<PERSON>querente", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Requerentes - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=<PERSON>querente", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Recorridos - Pessoa Física", "attr": "pessoa", "value": "label default key=Recorrido", "rule": "", "loop": "each", "fields": [{"name": "Recorrido", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Recorridos - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Recorrido", "rule": "", "loop": "each", "fields": [{"name": "Recorrido(a)", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Vítimas - Pessoa Física", "attr": "pessoa", "value": "label default key=Vitima", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Vítimas - <PERSON><PERSON><PERSON>", "attr": "empresa", "value": "label default key=Vitima", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Assistentes - <PERSON><PERSON><PERSON>", "attr": "empresa", "value": "label default key=Assistente", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Assistentes - Pessoa Física", "attr": "pessoa", "value": "label default key=Assistente", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "Representantes - Pessoa <PERSON>", "attr": "empresa", "value": "label default key=Representante", "rule": "", "loop": "each", "fields": [{"name": "Representante", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Representantes - Pessoa Física", "attr": "pessoa", "value": "label default key=Representante", "rule": "", "loop": "each", "fields": [{"name": "Representante", "position": "flow", "display": "full", "fields": "full name", "additional": [{"attr": "cpf", "display": "CPF"}]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "attr": "empresa", "value": "label default key=Ministerio Publico", "rule": "", "loop": "each", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}, {"name": "Litisconsorte", "attr": "empresa", "value": "label default key=Litisconsorte", "rule": "", "loop": "each", "fields": [{"name": "Litisconsorte", "position": "flow", "display": "full", "fields": "razao social", "additional": [{"attr": "cnpj", "display": "CNPJ"}]}]}]}]}]}, "recursospublicos": {"titulo": "Recursos Públicos Recebidos", "descricao": "Dados consultados em portais de Transparência estaduais e federal.", "data": [{"name": "Transparência MG (CPF)", "attr": "PortalDaTransparenciaDeMinasGerais[0]['pessoa']", "value": [], "rule": "", "loop": "once", "meta": "data consulta", "fields": [{"name": "<PERSON><PERSON> (R$)", "position": "flow", "display": "full", "fields": ["valor pago"], "additional": [{"attr": "valor empenhado", "display": "<PERSON><PERSON>"}, {"attr": "valor liquidado", "display": "Valor Liquidado"}]}]}, {"name": "Transparência MG (CPF)", "attr": "PortalDaTransparenciaDeMinasGerais[0]['despesas publicas recursos recebidos']", "value": ["label default key=!Gasto Total"], "rule": "||", "loop": "each", "meta": "data consulta", "fields": [{"name": "Categoria Econômica", "position": "flow", "display": "full", "fields": ["categoria economica"], "additional": [{"attr": "data", "display": "Data Despesa"}, {"attr": "grupo de despesa", "display": "Grupo de Despesas"}, {"attr": "Valor <PERSON>", "display": "valor pago"}, {"attr": "<PERSON><PERSON>", "display": "valor"}, {"attr": "Valor Liquidado", "display": "valor liquidado"}]}]}, {"name": "Transparência AM (CPF)", "attr": "PortalDaTransparenciaDoAmazonas[0]['despesas publicas recursos recebidos'][n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome do Orgão", "position": "flow", "display": "full", "fields": ["nome do orgao"], "additional": [{"attr": "codigo do orgao", "display": "Código do Orgão"}, {"attr": "data", "display": "Data Despesa"}, {"attr": "credor", "display": "C<PERSON>r"}, {"attr": "valor", "display": "Valor"}, {"attr": "pagamento execicio anterior", "display": "Pagamento Execício <PERSON>"}, {"attr": "numero de ordem bancaria", "display": "Nº Ordem Bancária"}, {"attr": "numero da nota de lancamento", "display": "Nº Nota de Lançamento"}, {"attr": "numero do empenho", "display": "Nº Empenho"}, {"attr": "fonte do recurso", "display": "Fonte do Recurso"}, {"attr": "classificacao", "display": "Classificação"}]}]}, {"name": "Transparência SC Credor (CPF)", "attr": "TransparenciaSC[0]['despesas publicas recursos recebidos'][n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome do Orgão", "position": "flow", "display": "full", "fields": ["nome do orgao"], "additional": [{"attr": "data", "display": "Data Despesa"}, {"attr": "credor", "display": "C<PERSON>r"}, {"attr": "unidade gestora", "display": "Unidade Gestora"}, {"attr": "status pagamento", "display": "Status Pagamento"}, {"attr": "pagamento", "display": "Pagamento"}, {"attr": "valor", "display": "Valor"}, {"attr": "numero de ordem bancaria", "display": "Nº Ordem Bancária"}, {"attr": "historico", "display": "Hist<PERSON><PERSON><PERSON>"}]}]}, {"name": "Transparência Manaus (CPF)", "attr": "TransparenciaManausCPF[0].empenho", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Número", "position": "flow", "display": "full", "fields": ["numero"], "additional": [{"attr": "favorecido", "display": "Favorecido"}, {"attr": "unidade gestora", "display": "Unidade Gestora"}, {"attr": "data", "display": "Data"}, {"attr": "valor empenhado", "display": "<PERSON><PERSON> (R$)"}, {"attr": "acrescimo", "display": "Acréscimo (R$)"}, {"attr": "an<PERSON><PERSON>", "display": "Anulado (R$)"}, {"attr": "pago", "display": "<PERSON><PERSON> (R$)"}, {"attr": "liquidado", "display": "<PERSON>or <PERSON>ado (R$)"}]}]}, {"name": "Transparência DF Despesas Credores (CPF)", "attr": "TransparenciaDF[0]['despesas publicas recursos recebidos'][n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Valor (R$)", "position": "flow", "display": "full", "fields": ["valor"], "additional": [{"attr": "data", "display": "Data Despesa"}, {"attr": "credor", "display": "C<PERSON>r"}, {"attr": "valor empenhado", "display": "<PERSON><PERSON>"}, {"attr": "valor liquidado", "display": "Valor liquidado"}]}]}, {"name": "Transparência DF Credor (CPF)", "attr": "TransparenciaSC[0]['despesas publicas recursos recebidos'][n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome do Orgão", "position": "flow", "display": "full", "fields": ["nome do orgao"], "additional": [{"attr": "data", "display": "Data Despesa"}, {"attr": "credor", "display": "C<PERSON>r"}, {"attr": "unidade gestora", "display": "Unidade Gestora"}, {"attr": "status pagamento", "display": "Status Pagamento"}, {"attr": "pagamento", "display": "Pagamento"}, {"attr": "valor", "display": "Valor"}, {"attr": "numero de ordem bancaria", "display": "Nº Ordem Bancária"}, {"attr": "historico", "display": "Hist<PERSON><PERSON><PERSON>"}]}]}, {"name": "Transparência DF Viagem (CPF)", "attr": "TransparenciaDF[0]['viagem'][n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Unidade Gestora", "position": "flow", "display": "full", "fields": ["unidade gestora"], "additional": [{"attr": "ano_mes", "display": "<PERSON><PERSON>/<PERSON>s"}, {"attr": "credor", "display": "C<PERSON>r"}, {"attr": "tipo de despesa", "display": "Tipo de Despesa"}, {"attr": "id", "display": "ID"}, {"attr": "esfera", "display": "Esfera"}, {"attr": "programa de trabalho", "display": "Programa de Trabalho"}, {"attr": "programa de trabalho", "display": "Programa de Trabalho"}, {"attr": "funcao", "display": "Função"}, {"attr": "subfuncao", "display": "Sub-Função"}, {"attr": "programa", "display": "Programa"}, {"attr": "categoria economica", "display": "Categoria Econômica"}, {"attr": "grupo de natureza da despesa", "display": "Grupo de Despesas"}, {"attr": "modalidade de aplicacao", "display": "Modalidade de Aplicação"}, {"attr": "elemento", "display": "Elemento"}, {"attr": "subelemento", "display": "Sub-Elemento"}, {"attr": "fonte de recurso", "display": "Fonte do Recurso"}, {"attr": "<PERSON><PERSON><PERSON><PERSON>", "display": "<PERSON><PERSON> (R$)"}, {"attr": "liquidado", "display": "<PERSON>or <PERSON>ado (R$)"}, {"attr": "pago ex", "display": "Pago Ex (R$)"}, {"attr": "pago rpp", "display": "pago rpp (R$)"}, {"attr": "pago rpnp", "display": "pago rpnp (R$)"}, {"attr": "pago ret", "display": "pago ret (R$)"}, {"attr": "valor", "display": "Valor (R$)"}]}]}]}, "servicopublico": {"titulo": "Serviço Público", "descricao": "Dados consultados em portais de Transparência estaduais e federal.", "data": [{"name": "Transparência PR (Nome)", "attr": "TransparenciaPRNome[0]['servidor publico']", "value": [], "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome do Servidor", "position": "flow", "display": "full", "fields": ["full name"], "additional": [{"attr": "<PERSON><PERSON><PERSON>", "display": "Função"}, {"attr": "instituicao", "display": "Instituição"}, {"attr": "quadro funcional", "display": "Quadro Funcional"}, {"attr": "municipio", "display": "Cidade / PR"}]}]}]}, "diariosoficiaiscpf": {"titulo": "Diários Oficiais - CPF", "descricao": "Dados consultados na API SNAP através do CPF de entrada.", "data": [{"name": "EscavadorDOCPF", "attr": "EscavadorDOCPF[0]['diario oficial']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Local", "position": "flow", "display": "full", "fields": ["local"], "additional": [{"attr": "data", "display": "Data"}, {"attr": "descricao", "display": "Descrição"}, {"attr": "texto correspondente", "display": "Descrição"}, {"attr": "dados adicionais", "display": "<PERSON><PERSON>"}, {"attr": "link", "display": "Link"}]}]}, {"name": "<PERSON><PERSON><PERSON> (CPF)", "attr": "QueridoDiarioCPF[0]['diario oficial']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Cidade", "position": "flow", "display": "concat:' / '", "fields": ["local", "uf"], "additional": [{"attr": "data", "display": "Data"}, {"attr": "edicao extra?", "display": "Edição Extra?"}, {"attr": "link", "display": "Link"}], "dicts": [{"name": "Ocorrências", "attr": "frase", "value": "", "rule": "", "loop": "each", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "texto"}]}]}]}]}, "diariosoficiaisnome": {"titulo": "Diários Oficiais - NOME", "descricao": "Dados consultados na API SNAP através do NOME da pessoa.", "data": [{"name": "EscavadorDONome", "attr": "EscavadorDONome[0]['diario oficial']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Local", "position": "flow", "display": "full", "fields": ["local"], "additional": [{"attr": "data", "display": "Data"}, {"attr": "descricao", "display": "Descrição"}, {"attr": "texto correspondente", "display": "Descrição"}, {"attr": "dados adicionais", "display": "<PERSON><PERSON>"}, {"attr": "link", "display": "Link"}]}]}, {"name": "<PERSON><PERSON><PERSON> (Nome)", "attr": "QueridoDiarioNome[0]['diario oficial']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Cidade", "position": "flow", "display": "concat:' / '", "fields": ["local", "uf"], "additional": [{"attr": "data", "display": "Data"}, {"attr": "edicao extra?", "display": "Edição Extra?"}, {"attr": "link", "display": "Link"}], "dicts": [{"name": "Ocorrências", "attr": "frase", "value": "", "rule": "", "loop": "each", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "texto"}]}]}]}]}, "filiacaopartidaria": {"titulo": "Filiação Partidária", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "TSE", "attr": "TSEFiliacaoPartidaria[0]['partido politico']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Sig<PERSON>", "position": "flow", "display": "full", "fields": ["sigla"], "additional": [{"attr": "uf", "display": "Estado"}], "dicts": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Data Registro", "position": "flow", "display": "full", "fields": "data registro"}, {"name": "Situação", "position": "flow", "display": "full", "fields": "situacao"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "tipo"}, {"name": "Data Desfiliação", "position": "flow", "display": "full", "fields": "data desfiliacao"}, {"name": "Data Cancelamento", "position": "flow", "display": "full", "fields": "data cancelamento"}, {"name": "Motivo Cancelamento", "position": "flow", "display": "full", "fields": "motivo cancelamento"}]}]}]}]}, "contatos": {"titulo": "Possíveis <PERSON>", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "SNAP", "attr": "SNAP[0].pessoa", "value": ["label default key=!parente MAE", "label default key=!parente IRMA(O)", "label default key=!parente PAI", "label default key=!parente TIO(A)", "label default key=!parente AVOS", "label default key=!parente FILHO(A)", "label default key=!parente CONJUGE", "grau de parentesco=!MAE", "bookmark=!4"], "rule": "&&", "loop": "each", "meta": "data consulta", "fields": [{"name": "Contato", "position": "flow", "display": "concat:' '", "fields": ["first names", "surname"], "additional": [{"attr": "nome1", "display": ""}, {"attr": "label default key", "display": "Parentesco"}, {"attr": "cpf", "display": "CPF"}, {"attr": "titulo de eleitor", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"attr": "data nascimento", "display": "Data de Nascimento"}, {"attr": "sexo", "display": "Sexo"}, {"attr": "procon", "display": "PROCON"}], "dicts": [{"name": "<PERSON><PERSON><PERSON>", "attr": "pessoa", "value": "label=parente MAE", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "label=parente PAI", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereços", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Logradouro", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}, {"name": "SNAP", "attr": "SNAP[0].pessoa", "value": ["label default key=undefined", "bookmark=undefined"], "rule": "&&", "loop": "each", "meta": "data consulta", "fields": [{"name": "Contato", "position": "flow", "display": "concat:' '", "fields": ["first names", "surname"], "additional": [{"attr": "nome1", "display": ""}, {"attr": "label default key", "display": "Parentesco"}, {"attr": "cpf", "display": "CPF"}, {"attr": "titulo de eleitor", "display": "<PERSON><PERSON><PERSON><PERSON>"}, {"attr": "data nascimento", "display": "Data de Nascimento"}, {"attr": "sexo", "display": "Sexo"}, {"attr": "procon", "display": "PROCON"}], "dicts": [{"name": "<PERSON><PERSON><PERSON>", "attr": "pessoa", "value": "label=parente MAE", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "<PERSON><PERSON>", "attr": "pessoa", "value": "label=parente PAI", "rule": "||", "loop": "once", "fields": [{"name": "", "position": "flow", "display": "full", "fields": "full name"}]}, {"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereços", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Logradouro", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}, {"name": "SNAP", "attr": "SNAP[0].empresa", "value": ["label default key=outros_contatos"], "rule": "&&", "loop": "each", "meta": "data consulta", "fields": [{"name": "Contato", "position": "flow", "display": "concat:' '", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}], "dicts": [{"name": "Telefones", "attr": "phonenumber", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Telefone", "position": "flow", "display": "full", "fields": "phone number"}]}, {"name": "Endereços", "attr": "location", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Logradouro", "position": "flow", "display": "full", "fields": "endereco", "additional": [{"attr": "numero", "display": "Número"}, {"attr": "bairro", "display": "Bairro"}, {"attr": "city", "display": "Cidade"}, {"attr": "area", "display": "Estado"}, {"attr": "cep ou zipcode", "display": "CEP"}, {"attr": "area code", "display": "CEP"}, {"attr": "telefone relacionado", "display": "Telefone"}]}]}]}]}]}, "contas": {"titulo": "Possíveis Contas em Sites", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "PAIs", "attr": "PAIscpf[0].PAIs", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Site", "position": "flow", "display": "full", "fields": ["aplicacao"], "additional": [{"attr": "found", "display": "Conta encontrada?"}, {"attr": "alerta", "display": "Alvo alertado?"}, {"attr": "tipo_alerta", "display": "Tipo de Alerta"}]}]}, {"name": "PAIsSNAP", "attr": "ProvedorDeAplicacacaoDaInternetcpf[0]['provedor de aplicacoes de internet']", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Site", "position": "flow", "display": "full", "fields": ["aplicacao"], "additional": [{"attr": "found", "display": "Conta encontrada?"}, {"attr": "alerta", "display": "Alvo alertado?"}, {"attr": "tipo_alerta", "display": "Tipo de Alerta"}]}]}]}, "doacoesenviadas": {"titulo": "Doações Enviadas Campanha", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "TSE Doações Enviadas Campanha", "attr": "TSEDoacoes[0].pessoa[0].candidato", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Candi<PERSON><PERSON>", "position": "flow", "display": "full", "fields": ["full name"], "additional": [{"attr": "cpf", "display": "CPF"}, {"attr": "ano", "display": "<PERSON><PERSON>"}, {"attr": "cargo eleitoral", "display": "<PERSON><PERSON>"}, {"attr": "unidade eleitoral", "display": "Unidade Eleitoral"}, {"attr": "número do candidato", "display": "Número"}, {"attr": "partido eleitoral", "display": "Partido"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}]}, "doacoesrecebidas": {"titulo": "Doações Recebidas Campanha", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "TSE Doações Recebidas Campanha", "attr": "TSEDoacoes[0].candidato[0].empresa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Razão Social", "position": "flow", "display": "full", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "cnae", "display": "CNAE"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}, {"name": "TSE Doações Recebidas Campanha", "attr": "TSEDoacoes[0].candidato[0].pessoa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome <PERSON>to", "position": "flow", "display": "full", "fields": ["full name"], "additional": [{"attr": "cpf", "display": "CPF"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}, {"name": "TSE Doações Recebidas Campanha", "attr": "TSEDoacoes[0].candidato[1].empresa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Razão Social", "position": "flow", "display": "full", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "cnae", "display": "CNAE"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}, {"name": "TSE Doações Recebidas Campanha", "attr": "TSEDoacoes[0].candidato[1].pessoa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Nome <PERSON>to", "position": "flow", "display": "full", "fields": ["full name"], "additional": [{"attr": "cpf", "display": "CPF"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}]}, "fornecimentosenviadas": {"titulo": "Fornecimentos Enviados Campanha", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "TSE Fornecimentos Enviados Campanha", "attr": "TSEFornecimento[0].candidato[0].empresa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Razão Social", "position": "flow", "display": "full", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "cnae", "display": "CNAE"}], "dicts": [{"name": "Fornecimentos", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}, {"name": "TSE Fornecimentos Enviados Campanha", "attr": "TSEFornecimento[0].candidato[1].empresa[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Razão Social", "position": "flow", "display": "full", "fields": ["razao social"], "additional": [{"attr": "cnpj", "display": "CNPJ"}, {"attr": "cnae", "display": "CNAE"}], "dicts": [{"name": "Fornecimentos", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}]}, "fornecimentosrecebidos": {"titulo": "Fornecimentos Recebidos Campanha", "descricao": "Dados consultados na API SNAP.", "data": [{"name": "TSE Fornecimentos Recebidas", "attr": "TSEFornecimento[0].pessoa[0].candidato[n]", "value": "", "rule": "", "loop": "each", "meta": "data consulta", "fields": [{"name": "Candi<PERSON><PERSON>", "position": "flow", "display": "full", "fields": ["full name"], "additional": [{"attr": "cpf", "display": "CPF"}, {"attr": "ano da eleição", "display": "Ano Da Eleição"}, {"attr": "cargo eleitoral", "display": "<PERSON><PERSON>"}, {"attr": "unidade eleitoral", "display": "Unidade Eleitoral"}, {"attr": "número do candidato", "display": "Número"}, {"attr": "partido eleitoral", "display": "Partido"}], "dicts": [{"name": "Doações", "attr": "vinculo", "value": "", "rule": "", "loop": "each", "fields": [{"name": "Valor", "position": "flow", "display": "full", "fields": "valor"}, {"name": "Data", "position": "flow", "display": "full", "fields": "data"}, {"name": "Documento", "position": "flow", "display": "full", "fields": "documento"}, {"name": "Recibo", "position": "flow", "display": "full", "fields": "recibo"}, {"name": "Descrição", "position": "flow", "display": "full", "fields": "descricao"}, {"name": "Espécie", "position": "flow", "display": "full", "fields": "especie"}, {"name": "Natureza", "position": "flow", "display": "full", "fields": "natureza"}, {"name": "<PERSON><PERSON>", "position": "flow", "display": "full", "fields": "natureza estimavel"}, {"name": "Origem", "position": "flow", "display": "full", "fields": "origem"}, {"name": "Fonte", "position": "flow", "display": "full", "fields": "fonte"}, {"name": "Tipo", "position": "flow", "display": "full", "fields": "Tipo"}]}]}]}]}}