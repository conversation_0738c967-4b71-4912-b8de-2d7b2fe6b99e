import { List, ListItem, Menu, MenuItem, Text } from '@snap/design-system'
import { FiTool } from 'react-icons/fi'
import { formatIsoDate, isEmptyObject } from '~/helpers';
import { useUserData } from '~/store/userStore'

interface UserMenuContentProps {
  menuActions?: MenuItem[] | null;
  showRemainingCredits?: boolean;
  classNameContainer?: string;
}

const UserMenuContent = ({ menuActions = null, showRemainingCredits = false, classNameContainer = "" }: UserMenuContentProps) => {
  const userData = useUserData();
  const getConsultasRealizadas = () => {
    const reportSum = Object.values(userData?.report_types || {}).reduce((acc, cur) => acc + cur, 0);
    return reportSum;
  };

  return (
    <div>
      {/* tag tipo perfil */}
      <div className="flex items-center gap-2 bg-[#2F3240] p-3 mb-1">
        <FiTool size={16} />
        <Text variant="body-md" className="font-semibold capitalize">{userData?.account_type || "Não Informado"}</Text>
      </div>

      <div className={`flex flex-col gap-4 p-4 ${classNameContainer}`}>
        {/* consultas disponíveis */}
        {
          showRemainingCredits ? (
            <div className="flex items-center gap-2 justify-between">
              <div className="flex flex-col items-start gap-0.5">
                <Text variant="body-lg" className="font-semibold">Consultas disponíveis</Text>
                <Text variant="body-sm" className="opacity-80">{`Válidas até ${formatIsoDate(userData?.valid_until || "")}`}</Text>
              </div>
              <Text variant="title-lg" className="font-semibold">{userData?.credits}</Text>
            </div>
          ) : null
        }
        <List className="flex flex-col gap-4">
          {/* consultas realizadas */}
          <ListItem className="flex items-center gap-2 bg-accent py-1.5 px-2 justify-between rounded-xs">
            <Text variant="body-md">Consultas realizadas:</Text>
            <Text variant="body-md" className="font-semibold">{getConsultasRealizadas()}</Text>
          </ListItem>
          {/* tipos de relatório */}
          <div className="flex flex-col gap-0.5">
            {userData?.report_types &&
              !isEmptyObject(userData?.report_types) ? (
              Object.entries(userData?.report_types).map(([key, value], index) => (
                <ListItem
                  key={index}
                  className="flex items-center gap-2 bg-card py-1.5 px-2 justify-between rounded-xs uppercase"
                >
                  <Text >{key}</Text>
                  <Text variant="body-md" className="font-semibold">{value}</Text>
                </ListItem>
              ))
            ) : (
              <ListItem className="opacity-80">
                Nenhum tipo de relatório permitido.
              </ListItem>
            )}
          </div>
        </List>
        {menuActions ?
          <Menu
            items={menuActions}
            separator
            gap="none"
          /> : null
        }
      </div>

    </div>
  )
}

export default UserMenuContent