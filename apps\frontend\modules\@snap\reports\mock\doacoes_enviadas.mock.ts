import { DoacaoEnviada } from "../model/DoacoesEnviadas";

// Mock for a donation to a candidate
const doacaoEnviada1: DoacaoEnviada = {
    candidato: {
        value: "<PERSON>",
        label: "Candidato",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cpf: {
            value: "123.456.789-00",
            label: "CPF",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        ano: {
            value: "2022",
            label: "Ano",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "cargo eleitoral": {
            value: "Vereador",
            label: "Cargo Eleitoral",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "unidade eleitoral": {
            value: "São Paulo / SP",
            label: "Unidade Eleitoral",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "número do candidato": {
            value: "12345",
            label: "Número",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "partido eleitoral": {
            value: "Partido ABC",
            label: "Partido",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor: {
                    value: "R$ 1.000,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "10/05/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "123456789",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para campanha eleitoral",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Física",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        {
            value: {
                valor: {
                    value: "R$ 500,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "15/08/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "987654321",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para material de campanha",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Depósito em espécie",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Física",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Mock for another donation to a different candidate
const doacaoEnviada2: DoacaoEnviada = {
    candidato: {
        value: "Maria Oliveira",
        label: "Candidato",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cpf: {
            value: "987.654.321-00",
            label: "CPF",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        ano: {
            value: "2022",
            label: "Ano",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "cargo eleitoral": {
            value: "Deputado Estadual",
            label: "Cargo Eleitoral",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "unidade eleitoral": {
            value: "Rio de Janeiro / RJ",
            label: "Unidade Eleitoral",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "número do candidato": {
            value: "54321",
            label: "Número",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        "partido eleitoral": {
            value: "Partido XYZ",
            label: "Partido",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor: {
                    value: "R$ 2.000,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "20/07/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "246813579",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para campanha eleitoral",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Física",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Export the complete mock
export const doacoesEnviadasMock: DoacaoEnviada[] = [
    doacaoEnviada1,
    doacaoEnviada2
];
