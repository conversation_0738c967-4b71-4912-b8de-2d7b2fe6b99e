import { ValueWithSource } from "./ValueWithSource";

interface _PessoaBase {
  cpf?: string;
  "full name": string;
}

interface _Pena {
  "pena imposta": ValueWithSource;
  recaptura: ValueWithSource;
}

interface _Mandado {
  "numero do mandado de prisao": string;
  "tipo de mandado": string;
  situacao: string;
  "numero do processo": string;
  "tipificacoes penais": string;
  "data de expedicao": string;
  "data de validade": string;
  "especie de prisao": string;
  municipio: string;
  "orgao expedidor": string;
  magistrado: string;

  pessoa?: Array<_PessoaBase>;
  pena?: _Pena;
}

export interface Mandado {
  numero: ValueWithSource<_Mandado["numero do mandado de prisao"]>;
  detalhes: Record<
    keyof Omit<
      _Mandado,
      "numero do mandado de prisao" | "pessoa" | "pena"
    >,
    ValueWithSource<string>
  >;
  pena?: ValueWithSource<_Pena>;
  pessoa?: Array<ValueWithSource<_PessoaBase>>;
}