import React from "react";
import { DiarioOficialCPF } from "../../model/DiariosOficiaisCPF";
import { RenderStrategy } from "./RenderStrategy";
import { ReadOnlyInputField, GridItem } from "@snap/design-system";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { parseValue } from "../../helpers";
import { ValueWithSource } from "../../model/ValueWithSource";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export class RenderDiariosOficiaisCPF implements RenderStrategy<DiarioOficialCPF> {

    validateKeys = (keys: Array<keyof DiarioOficialCPF>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    formatByKey: Record<
        string,
        (diarioOficial?: DiarioOficialCPF) => React.ReactElement | null
    > = {
            local: (diarioOficial?: DiarioOficialCPF) => {
                if (!diarioOficial?.local) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(diarioOficial.local.label || "Local").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={String(diarioOficial.local.value || "")}
                                tooltip={renderSourceTooltip(diarioOficial.local.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (diarioOficial?: DiarioOficialCPF) => {
                if (!diarioOficial?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} columnFirst className="mb-6">
                        {Object.entries(diarioOficial.detalhes).map(([key, value], index) => {
                            // Cast value to ValueWithSource type
                            const valorComFonte = value as ValueWithSource<string>;
                            return (
                                <GridItem key={`detalhes-${index}-${key}`} cols={1} className="group">
                                    <CustomReadOnlyInputField
                                        label={`${(translatePropToLabel(valorComFonte.label)).toUpperCase()}`}
                                        value={parseValue(String(valorComFonte.value))}
                                        tooltip={renderSourceTooltip(valorComFonte.source)}
                                    />
                                </GridItem>
                            );
                        })}
                    </CustomGridContainer>
                );
            },

            descrição: (diarioOficial?: DiarioOficialCPF) => {
                if (!diarioOficial?.descrição) return null;

                return (
                    <CustomGridContainer cols={3} className="mb-6">
                        <GridItem cols={3} className="group">
                            <CustomReadOnlyInputField
                                label={`${(translatePropToLabel(diarioOficial.descrição.label || "Descrição")).toUpperCase()}`}
                                value={String(diarioOficial.descrição.value)}
                                element="textarea"
                                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                tooltip={renderSourceTooltip(diarioOficial.descrição.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            "texto correspondente": (diarioOficial?: DiarioOficialCPF) => {
                if (!diarioOficial?.["texto correspondente"]) return null;

                return (
                    <CustomGridContainer cols={3} className="mb-6">
                        <GridItem cols={3} className="group">
                            <CustomReadOnlyInputField
                                label={`${(translatePropToLabel(diarioOficial["texto correspondente"].label || "Descrição")).toUpperCase()}`}
                                value={String(diarioOficial["texto correspondente"].value)}
                                element="textarea"
                                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                tooltip={renderSourceTooltip(diarioOficial["texto correspondente"].source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            }


        }

    render = (diarioOficial?: DiarioOficialCPF): React.ReactElement[] => {
        if (!diarioOficial) return [];

        const keys = Object.keys(diarioOficial) as Array<keyof DiarioOficialCPF>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof DiarioOficialCPF> = [
            'local',
            'detalhes',
            'descrição',
            "texto correspondente"
        ];

        // Filter the keys to only include those that exist in the diarioOficial object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(diarioOficial);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
