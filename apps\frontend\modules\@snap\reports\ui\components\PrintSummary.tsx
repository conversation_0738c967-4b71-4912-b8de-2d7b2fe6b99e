import type React from "react"
import { View, Text, StyleSheet, Link } from "@react-pdf/renderer"

interface SummaryItem {
  title: string
  data_count: number
}

const PrintSummary: React.FC<{ items: SummaryItem[], searchType: string, searchValue: string }> = ({
  items,
  searchType, searchValue
}) => {
  const total = items.reduce((acc, item) => acc + item.data_count, 0)
  return (
    <View style={styles.summaryContainer}>
      <Text style={styles.entradasHeader}>ENTRADAS</Text>

      <View style={styles.entryContainer}>
        <Text style={styles.entryLabel}>{searchType}</Text>
        <View style={styles.entryBox}>
          <Text style={styles.entryValue}>{searchValue}</Text>
        </View>
      </View>

      <View style={styles.registrosSection}>
        <Text style={styles.registrosHeader}>{`${total} REGISTROS ENCONTRADOS`}</Text>

        <View style={styles.registrosList}>
          {items.map((item, idx) => (
            <View key={idx} style={styles.registroItem}>
              <Text style={styles.registroTitle}>{item.title}</Text>
              <View style={styles.countBadge}>
                <Text style={styles.countText}>{item.data_count}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  summaryContainer: {
    width: "54%",
    paddingLeft: 16,
  },
  entradasHeader: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  entryContainer: {
    marginBottom: 24,
  },
  entryLabel: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#FE473C",
    textTransform: "uppercase",
    marginBottom: 6,
    letterSpacing: 0.5,
  },
  entryBox: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: "#E5E7EB",
    paddingVertical: 8,
  },
  entryValue: {
    fontSize: 10,
    color: "#1F2937",
  },
  registrosSection: {
    backgroundColor: "#E5E7EB",
    borderRadius: 8,
    padding: 16,
  },
  registrosHeader: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#374151",
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  registrosList: {
    flexDirection: "column",
  },
  registroItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 4,
  },
  registroTitle: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#374151",
    flex: 1,
  },
  countBadge: {
    backgroundColor: "#6B7280",
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    minWidth: 28,
    alignItems: "center",
  },
  countText: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#FFFFFF",
    textAlign: "center",
  },
})

export default PrintSummary