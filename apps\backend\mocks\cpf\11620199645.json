{"cpf": [{"SNAP": [{"pessoa": [{"bookmark": 4, "cpf": "116.201.996-45", "info restricao": "* NADA CONSTA *", "first names": "MARIANA", "surname": "LUIZA LOPES BARROS", "full name": "MARIANA LUIZA LOPES BARROS", "status receita": "REGULAR", "pais do passaporte": "BRASIL", "sexo": "F", "pessoa": [{"full name": "ANA MARIA LOPES BARROS", "label default key": "parente MAE"}, {"full name": "ECIO MILAGRES DE BARROS", "label default key": "parente PAI"}], "nome mae": "ANA MARIA LOPES", "data nascimento": "08/11/1991", "idade": "33", "signo": "ESCORPIAO", "procon": "(NAO TEM)"}, {"first names": "ANA", "surname": "MARIA LOPES", "full name": "ANA MARIA LOPES", "phonenumber": [{"phone number": "553141330464", "operadora": "CLARO"}, {"phone number": "5531999322482", "operadora": "VIVO"}, {"phone number": "5531991084725", "operadora": "TIM"}, {"phone number": "553136744525", "operadora": "OI"}], "cpf": "59634952615", "sexo": "F", "pessoa": [{"full name": "MARIA HELENA LOPES", "label default key": "parente MAE"}], "data nascimento": "26/02/1967", "titulo de eleitor": "038733260299", "procon": "(NAO TEM)", "location": [{"logradouro": "R JOSE MGLHES BRBOS", "label default key": "553141330464", "numero": "85", "complemento": "A", "bairro": "CENTRO", "cep ou zipcode": "34505510", "city": "SABARA", "area": "MG"}, {"logradouro": "R ABREU GUIMARAES", "label default key": "5531999322482", "numero": "2412", "bairro": "CENTRO", "cep ou zipcode": "34505250", "city": "SABARA", "area": "MG"}, {"logradouro": "ABREU GUIMARAES", "label default key": "5531991084725", "numero": "212", "bairro": "CENTRO", "cep ou zipcode": "34505250", "city": "SABARA", "area": "MG"}, {"logradouro": "R JOSE MAGALHAES BARBOSA", "label default key": "553136744525", "numero": "85", "bairro": "CENTRO", "cep ou zipcode": "34505510", "city": "SABARA", "area": "MG"}], "label default key": "parente MAE", "credilink label": "parente MAE", "full name1": "ANA MARIA LOPES BARROS", "label default key1": "outros contatos", "credilink label1": "outros contatos"}, {"first names": "JOAO", "surname": "RAFAEL LOPES CAMPERA", "full name": "JOAO RAFAEL LOPES CAMPERA", "phonenumber": [{"phone number": "5531994085604", "operadora": "CLARO"}], "cpf": "07872270641", "sexo": "M", "pessoa": [{"full name": "MARIA CELESTE LOPES", "label default key": "parente MAE"}], "data nascimento": "15/10/1986", "procon": "(NAO TEM)", "location": [{"logradouro": "R DIANA", "label default key": "5531994085604", "numero": "765", "complemento": "AP 202", "bairro": "ANA LUCIA", "cep ou zipcode": "34710210", "city": "SABARA", "area": "MG"}], "label default key": "parente PRIMO(A)", "credilink label": "parente PRIMO(A)"}, {"first names": "MARCELA", "surname": "HELENA LOPES", "full name": "MARCELA HELENA LOPES", "phonenumber": [{"phone number": "5531995527449", "operadora": "VIVO"}, {"phone number": "5531996893413", "operadora": "TELEMIG"}, {"phone number": "5531987611772", "operadora": "OI"}, {"phone number": "553136745260", "operadora": "OI"}], "cpf": "10877934673", "sexo": "F", "pessoa": [{"full name": "MARIA CELESTE LOPES", "label default key": "parente MAE"}], "data nascimento": "19/01/1992", "procon": "(NAO TEM)", "location": [{"logradouro": "R ABREU GUIMARAES", "label default key": "5531996893413", "numero": "212", "bairro": "CENTRO", "cep ou zipcode": "34505250", "city": "SABARA", "area": "MG"}, {"logradouro": "RABREU GUIMARAES", "label default key": "553136745260", "numero": "212", "bairro": "CENTRO", "cep ou zipcode": "34505250", "city": "SABARA", "area": "MG"}], "label default key": "parente PRIMO(A)", "credilink label": "parente PRIMO(A)"}, {"first names": "MARIA", "surname": "HELENA LOPES", "full name": "MARIA HELENA LOPES", "cpf": "03126211674", "sexo": "F", "pessoa": [{"full name": "LUIZA AUGUSTA", "label default key": "parente MAE"}], "data nascimento": "04/09/1932", "procon": "(NAO TEM)", "label default key": "parente AVOS", "credilink label": "parente AVOS"}, {"first names": "RODNEY", "surname": "BRAGA PIRES", "full name": "RODNEY BRAGA PIRES", "phonenumber": [{"phone number": "553132221439", "operadora": "OI"}], "cpf": "73716065668", "sexo": "M", "pessoa": [{"full name": "MARIA HELENA BRAGA PIRES", "label default key": "parente MAE"}], "data nascimento": "26/09/1969", "titulo de eleitor": "086476670205", "procon": "(NAO TEM)", "location": [{"logradouro": "R EPAMINONDAS DE MOURA E SILVA", "label default key": "553132221439", "bairro": "PLANALTO", "cep ou zipcode": "31720580", "city": "BELO HORIZONTE", "area": "MG"}], "label default key": "outros contatos", "credilink label": "outros contatos"}, {"first names": "SILVIA", "surname": "MARIA LOPES", "full name": "SILVIA MARIA LOPES", "phonenumber": [{"phone number": "5531998379809", "operadora": "CLARO"}], "cpf": "00826486622", "sexo": "F", "pessoa": [{"full name": "MARIA HELENA LOPES", "label default key": "parente MAE"}], "data nascimento": "18/03/1975", "titulo de eleitor": "113378640256", "procon": "(NAO TEM)", "location": [{"logradouro": "JOSE ANTONIO FABRICIO", "label default key": "5531998379809", "numero": "100", "cep ou zipcode": "85450000", "city": "CAMPO BONITO", "area": "PR"}], "label default key": "parente TIO(A)", "credilink label": "parente TIO(A)"}], "phonenumber": [{"phone number": "5531999073413", "operadora": "VIVO", "bookmark": "1", "whatsapp": "<PERSON>m"}], "location": [{"logradouro": "R ABREU GUIMARAES", "numero": "212", "bairro": "CENTRO", "cep ou zipcode": "34505250", "city": "SABARA", "area": "MG"}], "emailaddress": [{"email address": "<EMAIL>"}, {"email address": "mari<PERSON><EMAIL>"}, {"email address": "<EMAIL>"}], "empresa": [{"razao social": "MARIANA LUIZA LOPES BARROS 11620199645", "cnpj": "33258772000187", "codigo de cargo na sociedade": "50", "participacao na sociedade": "100", "location": [{"logradouro": "RUA AMELIA ZEFERINA DE FREITAS", "numero": "176", "bairro": "TRES BARRAS", "city": "CONTAGEM", "cep ou zipcode": "32041160", "area": "MG"}], "label default key": "socio", "credilink label": "socio"}]}], "Banco Nacional de Medidas Penais e Prisões": [{"error_message": "Nao foram encontrados resultados!"}], "Cadastro Nacional PJ": [{"error_message": "Nao foram encontrados resultados!"}], "Escavador": [{"error_message": "Nao foram encontrados resultados!"}], "EscavadorDONome": [{"error_message": "Nao foram encontrados resultados!"}], "EscavadorDOCPF": [{"error_message": "Nao foram encontrados resultados!"}], "Provedor de Aplicacação da Internet": [{"provedor de aplicacoes de internet": [{"nome": "BrasilBitcoin", "termo procurado": "116.201.996-45", "existe": false}, {"nome": "CasasBahia", "termo procurado": "116.201.996-45", "existe": "Erro: A aplicacao bloqueou a requisicao."}, {"nome": "Digipare", "termo procurado": "116.201.996-45", "existe": false}, {"nome": "Extra", "termo procurado": "116.201.996-45", "existe": "Erro: A aplicacao bloqueou a requisicao."}, {"nome": "PontoFrio", "termo procurado": "116.201.996-45", "existe": "Erro: A aplicacao bloqueou a requisicao."}]}], "Querido DiárioCPF": [{"diario oficial": []}], "Querido DiárioNome": [{"diario oficial": []}], "Tribunal Superior Eleitoral Doadores": [{"error_message": "Nao foram encontrados resultados!"}], "Tribunal Superior Eleitoral Fornecimento": [{"error_message": "Nao foram encontrados resultados!"}], "Sintegra MG": [{"error_message": "Nao foram encontrados resultados!"}], "Portal da Transparência de Minas Gerais": [{"pessoa": [], "despesas publicas recursos recebidos": []}]}]}