import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { Endereco } from "../../model/Enderecos";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { GridItem, Input, CustomLabel } from "@snap/design-system";
import { translatePropToLabel, getSingular, } from "../../helpers";
import { getFieldLabel, getFieldValue, renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export function useRenderEnderecos(sectionTitle: string): RenderStrategy<Endereco> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // block‐level: show if *any* subfield matches
  const shouldIncludeBlock = (detalhe: any) => {
    const vals = Object.values(detalhe.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const onToggleField = (blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value?.[fieldKey]) {
          detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;
        }
      },
      // entry-deleted when *all* subfields deleted
      (entry) =>
        entry.detalhes?.every((d: any) =>
          Object.values(d.value).every((v: any) => v.is_deleted === true)
        ) ?? false,
      // section deleted when every entry is deleted
      (section) =>
        Array.isArray(section.data) &&
        section.data.every((e) =>
          e.detalhes?.every((d: any) =>
            Object.values(d.value).every((v: any) => v.is_deleted === true)
          )
        )
    );
  };

  const formatByKey: Record<
    string,
    (endereco?: Endereco) => React.ReactElement | null
  > = {
    detalhes: (endereco) => {
      if (!endereco?.detalhes?.length) return null;

      // pair with original index
      const blocks = endereco.detalhes
        .map((d, idx) => ({ bloco: d, idx }))
        .filter(({ bloco }) => shouldIncludeBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} className="mb-5">
          {/* Blocks */}
          <GridItem fullWidth>
            <CustomGridContainer cols={2}>
              {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
                <GridItem key={`end-${origIdx}`} cols={1}>
                  {/* Generic block label */}
                  <div className="mb-4">
                    <CustomLabel
                      label={`ENDEREÇO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                      colorClass="bg-primary"
                      icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                    />
                  </div>
                  <div className="pl-5">
                    {/* Only fields matching mode */}
                    {Object.entries(bloco.value)
                      .filter(([_, v]: any) =>
                        isTrash ? v.is_deleted : !v.is_deleted
                      )
                      .map(([fieldKey, fieldValue]: any) => (
                        <CustomGridItem
                          key={`end-${origIdx}-${fieldKey}`}
                          cols={1}
                          className="py-2"
                          onToggleField={() => onToggleField(origIdx, fieldKey)}
                        >
                          {/* <CustomLabel
                            label={translatePropToLabel(
                              getFieldLabel(fieldKey, fieldValue)
                            ).toUpperCase()}
                            colorClass="bg-border"
                            icon={
                              <MdOutlineSubdirectoryArrowRight size={16} />
                            }
                          />
                          <Input
                            onFocus={(e) => e.target.select()}
                            value={String(getFieldValue(fieldValue) || "")}
                            readOnly
                            className="border-dashed"
                          /> */}
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(
                              getFieldLabel(fieldKey, fieldValue)
                            ).toUpperCase()}
                            colorClass="bg-border"
                            icon={
                              <MdOutlineSubdirectoryArrowRight size={16} />
                            }
                            value={String(getFieldValue(fieldValue) || "")}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                          />
                        </CustomGridItem>
                      ))}
                  </div>
                </GridItem>
              ))}
            </CustomGridContainer>
          </GridItem>
        </CustomGridContainer>
      );
    },
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) =>
      Object.values(d.value).every((v: any) => v.is_deleted === true)
    ) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  const validateKeys = (keys: Array<keyof Endereco>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const render = (endereco: Endereco): React.ReactElement[] => {
    const keys = Object.keys(endereco) as Array<keyof Endereco>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Endereços] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(endereco))
      .filter((el): el is React.ReactElement => el !== null);
  };

  return {
    validateKeys,
    formatByKey,
    render,
  };
}
