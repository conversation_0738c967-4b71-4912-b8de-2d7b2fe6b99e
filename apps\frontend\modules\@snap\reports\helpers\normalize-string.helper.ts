import { NormalizeOptions } from "../global";

/**
 * Advanced string normalizer with configurable options for special character handling
 * and case transformation. Safely processes strings while handling invalid inputs.
 *
 * @param value - Input value to normalize (any type, string processing only)
 * @param options - Configuration options for normalization behavior
 *
 * @returns Normalized string cleaned of diacritics and special characters,
 *          or empty string for invalid inputs
 *
 * @example
 * // Basic usage
 * normalizeStringAdvanced("Nĭñja_Çõmpâny"); // "Ninja_Company"
 *
 * // With options
 * normalizeStringAdvanced("Relações_2024!", {
 *   case: 'lower',
 *   allowSpecialChars: /[^a-zA-Z0-9_-]/g
 * }); // "relacoes_2024"
 */
export const normalizeString = (
  value: unknown,
  options: NormalizeOptions = {}
): string => {
  if (typeof value !== "string") return "";

  let normalized = value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  if (options.allowSpecialChars) {
    normalized = normalized.replace(options.allowSpecialChars, "");
  }

  switch (options.case) {
    case "lower":
      return normalized.toLowerCase();
    case "upper":
      return normalized.toUpperCase();
    default:
      return normalized;
  }
};