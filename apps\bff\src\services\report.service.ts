import { EncryptedData, ReportDetails } from "../models/reportDetails.model";
import fs from "fs/promises";
import path from "path";

const REPORTS_DIR = path.join(__dirname, "../../mocks");

export const fetchReportList = async (): Promise<EncryptedData | null> => {
  try {
    const listFilePath = path.join(REPORTS_DIR, "reports_list_encrypted.json");
    const rawData = await fs.readFile(listFilePath, "utf-8");
    const jsonData = JSON.parse(rawData);
    return jsonData;
  } catch (error) {
    console.error("Error reading report list:", error);
    return null;
  }
};

export const fetchReportData = async (
  id: string
): Promise<EncryptedData | null> => {
  try {
    const filePath = path.join(REPORTS_DIR, `${id}_encrypted.json`);
    const rawData = await fs.readFile(filePath, "utf-8");
    const jsonData = JSON.parse(rawData);

    console.log("filePath: ", filePath);
    console.log("rawData: ", rawData);

    return jsonData;
  } catch (error) {
    console.error(`Error reading report ${id}:`, error);
    return null;
  }
};
