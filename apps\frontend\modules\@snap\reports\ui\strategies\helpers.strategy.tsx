import React from "react";
import { ReadOnlyInputField, CustomLabel, Input, Separator, Text } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { getSingular, parseValue, translatePropToLabel, translateSource } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import genericConstants from "../../config/word-map/nomes_genericos.json";
import { CustomGridItem } from "../components/CustomGridItem";
import { Info } from "lucide-react";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

/**
 * Gets the appropriate label for a field based on its structure
 * @param key The field key
 * @param value The field value (which might be an object with a label property)
 * @returns The appropriate label for the field
 */
export const getFieldLabel = (key: string, value: any): string => {
  // if the value has its own .label, translate _that_; otherwise translate the raw key
  const raw = typeof value === 'object' && value?.label
    ? value.label
    : key;

  return translatePropToLabel(raw);
};


/**
 * Gets the actual value from a field that might be a nested object
 * @param value The field value (which might be an object with a value property)
 * @returns The actual value to display
 */
export const getFieldValue = (value: any): any => {
  return typeof value === 'object' && value?.value !== undefined
    ? value.value
    : value;
};

/**
 * Checks if a value is a valid array of ValueWithSource objects
 * @param value The value to check
 * @returns true if the value is a valid array of ValueWithSource objects, false otherwise
 */
export const isValidArray = (value: any): boolean => {
  try {
    if (!Array.isArray(value) || value.length === 0) {
      console.warn('Invalid or empty array received');
      return false;
    }
    const firstItem = value[0];

    const isValid = typeof firstItem === 'object' &&
      firstItem !== null &&
      'value' in firstItem &&
      'source' in firstItem &&
      typeof firstItem.value === 'object';

    if (!isValid) {
      console.warn('Array item has invalid format:', firstItem);
    }

    return isValid;
  } catch (error) {
    console.warn('Error validating array:', error);
    return false;
  }
};

/**
 * Generic function to render arrays of ValueWithSource objects with complex value objects
 * @param key The key of the array in the data object
 * @param data The data object containing the array
 * @returns React element or null if no data
 */
export const renderValidArray = <T extends Record<string, any>>(key: string, data: T, noRenderList: string[] = [], hasLabel: boolean = true, hasTitle: boolean = true, hasGenericLabel: boolean = true): React.ReactElement | null => {
  const arrayData = data?.[key as keyof T] as Array<any> | undefined;

  if (!arrayData?.length) return null;
  if (!isValidArray(arrayData)) return null;

  const hasOnlyOneItem = arrayData.length === 1;
  const lastItem = arrayData[arrayData.length - 1];

  //@ts-ignore
  let pluralLabel = genericConstants.getPluralWord[key] || `${key}`;

  return (
    <CustomGridContainer cols={1} className="mb-5">
      {
        hasTitle && (
          <GridItem fullWidth className="pb-2">
            <CustomLabel
              label={(translatePropToLabel(pluralLabel)).toUpperCase()}
              colorClass="bg-white"
            />
          </GridItem>
        )
      }

      <GridItem fullWidth>
        <CustomGridContainer cols={2}>
          {arrayData.map((genericArray, index) => (
            <GridItem key={`${key}-column-${index}`} cols={1}>
              <div className="">
                {hasGenericLabel && (
                  <div className="group">
                    <CustomReadOnlyInputField
                      label={`${translatePropToLabel(getSingular(genericArray.label) || key).toUpperCase()} ${index + 1}`}
                      colorClass="bg-primary"
                      icon={hasTitle ? <MdOutlineSubdirectoryArrowRight size={16} /> : null}
                      value={""}
                      tooltip={renderSourceTooltip(genericArray.source)}
                    />
                  </div>
                )}
                <div className={`${hasGenericLabel && "pl-5"} ${hasLabel && 'pt-4'}`}>
                  {Object.entries(genericArray.value).filter(([fieldKey]) => !noRenderList?.includes(fieldKey)).map(([fieldKey, fieldValue]) => (
                    <GridItem
                      key={`${key}-${index}-${fieldKey}`}
                      cols={1}
                      className="py-2 group"
                    >
                      <CustomReadOnlyInputField
                        label={hasLabel ? `${(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}` : ''}
                        colorClass="bg-border"
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                        value={String(parseValue(getFieldValue(fieldValue)) || "")}
                        tooltip={renderSourceTooltip(getFieldValue(fieldValue).source)}
                      />
                    </GridItem>
                  ))}
                </div>
              </div>
            </GridItem>
          ))}
        </CustomGridContainer>
      </GridItem>
    </CustomGridContainer>
  );
};

/**
 * Generic function to render arrays of ValueWithSource objects with simple value objects (single property)
 * @param key The key of the array in the data object
 * @param data The data object containing the array
 * @param valueKey The key of the single property in the value object (e.g., "phone number", "email address")
 * @returns React element or null if no data
 */
export const renderSimpleArray = <T extends Record<string, any>>(
  key: string,
  data: T,
  valueKey: string
): React.ReactElement | null => {
  const arrayData = data?.[key as keyof T] as Array<any> | undefined;

  if (!arrayData?.length) return null;
  if (!isValidArray(arrayData)) return null;

  //@ts-ignore
  let pluralLabel = genericConstants.getPluralWord[key] || `${key}`;

  return (
    <CustomGridContainer cols={1} className="">
      <GridItem fullWidth className="">
        <CustomGridContainer cols={1}>
          <div className="mb-4">
            <CustomLabel
              label={pluralLabel.toUpperCase()}
              colorClass="bg-white"
            />
            <CustomGridContainer cols={2} className="pt-2">
              {arrayData.map((item, index) => (
                <GridItem key={`${key}-item-${index}`} cols={1}>
                  <div className="">
                    <GridItem
                      key={`${key}-${index}-${valueKey}`}
                      cols={1}
                      className="py-2"
                    >
                      <ReadOnlyInputField
                        label={`${(translatePropToLabel(valueKey)).toUpperCase()} ${index + 1}`}
                        value={String(getFieldValue(item.value[valueKey]) || "")}
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </GridItem>
                  </div>
                </GridItem>
              ))}
            </CustomGridContainer>
          </div>
        </CustomGridContainer>
      </GridItem>
    </CustomGridContainer>
  );
};

export const renderValidObject = <T extends Record<string, any>>(
  key: string,
  data: T,
  valueKey: string = 'value'
): React.ReactElement | null => {
  const objectData = data?.[key as keyof T] as Record<string, any> | undefined;

  if (!objectData || !objectData[valueKey]) return null;

  return (
    <CustomGridContainer cols={1} className="pt-2">
      <GridItem fullWidth className="pt-5">
        <CustomGridContainer cols={1}>
          <div className="mb-4">
            <CustomLabel
              label={translatePropToLabel(key).toUpperCase()}
              colorClass="bg-white"
            />
            <CustomGridContainer cols={2} className="pl-5 pt-4">
              {Object.entries(objectData[valueKey]).filter(([fieldKey]) => fieldKey !== "label default key").map(([fieldKey, fieldValue]) => (
                <GridItem
                  key={`${key}-${fieldKey}`}
                  cols={1}
                  className="py-2"
                >
                  <ReadOnlyInputField
                    label={`${(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}`}
                    value={String(getFieldValue(fieldValue) || "")}
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  />
                </GridItem>
              ))}
            </CustomGridContainer>
          </div>
        </CustomGridContainer>
      </GridItem>
    </CustomGridContainer>);
};

export const getIconImage = (name: string) => {
  if (!name) return null;
  /* 
  image will be based on the name received, ex.:

  getcontact --> /assets/logo-getcontact.png
  tc --> /assets/logo-tc.png
  drupe --> /assets/logo-drupe.png
  eyecon --> /assets/logo-eyecon.png
  hiya --> /assets/logo-hiya.png
  callapp --> /assets/logo-callapp.png

  if none match return null
  */
  try {
    return `/assets/logo-${name}.png`;
  } catch (error) {
    return null;
  }
};

export const isValidUrl = (url: string | undefined | null) => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

export const isBase64Image = (str: string) => {
  if (!str) return false;
  try {
    if (str.startsWith('data:image')) {
      return true;
    }
    return btoa(atob(str)) == str;
  } catch (err) {
    return false;
  }
};

interface RenderValidArrayProps<T> {
  key: string;
  data: T;
  name: string;
  noRenderList?: string[];
  hasLabel?: boolean;
  hasTitle?: boolean;
  hasGenericLabel?: boolean;
  shouldIncludeItem?: (item: any) => boolean;
  onToggleField?: (origIndex: number, fieldKey: string) => void;
}

export function NEW_renderValidArray<T extends Record<string, any>>({
  key,
  data,
  name,
  noRenderList = [],
  hasLabel = true,
  hasTitle = true,
  hasGenericLabel = true,
  shouldIncludeItem,
  onToggleField,
}: RenderValidArrayProps<T>): React.ReactElement | null {
  const arrayData = data[key as keyof T] as any[] | undefined;
  if (!arrayData || !arrayData.length) return null;

  // 1) zip each item with its original index
  const indexed = arrayData.map((item, idx) => ({ item, origIndex: idx }));

  // 2) apply the deletion filter
  const shown = shouldIncludeItem
    ? indexed.filter(({ item }) => shouldIncludeItem(item))
    : indexed;

  if (!shown.length) return null;

  // 3) title
  return (
    <CustomGridContainer cols={1} className="mb-5">
      {hasTitle && (
        <GridItem fullWidth className="pb-2">
          <CustomLabel
            label={translatePropToLabel(name).toUpperCase()}
            colorClass="bg-white"
          />
        </GridItem>
      )}

      <GridItem fullWidth>
        <CustomGridContainer cols={2}>
          {shown.map(({ item, origIndex }, renderIdx) => (
            <GridItem key={`${key}-${origIndex}`} cols={1}>
              {/* generic label per bloco */}
              {hasGenericLabel && (
                <CustomLabel
                  label={`${translatePropToLabel(
                    getSingular(item.label) || name
                  ).toUpperCase()} ${renderIdx + 1}`}
                  colorClass="bg-primary"
                  icon={
                    hasTitle ? (
                      <MdOutlineSubdirectoryArrowRight size={16} />
                    ) : null
                  }
                />
              )}

              <div className={`${hasGenericLabel ? "pl-5" : ""}`}>
                {Object.entries(item.value)
                  .filter(([fieldKey]) => !noRenderList.includes(fieldKey))
                  .map(([fieldKey, fieldValue]) => (
                    <CustomGridItem
                      key={`${key}-${origIndex}-${fieldKey}`}
                      cols={1}
                      className="py-2"
                      onToggleField={() =>
                        onToggleField?.(origIndex, fieldKey)
                      }
                    >
                      <div className="py-2">
                        {hasLabel && (
                          <CustomLabel
                            label={translatePropToLabel(
                              getFieldLabel(fieldKey, fieldValue)
                            ).toUpperCase()}
                            colorClass="bg-border"
                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                          />
                        )}
                        {/* your original Input styling */}
                        <Input
                          onFocus={(e) => e.target.select()}
                          value={String(getFieldValue(fieldValue) || "")}
                          readOnly
                          className="border-dashed"
                        />
                      </div>
                    </CustomGridItem>
                  ))}
              </div>
            </GridItem>
          ))}
        </CustomGridContainer>
      </GridItem>
    </CustomGridContainer>
  );
}

export const renderSourceTooltip = (source: string[]) => {
  if (!source?.length) return undefined;

  const tooltipContent = {
    title: "Origem do dado",
    icon: <Info size={16} className="text-accent" />,
    children: <div>
      <Text variant="label-sm" className="opacity-80">
        ORIGEM DO DADO
      </Text>
      <Separator className="my-2 border-border" />
      {source?.map((src, i) => (
        <Text key={i} className="text-accent">
          {translateSource(src)}
        </Text>
      ))}
    </div>
  }

  return tooltipContent
};
