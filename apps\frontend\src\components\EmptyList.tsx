import { /* Button */ Text } from "@snap/design-system";
import { PiListMagnifyingGlassBold } from "react-icons/pi";

interface EmptyListProps {
  onReload?: () => void;
}

const EmptyList = ({ onReload }: EmptyListProps) => {
  return (
    <div className="flex flex-col gap-4 items-center text-gray-300">
      <Text variant="label-lg" align="center">Nenhum relatório encontrado.</Text>
      <PiListMagnifyingGlassBold size={64} />
      {/* <Button variant="destructive" onClick={onReload} className="uppercase">
        Regarregar
      </Button> */}
    </div>
  );
};

export default EmptyList;
