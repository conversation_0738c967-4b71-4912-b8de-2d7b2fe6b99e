import { useState } from "react";
import { Outlet } from "react-router";
import BreadcrumbNavigation from "~/components/Breadcrumbs";
import Header from "~/components/Header";
import Toolbar from "~/components/Toolbar";
import { BreadcrumbItem } from "~/types/global";

const DefaultLayout = () => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  return (
    <>
      <Header />
      <div className="px-8">
        <BreadcrumbNavigation breadcrumbs={breadcrumbs} />
      </div>
      <main className="flex flex-col min-h-screen">
        <div className="w-full px-8 py-4">
          <Toolbar />
        </div>
        <div className="flex-1 w-full flex flex-col min-h-0">
          <Outlet context={{ setBreadcrumbs }} />
        </div>
      </main>
    </>
  );
};

export default DefaultLayout;
