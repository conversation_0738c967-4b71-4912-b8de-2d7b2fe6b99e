import { ReportData, EncryptedData } from "~/types/global";
import { REPORT_CONSTANTS } from "~/helpers/constants";

export class ListReportOutputDTO {
  private readonly reports: ReportData[];

  constructor(data: unknown) {
    this.reports = this.validateAndTransform(data);
  }

  private validateAndTransform(data: unknown): ReportData[] {
    if (!Array.isArray(data)) {
      console.error("Invalid data format for report list: expected an array");
      throw new Error("Erro ao buscar lista de relatórios.");
    }

    return data.map((item, index) => {
      if (!this.isValidReportData(item)) {
        console.error(
          `Invalid report data at index ${index} on the report list`
        );
        throw new Error("Erro ao buscar lista de relatórios.");
      }
      return item;
    });
  }

  private isValidReportData(item: any): item is ReportData {
    const requiredProps = [
      REPORT_CONSTANTS.new_report.report_id,
      REPORT_CONSTANTS.new_report.report_status,
      REPORT_CONSTANTS.new_report.report_type,
      REPORT_CONSTANTS.new_report.report_search_args,
    ];

    // Check if all required properties exist
    const hasAllRequiredProps = requiredProps.every((prop) => prop in item);
    if (!hasAllRequiredProps) return false;

    // Validate report_id is string
    if (typeof item[REPORT_CONSTANTS.new_report.report_id] !== "string") {
      return false;
    }

    // Validate report_search_args is EncryptedData
    if (
      !this.isValidEncryptedData(
        item[REPORT_CONSTANTS.new_report.report_search_args]
      )
    ) {
      return false;
    }

    // Validate report_type (can be string or EncryptedData)
    const reportType = item[REPORT_CONSTANTS.new_report.report_type];
    if (
      typeof reportType !== "string" &&
      !this.isValidEncryptedData(reportType)
    ) {
      return false;
    }

    return true;
  }

  private isValidEncryptedData(data: any): data is EncryptedData {
    return (
      data && typeof data.encrypted === "string" && typeof data.iv === "string"
    );
  }

  public getReports(): ReadonlyArray<ReportData> {
    return this.reports;
  }

  public count(): number {
    return this.reports.length;
  }
}
