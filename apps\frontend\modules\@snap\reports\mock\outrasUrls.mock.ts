import { Url } from "../model/Urls";

export const urlsMock: Url = {
    detalhes: [
        {
            "value": {
                "url": {
                    "value": "http://www.mp.rj.gov.br/portal/page/portal/Internet/Imprensa/Em_Destaque/Noticia?caid=293&iditem=8308973",
                    "label": "URL",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                }
            },
            "label": "URL",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        {
            "value": {
                "url": {
                    "value": "http://www.facebook.com/people/_/100001296267978",
                    "label": "URL",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
            },
            "label": "URL",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        {
            "value": {
                "url": {
                    "value": "https://www.linkedin.com/in/jo%C3%A3o-aversa-29234320",
                    "label": "URL",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                }
            },
            "label": "URL",
            "source": ["IRBISLuna"],
            "is_deleted": false
        }



    ]
};

