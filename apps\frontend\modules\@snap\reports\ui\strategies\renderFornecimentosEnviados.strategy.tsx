import React from "react";
import { FornecimentoEnviado } from "../../model/FornecimentosEnviados";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { parseValue } from "../../helpers";

export class RenderFornecimentosEnviados implements RenderStrategy<FornecimentoEnviado> {

    validateKeys = (keys: Array<keyof FornecimentoEnviado>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the fornecimentos information for a FornecimentoEnviado
     * @param fornecimentoEnviado The FornecimentoEnviado object
     * @returns React element or null if no data
     */
    renderFornecimentos = (fornecimentoEnviado?: FornecimentoEnviado): React.ReactElement | null => {
        return renderValidArray('vinculos', fornecimentoEnviado || {});
    };

    formatByKey: Record<
        string,
        (fornecimentoEnviado?: FornecimentoEnviado) => React.ReactElement | null
    > = {
            razao_social: (fornecimentoEnviado?: FornecimentoEnviado) => {
                if (!fornecimentoEnviado?.razao_social) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(fornecimentoEnviado.razao_social.label || "Razão Social").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(fornecimentoEnviado.razao_social.value)}
                                tooltip={renderSourceTooltip(fornecimentoEnviado.razao_social.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            nome_completo: (fornecimentoEnviado?: FornecimentoEnviado) => {
                if (!fornecimentoEnviado?.nome_completo) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(fornecimentoEnviado.nome_completo.label || "Nome Completo").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(fornecimentoEnviado.nome_completo.value)}
                                tooltip={renderSourceTooltip(fornecimentoEnviado.nome_completo.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (fornecimentoEnviado?: FornecimentoEnviado) => {
                if (!fornecimentoEnviado?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(fornecimentoEnviado.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={parseValue(String(value.value))}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            vinculos: (fornecimentoEnviado?: FornecimentoEnviado) => {
                return this.renderFornecimentos(fornecimentoEnviado);
            }
        };

    render = (fornecimentoEnviado: FornecimentoEnviado): React.ReactElement[] => {
        const keys = Object.keys(fornecimentoEnviado) as Array<keyof FornecimentoEnviado>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof FornecimentoEnviado> = [
            "nome_completo",
            'razao_social',
            'detalhes',
            'vinculos'
        ];

        // Filter the keys to only include those that exist in the fornecimentoEnviado object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(fornecimentoEnviado);
                }

                // Check if the value is an array with the expected structure
                const value = fornecimentoEnviado[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, fornecimentoEnviado);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
