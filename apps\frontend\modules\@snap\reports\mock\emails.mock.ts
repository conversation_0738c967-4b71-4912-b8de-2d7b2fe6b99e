import { Email } from "../model/Emails";

// Pensando que emails podem trazer outras propriedades além do email address futuramente.
export const emailsMock: Email = {
    detalhes: [
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "email address": {
                    value: "<EMAIL>",
                    label: "Email",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Email",
            source: ["SNAP"],
            is_deleted: false
        },
    ]
};