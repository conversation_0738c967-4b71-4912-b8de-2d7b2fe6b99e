import { ValueWithSource } from "./ValueWithSource";
import { _Empresa } from "./DadosEmpresa";
import { _Endereco } from "./Enderecos";
import { _Telefone } from "./Telefones";
import { _Email } from "./Emails";

export interface Sociedade {
    razao_social: ValueWithSource;
    detalhes: Partial<Record<keyof Omit<_Empresa, "razao social">, ValueWithSource<string>>>;
    enderecos?: Array<ValueWithSource<_Endereco>>;
    telefones?: Array<ValueWithSource<_Telefone>>;
    emails?: Array<ValueWithSource<_Email>>;
}