import reportTypes from "~constants/report_types.json";
import reportLabels from "~constants/labels.json";
import reportSections from "~constants/ui_group_titles.json";
import translatedLabels from "~constants/translate_prop_to_label.json"
import parsedValues from "~constants/parsed_values.json";
import user from "~constants/user.json";

export const REPORT_CONSTANTS = reportTypes;
export const REPORT_LABELS = reportLabels;
export const REPORT_SECTIONS = reportSections;
export const USER_CONSTANTS = user;
export const TRANSLATED_LABELS = translatedLabels;
export const PARSED_VALUES = parsedValues;
export const REPORT_TYPES = Object.entries(reportTypes.types).filter(([key]) => key !== "relacoes");
export const NON_ENCRYPTED_KEYS = [
  REPORT_CONSTANTS.new_report.report_type,
  //REPORT_CONSTANTS.new_report.report_status,
  REPORT_CONSTANTS.new_report.subject_age,
  REPORT_CONSTANTS.new_report.creation_at,
  REPORT_CONSTANTS.new_report.modified_at,
];

export const verifierPhrase = "youshallnotpass";

export const tanstackQueryConfig = {
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
};

export const relativesList = [
  "mae",
  "irma",
  "pai",
  "tio",
  "avos",
  "filho",
  "conjuge",
];