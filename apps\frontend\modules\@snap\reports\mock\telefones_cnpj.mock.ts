import { Telefone, _Telefone } from "../model/Telefones";

export const telefonesMockCNPJ: Telefone = {
    detalhes: [
        {
            "value": {
                "phone number": {
                    value: "553125860228",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            "label": "Telefone",
            "source": ["SNAP"],
            "is_deleted": false
        },
        {
            "value": {
                "phone number": {
                    value: "5531983462818",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            "label": "Telefone",
            "source": ["SNAP"],
            "is_deleted": false
        },
        {
            "value": {
                "phone number": {
                    value: "5531988684136",
                    label: "Número do Telefone",
                    source: ["SintegraMACNPJ"],
                    is_deleted: false
                },
            },
            "label": "Telefone",
            "source": ["SintegraMACNPJ"],
            "is_deleted": false
        }
    ]
};