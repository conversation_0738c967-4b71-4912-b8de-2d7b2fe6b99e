# REGRAS GERAIS PROCESSAMENTO RELATÓRIOS

## Formato padrão do retorno de cada seção após o processamento (com tipos):

```json
[
    {
        "title": "string", // título da seção
        "subtitle": "string", // subtitulo da seção
        "source": ["string"], // lista de strings com as chaves utilizadas para obter os dados desta seção
        "data_count": "number", // quantidade de dados inseridos nesta seção - contagem de objetos dentro do "data":
        "data": ["string"] // lista de objetos
    }
     //outras seções
]
```

## Formatos de propriedades nos objetos

Os objetos dentro da lista **"data"** seguem alguns padrões de formatação para as propriedades. As chaves de primeiro nível de cada objeto de sessão podem variar, porém o formato de cada propriedade seguem um padrão.

Abaixo estão os principais formatos encontrados:

### 1. Formato básico de propriedade

```json
"nome_da_propriedade": {
    "value": "valor da propriedade",
    "label": "Rótulo para exibição",
    "source": ["Fonte da informação"],
    "is_deleted": false // propriedade que indica se o dado foi deletado
}

// Exemplo de uso nos itens da chave "detalhes" da seção "Dados Pessoais":
"full name": {
    "value": "João da Silva",
    "label": "Nome Completo",
    "source": ["SNAP"],
    "is_deleted": false
}
```

### 2. Object com valor também sendo um objeto

```json
{
    "value": {
        "chave1": {
            "value": "valor1",
            "label": "Rótulo da chave1",
            "source": ["Fonte da informação"],
            "is_deleted": false
        },
        "chave2": {
            "value": "valor2",
            "label": "Rótulo da chave2",
            "source": ["Fonte da informação"],
            "is_deleted": false
        },
    },
    "label": "Rótulo para exibição",
    "source": ["Fonte da informação"],
    "is_deleted": false
}

// Exemplo de uso na chave "advogado" da seção "Processos":
{

    "value": {
        "cpf": {
            "value": "123.456.789-00",
            "label": "CPF",
            "source": ["Escavador"],
            "is_deleted": false,
        },
        "full name": {
            "value": "Carlos Alberto Souza",
            "label": "Nome",
            "source": ["Escavador"],
            "is_deleted": false,
        },
        "oab": {
            "value": "OAB/SP 12345",
            "label": "OAB",
            "source": ["Escavador"],
            "is_deleted": false,
        },
    },
    "label": "Advogado",
    "source": ["Escavador"],
    "is_deleted": false,
}
```

- O formato dos objetos dentro da lista **"data"** varia de acordo com a seção. Necessário seguir a documentação de cada seção, para cada tipo de relatório.
- A chave **"detalhes"** pode estar no formato de objeto ou de lista. Essa regra também vale para todas as chaves de primeiro nível do **"data"** de cada sessão.

- Estes são os arquivos com as regras de processamento de cada seção com exemplos:
  - [Regras para o tipo de relatório "CPF"](REGRAS_SNAP_REPORTS_CPF.md)
  - [Regras para o tipo de relatório "CNPJ"](REGRAS_SNAP_REPORTS_CNPJ.md)
  - [Regras para o tipo de relatório "Email"](REGRAS_SNAP_REPORTS_EMAIL.md)
  - [Regras para o tipo de relatório "Telefone"](REGRAS_SNAP_REPORTS_TELEFONE.md)

## Seções e Chaves para o Tipo de Relatório "CPF"

Abaixo estão listadas todas as seções disponíveis para o tipo de relatório "CPF" e as chaves que são renderizadas dentro de cada objeto da lista de dados, junto com os links para os arquivos de modelo e mock:

### 1. Dados Pessoais
- **Chaves**: `detalhes` (contendo propriedades como `full name`, `nome_da_mae`, `idade`, `sexo`, `pais do passaporte`, `data nascimento`, `cpf`, etc.)
- **Modelo**: [apps/frontend/domain/models/DadosPessoais.ts](apps/frontend/domain/models/DadosPessoais.ts)
- **Mock**: [apps/frontend/domain/mocks/dados_pessoais.mock.ts](apps/frontend/domain/mocks/dados_pessoais.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderDadosPessoais.strategy.tsx](apps/frontend/domain/models/strategy/renderDadosPessoais.strategy.tsx)

### 2. Mandados de Prisão
- **Chaves**: `numero`, `detalhes` (contendo propriedades como `tipo de mandado`, `situacao`, `numero do processo`, etc.), `pena`
- **Modelo**: [apps/frontend/domain/models/Mandados.ts](apps/frontend/domain/models/Mandados.ts)
- **Mock**: [apps/frontend/domain/mocks/mandados.mock.ts](apps/frontend/domain/mocks/mandados.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderMandados.strategy.tsx](apps/frontend/domain/models/strategy/renderMandados.strategy.tsx)

### 3. Telefones
- **Chaves**: `detalhes` (lista de objetos com `phone number`)
- **Modelo**: [apps/frontend/domain/models/Telefones.ts](apps/frontend/domain/models/Telefones.ts)
- **Mock**: [apps/frontend/domain/mocks/telefones.mock.ts](apps/frontend/domain/mocks/telefones.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderTelefones.strategy.tsx](apps/frontend/domain/models/strategy/renderTelefones.strategy.tsx)

### 4. Emails
- **Chaves**: `detalhes` (lista de objetos com `email`)
- **Modelo**: [apps/frontend/domain/models/Emails.ts](apps/frontend/domain/models/Emails.ts)
- **Mock**: [apps/frontend/domain/mocks/emails.mock.ts](apps/frontend/domain/mocks/emails.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderEmails.strategy.tsx](apps/frontend/domain/models/strategy/renderEmails.strategy.tsx)

### 5. Endereços
- **Chaves**: `detalhes` (lista de objetos com informações de endereço)
- **Modelo**: [apps/frontend/domain/models/Enderecos.ts](apps/frontend/domain/models/Enderecos.ts)
- **Mock**: [apps/frontend/domain/mocks/enderecos.mock.ts](apps/frontend/domain/mocks/enderecos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderEnderecos.strategy.tsx](apps/frontend/domain/models/strategy/renderEnderecos.strategy.tsx)

### 6. Parentes
- **Chaves**: `nome`, `detalhes` (contendo propriedades como `grau de parentesco`, `cpf`, etc.)
- **Modelo**: [apps/frontend/domain/models/Parentes.ts](apps/frontend/domain/models/Parentes.ts)
- **Mock**: [apps/frontend/domain/mocks/parentes.mock.ts](apps/frontend/domain/mocks/parentes.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderParentes.strategy.tsx](apps/frontend/domain/models/strategy/renderParentes.strategy.tsx)

### 7. Sociedades
- **Chaves**: `razao_social`, `detalhes` (contendo propriedades como `cnpj`, `situacao`, etc.)
- **Modelo**: [apps/frontend/domain/models/Sociedades.ts](apps/frontend/domain/models/Sociedades.ts)
- **Mock**: [apps/frontend/domain/mocks/sociedades.mock.ts](apps/frontend/domain/mocks/sociedades.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderSociedades.strategy.tsx](apps/frontend/domain/models/strategy/renderSociedades.strategy.tsx)

### 8. Vínculos Empregatícios
- **Chaves**: `empresa`, `detalhes` (contendo propriedades como `cnpj`, `data de admissao`, etc.)
- **Modelo**: [apps/frontend/domain/models/VinculosEmpregaticios.ts](apps/frontend/domain/models/VinculosEmpregaticios.ts)
- **Mock**: [apps/frontend/domain/mocks/vinculos_empregaticios.mock.ts](apps/frontend/domain/mocks/vinculos_empregaticios.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderVinculosEmpregaticios.strategy.tsx](apps/frontend/domain/models/strategy/renderVinculosEmpregaticios.strategy.tsx)

### 9. Processos
- **Chaves**: `numero`, `detalhes` (contendo propriedades como `classe`, `assunto`, etc.), `movimentacoes` (lista de movimentações do processo), `participantes` (contendo listas de diferentes tipos de participantes como `advogados`, `autores`, `reus`, `testemunhas`, `terceiros_interessados`, `representantes`, etc.)
- **Modelo**: [apps/frontend/domain/models/Processo.ts](apps/frontend/domain/models/Processo.ts)
- **Mock**: [apps/frontend/domain/mocks/processos.mock.ts](apps/frontend/domain/mocks/processos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderProcessos.strategy.tsx](apps/frontend/domain/models/strategy/renderProcessos.strategy.tsx)

### 10. Recursos Públicos Recebidos
- **Chaves**: `programa`, `detalhes` (contendo propriedades como `valor`, `data`, etc.)
- **Modelo**: [apps/frontend/domain/models/RecursosPublicos.ts](apps/frontend/domain/models/RecursosPublicos.ts)
- **Mock**: [apps/frontend/domain/mocks/recursos_publicos.mock.ts](apps/frontend/domain/mocks/recursos_publicos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderRecursosPublicos.strategy.tsx](apps/frontend/domain/models/strategy/renderRecursosPublicos.strategy.tsx)

### 11. Serviço Público
- **Chaves**: `servidor`, `detalhes` (contendo propriedades como `cargo`, `orgao`, etc.)
- **Modelo**: [apps/frontend/domain/models/ServicoPublico.ts](apps/frontend/domain/models/ServicoPublico.ts)
- **Mock**: [apps/frontend/domain/mocks/servicos_publicos.mock.ts](apps/frontend/domain/mocks/servicos_publicos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderServicosPublicos.strategy.tsx](apps/frontend/domain/models/strategy/renderServicosPublicos.strategy.tsx)

### 12. Diários Oficiais - NOME
- **Chaves**: `data`, `detalhes` (contendo propriedades como `diario`, `pagina`, etc.), `ocorrencia`
- **Modelo**: [apps/frontend/domain/models/DiariosOficiaisNOME.ts](apps/frontend/domain/models/DiariosOficiaisNOME.ts)
- **Mock**: [apps/frontend/domain/mocks/diarios_oficiais_nome.mock.ts](apps/frontend/domain/mocks/diarios_oficiais_nome.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderDiariosOficiaisNOME.strategy.tsx](apps/frontend/domain/models/strategy/renderDiariosOficiaisNOME.strategy.tsx)

### 13. Diários Oficiais - CPF
- **Chaves**: `data`, `detalhes` (contendo propriedades como `diario`, `pagina`, etc.), `ocorrencia`
- **Modelo**: [apps/frontend/domain/models/DiariosOficiaisCPF.ts](apps/frontend/domain/models/DiariosOficiaisCPF.ts)
- **Mock**: [apps/frontend/domain/mocks/diarios_oficiais_cpf.mock.ts](apps/frontend/domain/mocks/diarios_oficiais_cpf.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderDiariosOficiaisCPF.strategy.tsx](apps/frontend/domain/models/strategy/renderDiariosOficiaisCPF.strategy.tsx)

### 14. Filiação Partidária
- **Chaves**: `partido`, `detalhes` (contendo propriedades como `data de filiacao`, `situacao`, etc.)
- **Modelo**: [apps/frontend/domain/models/FiliacaoPartidaria.ts](apps/frontend/domain/models/FiliacaoPartidaria.ts)
- **Mock**: [apps/frontend/domain/mocks/filiacao_partidaria.mock.ts](apps/frontend/domain/mocks/filiacao_partidaria.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderFiliacaoPartidaria.strategy.tsx](apps/frontend/domain/models/strategy/renderFiliacaoPartidaria.strategy.tsx)

### 15. Possíveis Contatos
- **Chaves**: `nome`, `detalhes` (contendo propriedades como `cpf`, `data nascimento`, etc.), `telefones`, `emails`
- **Modelo**: [apps/frontend/domain/models/PossiveisContatos.ts](apps/frontend/domain/models/PossiveisContatos.ts)
- **Mock**: [apps/frontend/domain/mocks/possiveis_contatos.mock.ts](apps/frontend/domain/mocks/possiveis_contatos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderPossiveisContatos.strategy.tsx](apps/frontend/domain/models/strategy/renderPossiveisContatos.strategy.tsx)

### 16. Possíveis Contas em Sites
- **Chaves**: `detalhes` (lista de objetos contendo `site` e informações sobre a conta)
- **Modelo**: [apps/frontend/domain/models/PossiveisContas.ts](apps/frontend/domain/models/PossiveisContas.ts)
- **Mock**: [apps/frontend/domain/mocks/possiveis_contas.mock.ts](apps/frontend/domain/mocks/possiveis_contas.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderPossiveisContas.strategy.tsx](apps/frontend/domain/models/strategy/renderPossiveisContas.strategy.tsx)

### 17. Doações Enviadas Campanha
- **Chaves**: `candidato`, `detalhes` (contendo propriedades como `cpf`, `ano`, `cargo eleitoral`, etc.), `doacoes` (lista de objetos com informações sobre doações)
- **Modelo**: [apps/frontend/domain/models/DoacoesEnviadas.ts](apps/frontend/domain/models/DoacoesEnviadas.ts)
- **Mock**: [apps/frontend/domain/mocks/doacoes_enviadas.mock.ts](apps/frontend/domain/mocks/doacoes_enviadas.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderDoacoesEnviadas.strategy.tsx](apps/frontend/domain/models/strategy/renderDoacoesEnviadas.strategy.tsx)

### 18. Doações Recebidas Campanha
- **Chaves**: `razao_social` ou `nome_completo`, `detalhes` (contendo propriedades como `cnpj`, `cpf`, etc.), `doacoes` (lista de objetos com informações sobre doações)
- **Modelo**: [apps/frontend/domain/models/DoacoesRecebidas.ts](apps/frontend/domain/models/DoacoesRecebidas.ts)
- **Mock**: [apps/frontend/domain/mocks/doacoes_recebidas.mock.ts](apps/frontend/domain/mocks/doacoes_recebidas.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderDoacoesRecebidas.strategy.tsx](apps/frontend/domain/models/strategy/renderDoacoesRecebidas.strategy.tsx)

### 19. Fornecimentos Enviados Campanha
- **Chaves**: `razao_social`, `detalhes` (contendo propriedades como `cnpj`, `cnae`, etc.), `fornecimentos` (lista de objetos com informações sobre fornecimentos)
- **Modelo**: [apps/frontend/domain/models/FornecimentosEnviados.ts](apps/frontend/domain/models/FornecimentosEnviados.ts)
- **Mock**: [apps/frontend/domain/mocks/fornecimentos_enviados.mock.ts](apps/frontend/domain/mocks/fornecimentos_enviados.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderFornecimentosEnviados.strategy.tsx](apps/frontend/domain/models/strategy/renderFornecimentosEnviados.strategy.tsx)

### 20. Fornecimentos Recebidos Campanha
- **Chaves**: `candidato`, `detalhes` (contendo propriedades como `cpf`, `ano da eleição`, `cargo eleitoral`, etc.), `fornecimentos` (lista de objetos com informações sobre fornecimentos)
- **Modelo**: [apps/frontend/domain/models/FornecimentosRecebidos.ts](apps/frontend/domain/models/FornecimentosRecebidos.ts)
- **Mock**: [apps/frontend/domain/mocks/fornecimentos_recebidos.mock.ts](apps/frontend/domain/mocks/fornecimentos_recebidos.mock.ts)
- **Estratégia de Renderização**: [apps/frontend/domain/models/strategy/renderFornecimentosRecebidos.strategy.tsx](apps/frontend/domain/models/strategy/renderFornecimentosRecebidos.strategy.tsx)

## Análise Comparativa das Seções por Tipo de Relatório

### Seções Compartilhadas Entre Todos os Tipos de Relatório:

1. **Telefones**
   - Presente em: CNPJ, CPF, TELEFONE
   - Fontes variam por tipo de relatório:
     - CNPJ: "SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"
     - CPF: "SNAP", "SintegraMA"
     - TELEFONE: "IRBISLuna"

2. **Emails**
   - Presente em: CNPJ, CPF, TELEFONE
   - Fontes variam por tipo de relatório:
     - CNPJ: "SNAP", "CadastroNacionalPJ"
     - CPF: "SNAP"
     - TELEFONE: "IRBIS", "IRBISLuna"

3. **Endereços**
   - Presente em: CNPJ, CPF, TELEFONE
   - Fontes variam por tipo de relatório:
     - CNPJ: Múltiplas fontes incluindo "SNAP", "CadastroNacionalPJ" e várias fontes Sintegra
     - CPF: "SNAP", "SintegraMTCPF", "SintegraPBCPF", "SintegraSECPF", "SintegraMA", "SintegraPRCPF"
     - TELEFONE: "SNAP", "IRBISLuna"

4. **Parentes**
   - Presente em: CPF, TELEFONE
   - Fontes:
     - CPF: "SNAP", "BancoNacionalDeMonitoramentoDePrisoes"
     - TELEFONE: "SNAP"

5. **Processos**
   - Presente em: CNPJ, CPF
   - Fontes: "Escavador"

### Seções Exclusivas do Relatório CNPJ:

1. **Dados Empresa**
   - Fontes: "SNAP", "CadastroNacionalPJ"

2. **Sócios**
   - Fontes: "SNAP", "CadastroNacionalPJ"

3. **Juntas Comerciais**
   - Fontes: "JUCESP"

4. **Diários Oficiais - CNPJ**
   - Fontes: "EscavadorDOCNPJ"

5. **Diários Oficiais - NOME**
   - Fontes: "EscavadorDONome"

6. **Recursos Públicos Recebidos**
   - Fontes: Múltiplos portais de transparência

7. **Doações Enviadas Campanha**
   - Fontes: "TSEDoacoes"

8. **Doações Recebidas Campanha**
   - Fontes: "TSEDoacoes"

9. **Fornecimentos Enviados Campanha**
   - Fontes: "TSEFornecimento"

10. **Fornecimentos Recebidos Campanha**
    - Fontes: "TSEFornecimento"

### Seções Exclusivas do Relatório CPF:

1. **Dados Pessoais**
   - Fontes: "SNAP"

2. **Mandados de Prisão**
   - Fontes: "BancoNacionalDeMonitoramentoDePrisoes"

3. **Sociedades**
   - Fontes: "SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"

4. **Vínculos Empregatícios**
   - Fontes: "SNAP"

5. **Recursos Públicos Recebidos**
   - Fontes: Múltiplos portais de transparência

6. **Serviço Público**
   - Fontes: "TransparenciaPRNome"

7. **Diários Oficiais - CPF**
   - Fontes: "EscavadorDOCPF", "QueridoDiarioCPF"

8. **Diários Oficiais - NOME**
   - Fontes: "EscavadorDONome", "QueridoDiarioNome"

9. **Filiação Partidária**
   - Fontes: "TSEFiliacaoPartidaria"

10. **Possíveis Contatos**
    - Fontes: "SNAP"

11. **Possíveis Contas em Sites**
    - Fontes: "PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"

12. **Doações Enviadas Campanha**
    - Fontes: "TSEDoacoes"

13. **Doações Recebidas Campanha**
    - Fontes: "TSEDoacoes"

14. **Fornecimentos Enviados Campanha**
    - Fontes: "TSEFornecimento"

15. **Fornecimentos Recebidos Campanha**
    - Fontes: "TSEFornecimento"

### Seções Exclusivas do Relatório TELEFONE:

1. **Contatos Salvos**
   - Fontes: "IRBIS"

2. **Dados Pessoais**
   - Fontes: "SNAP"

3. **Imagens**
   - Fontes: "IRBISLuna"

4. **Empresas Relacionadas**
   - Fontes: "IRBISLuna"

5. **Nomes de Usuário**
   - Fontes: "IRBISLuna"

6. **Perfis Redes Sociais**
   - Fontes: "IRBIS", "IRBISLuna"

7. **Outras URLs**
   - Fontes: "IRBISLuna"

8. **Possíveis Contatos**
   - Fontes: "IRBIS", "IRBISLuna"

9. **Possíveis Pessoas Relacionadas**
   - Fontes: "IRBIS", "SNAP"

10. **Possíveis Contas em Sites**
    - Fontes: "ProvedorDeAplicacacaoDaInternettelefone"

### Observações Importantes:

1. **Seções Compartilhadas vs. Exclusivas**:
   - Embora alguns nomes de seção sejam compartilhados (como "Telefones", "Emails", "Endereços"), as fontes de dados e regras específicas de processamento diferem entre os tipos de relatório