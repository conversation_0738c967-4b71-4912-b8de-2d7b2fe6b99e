import React from 'react';
import {
  Document,
  Page,
  View,
  Text,
  StyleSheet,
  Image,
  Link,
} from '@react-pdf/renderer';
import { ReportMetadata } from '../../global';
import { REPORT_CONSTANTS } from '../../config/constants';
import { getInitials } from '../../helpers';

const PrintProfileHeader: React.FC<{ metadata: ReportMetadata; report_type: string }> = ({ metadata, report_type }) => {
  const profileName = metadata[REPORT_CONSTANTS.new_report.subject_name] as string || 'Nome não disponível';
  const initials = getInitials(profileName as string);
  const idade = metadata[REPORT_CONSTANTS.new_report.subject_age] as number | string ?? 'N/A';
  const sexo = metadata[REPORT_CONSTANTS.new_report.subject_sex] as string || 'N/A';
  const nomeMae = metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string || 'N/A';

  return (
    <View style={styles.profileContainer}>
      <Text style={styles.sectionHeader}>PERFIL</Text>

      <View style={styles.photoContainer}>
        <View style={styles.avatarBox}>
          <Text style={styles.avatarFallback}>{initials}</Text>
        </View>
      </View>

      <View style={styles.dataList}>
        <View style={styles.dataRow}>
          <Text style={styles.dataLabel}>NOME</Text>
          <Text style={styles.dataValue}>{profileName}</Text>
        </View>

        <View style={styles.dataRow}>
          <Text style={styles.dataLabel}>NOME DA MÃE</Text>
          <Text style={styles.dataValue}>{nomeMae}</Text>
        </View>

        <View style={styles.dataRow}>
          <Text style={styles.dataLabel}>IDADE</Text>
          <Text style={styles.dataValue}>{idade}</Text>
        </View>

        <View style={styles.dataRow}>
          <Text style={styles.dataLabel}>SEXO</Text>
          <Text style={styles.dataValue}>{sexo}</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  profileContainer: {
    width: "48%",
    paddingRight: 16,
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  photoContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  avatarBox: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#E5E7EB",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  avatarFallback: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#6B7280",
    textAlign: "center",
  },
  subsectionHeader: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  dataList: {
    flexDirection: "column",
  },
  dataRow: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottom: "1px solid #E5E7EB",
  },
  dataLabel: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 4,
    letterSpacing: 0.5,
  },
  dataValue: {
    fontSize: 10,
    fontWeight: "normal",
  },
})

export default PrintProfileHeader;
