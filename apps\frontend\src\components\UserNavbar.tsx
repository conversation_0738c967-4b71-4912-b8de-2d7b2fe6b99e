import UserMenu from "~/components/UserMenu";
import { useAuth } from "~/hooks/useAuth";
import { useNavigate } from "react-router";
import { MenuItem } from "@snap/design-system";
import UserMenuContent from "./UserMenuContent";

export default function UserNavbar() {
  const navigate = useNavigate();
  const {
    logoutMutation: { mutateAsync: logout },
  } = useAuth();

  const handleLogout = () => {
    logout();
  };

  const handleNavigateConfig = () => {
    const path = `/conta/configuracoes`;
    navigate(path);
  };

  const menuActions: MenuItem[] = [
    {
      label: "CONFIGURAÇÕES",
      icon: <div className="h-3 w-3 bg-border" />,
      onClick: handleNavigateConfig,
    },
    {
      label: "SAIR",
      icon: <div className="h-3 w-3 bg-border" />,
      onClick: handleLogout,
    },
  ]

  return <UserMenu userMenuContent={<UserMenuContent menuActions={menuActions} />} />;
}
