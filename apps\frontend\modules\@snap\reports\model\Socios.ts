import { ValueWithSource } from "./ValueWithSource";

export interface _Socio {
    "full name"?: ValueWithSource;
    "cargo em sociedade"?: ValueWithSource;
    "qualificação societária"?: ValueWithSource;
    cpf?: ValueWithSource;
    "faixa etaria"?: ValueWithSource;
    "identificador socio"?: ValueWithSource;
    "pais"?: ValueWithSource;
    "entradasociedade"?: ValueWithSource;
    "data de inicio em sociedade"?: ValueWithSource;
}

export interface Socio {
    detalhes: Array<ValueWithSource<_Socio>>;
}