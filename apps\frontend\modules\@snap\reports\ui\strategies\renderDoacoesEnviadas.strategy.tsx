import React from "react";
import { DoacaoEnviada } from "../../model/DoacoesEnviadas";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel, parseValue} from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderDoacoesEnviadas implements RenderStrategy<DoacaoEnviada> {

    validateKeys = (keys: Array<keyof DoacaoEnviada>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the doacoes information for a DoacaoEnviada
     * @param doacaoEnviada The DoacaoEnviada object
     * @returns React element or null if no data
     */
    renderDoacoes = (doacaoEnviada?: DoacaoEnviada): React.ReactElement | null => {
        return renderValidArray('vinculos', doacaoEnviada || {});
    };

    formatByKey: Record<
        string,
        (doacaoEnviada?: DoacaoEnviada) => React.ReactElement | null
    > = {
            candidato: (doacaoEnviada?: DoacaoEnviada) => {
                if (!doacaoEnviada?.candidato) return null;

                return (
                    <CustomGridContainer cols={3} className="mb-6 group">
                        <CustomReadOnlyInputField
                            label={(doacaoEnviada.candidato.label || "Candidato").toUpperCase()}
                            colorClass="bg-primary"
                            labelTextClass="text-accent"
                            value={parseValue(doacaoEnviada.candidato.value)}
                            tooltip={renderSourceTooltip(doacaoEnviada.candidato.source)}
                        />
                    </CustomGridContainer>
                );
            },

            detalhes: (doacaoEnviada?: DoacaoEnviada) => {
                if (!doacaoEnviada?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(doacaoEnviada.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={parseValue(String(value.value))}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            vinculos: (doacaoEnviada?: DoacaoEnviada) => {
                return this.renderDoacoes(doacaoEnviada);
            }
        };

    render = (doacaoEnviada: DoacaoEnviada): React.ReactElement[] => {
        const keys = Object.keys(doacaoEnviada) as Array<keyof DoacaoEnviada>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof DoacaoEnviada> = [
            'candidato',
            'detalhes',
            'vinculos'
        ];

        // Filter the keys to only include those that exist in the doacaoEnviada object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(doacaoEnviada);
                }

                // Check if the value is an array with the expected structure
                const value = doacaoEnviada[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, doacaoEnviada);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
