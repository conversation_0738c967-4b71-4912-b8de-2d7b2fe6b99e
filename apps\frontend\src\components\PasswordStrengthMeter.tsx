import { useState, useEffect, useMemo } from "react";
import { cn } from "~/lib/utils";
import { Text } from "@snap/design-system";
import { SquareX, SquareCheck } from "lucide-react";

export type PasswordStrengthLevel = 'muitoFraca' | 'fraca' | 'moderada' | 'boa' | 'forte';

const commonPatterns = [
  /^123456/,
  /^senha/i,
  /^admin/i,
  /^qwerty/i,
  /^abc123/i,
  /^password/i,
  /^brasil/i,
  /^mudar123/i,
  /^teste123/i,
  /^futebol/i,
  /^flamengo/i,
  /^corinthians/i,
  /^palmeiras/i,
  /^santos/i,
  /^saopaulo/i,
  /^vasco/i,
  /^gremio/i,
  /^cruzeiro/i,
  /^fluminense/i,
  /^botafogo/i,
  /^internacional/i,
  /^atletico/i,
  /^bahia/i,
  /^sport/i,
  /^vitoria/i,
  /^coritiba/i,
  /^goias/i,
  /^figueirense/i,
  /^ponte/i,
  /^chapecoense/i,
  /^america/i,
  /^juventude/i,
  /^fortaleza/i,
  /^ceara/i,
  /^cuiaba/i,
  /^bragantino/i,
  /^atleticopr/i,
  /^atleticomg/i,
  /^atleticogo/i,
];

const sequences = [
  '12345678',
  'abcdefgh',
  'qwertyui',
  '87654321',
  '01234567',
  'asdfghjk',
  'zxcvbnm',
  '1q2w3e4r',
  'qazwsxedc',
  '1qaz2wsx',
  'qweasdzxc',
  'poiuylkjh',
];

/**
 * Verifica se uma senha foi exposta em vazamentos de dados usando a API "Have I Been Pwned"
 * Utiliza o método k-anonimity para não enviar a senha completa para a API
 */
const checkPasswordBreached = async (password: string): Promise<boolean> => {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-1', data);

    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    const prefix = hashHex.substring(0, 5).toUpperCase();
    const suffix = hashHex.substring(5).toUpperCase();

    const response = await fetch(`https://api.pwnedpasswords.com/range/${prefix}`);
    const text = await response.text();

    return text.includes(suffix);
  } catch (error) {
    console.error('Error checking password breach:', error);
    return false;
  }
};

/**
 * Avalia a força da senha baseado nas diretrizes NIST SP 800-63B
 * Retorna o nível de força, pontuação, feedback e função para verificar vazamentos
 */
export const evaluatePasswordStrength = (password: string): {
  level: PasswordStrengthLevel;
  score: number;
  feedback: string;
  checkBreached?: () => Promise<boolean>;
} => {
  if (!password) {
    return {
      level: 'muitoFraca',
      score: 0,
      feedback: ''
    };
  }

  let score = 0;

  if (password.length < 8) {
    return {
      level: 'muitoFraca',
      score: Math.min(20, password.length * 2),
      feedback: "",//'Senha muito curta, use pelo menos 8 caracteres',
      checkBreached: () => Promise.resolve(false)
    };
  }

  score += 20;

/*   if (commonPatterns.some(pattern => pattern.test(password))) {
    score = Math.max(10, score - 20);
    return {
      level: 'muitoFraca',
      score,
      feedback: "",//'Evite usar padrões comuns de senha',
      checkBreached: () => Promise.resolve(true)
    };
  } */

/*   if (sequences.some(seq => password.toLowerCase().includes(seq.toLowerCase()))) {
    score = Math.max(15, score - 15);
    return {
      level: 'fraca',
      score,
      feedback: "", //'Evite sequências de caracteres'
      checkBreached: () => Promise.resolve(true)
    };
  } */

  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSpecialChars = /[^A-Za-z0-9]/.test(password);

  if (hasLowercase) score += 10;
  if (hasUppercase) score += 15;
  if (hasNumbers) score += 15;
  if (hasSpecialChars) score += 20;

  const repeatedChars = password.match(/(.)\1{2,}/g);
  if (repeatedChars) {
    score -= repeatedChars.length * 5;
  }

  if (password.length >= 12) score += 10;
  if (password.length >= 16) score += 10;

  score = Math.max(0, Math.min(100, score));

  let level: PasswordStrengthLevel;
  let feedback: string;

  if (score < 30) {
    level = 'muitoFraca';
    feedback = "" //'Senha muito fraca, adicione mais caracteres e variação';
  } else if (score < 50) {
    level = 'fraca';
    feedback = "" //'Senha fraca, tente adicionar números e caracteres especiais';
  } else if (score < 70) {
    level = 'moderada';
    feedback = "" //'Senha moderada, tente adicionar mais variação';
  } else if (score < 90) {
    level = 'boa';
    feedback = ""//'Boa senha!';
  } else {
    level = 'forte';
    feedback = ""//'Senha forte!';
  }

  return {
    level,
    score,
    feedback,
    checkBreached: () => checkPasswordBreached(password)
  };
};

interface PasswordStrengthMeterProps {
  password: string;
}

/**
 * Componente que exibe um medidor de força de senha com feedback visual
 */
const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ password }) => {
  const [isBreached, setIsBreached] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const strengthResult = useMemo(() =>
    evaluatePasswordStrength(password),
    [password]
  );

  const { level, score, feedback, checkBreached } = strengthResult;

  const passwordRules = useMemo(() =>
    checkPasswordRules(password),
    [password]
  );

  const passwordWarnings = useMemo(() =>
    checkPasswordWarnings(password),
    [password]
  );

  // Verifica se a senha foi exposta em vazamentos quando ela atinge um certo nível de força
  useEffect(() => {
    if (password.length >= 8 && score >= 50 && !isChecking) {
      const checkBreachedStatus = async () => {
        setIsChecking(true);
        try {
          const breached = await checkBreached?.();
          setIsBreached(breached || false);
        } catch (error) {
          console.error('Error checking breach status:', error);
        } finally {
          setIsChecking(false);
        }
      };

      setIsBreached(null);

      const timeoutId = setTimeout(checkBreachedStatus, 800);
      return () => clearTimeout(timeoutId);
    }
  }, [password, score, checkBreached]);

  const getColorClass = () => {
    if (isBreached) return 'bg-red-500';

    switch (level) {
      case 'muitoFraca': return 'bg-red-500';
      case 'fraca': return 'bg-orange-500';
      case 'moderada': return 'bg-yellow-500';
      case 'boa': return 'bg-green-600';
      case 'forte': return 'bg-green-500';
      default: return 'bg-gray-300';
    }
  };

  const getLevelText = () => {
    if (isBreached) return 'Comprometida';

    switch (level) {
      case 'muitoFraca': return 'Força da senha: Fraca';
      case 'fraca': return 'Força da senha: Fraca';
      case 'moderada': return 'Força da senha: Moderada';
      case 'boa': return 'Força da senha: Boa';
      case 'forte': return 'Força da senha: Forte';
      default: return '';
    }
  };

  const getFeedbackText = () => {
    // Primeiro, verificar se a senha foi encontrada em vazamentos
    if (isBreached) {
      return 'Esta senha foi encontrada em vazamentos de dados!';
    }

    // Depois, verificar se há avisos de padrões comuns, sequências ou caracteres repetidos
    const detectedWarnings = passwordWarnings.filter(warning => warning.isDetected);
    if (detectedWarnings.length > 0) {
      // Se houver apenas um aviso, retorná-lo
      if (detectedWarnings.length === 1) {
        return `Evite usar: ${detectedWarnings[0].label}`;
      }

      // Se houver múltiplos avisos, mostrar o primeiro e indicar que há mais
      return `Evite usar: ${detectedWarnings[0].label} e outros padrões fracos`;
    }

    // Se não houver avisos, retornar o feedback padrão
    return feedback;
  };



  return (
    <div className="w-full mt-1 mb-3">
      <div className="flex justify-between mb-1 items-start">
        <div className="flex items-center gap-1 flex-shrink-0">
          <Text className="">{getLevelText()}</Text>
        </div>
        {isChecking ? (
          <Text className="text-xs font-medium min-h-[1.5em] flex items-center justify-end w-[280px] text-right flex-wrap">Verificando segurança...</Text>
        ) : (
          <Text
            className={cn(
              "text-xs font-medium min-h-[1.5em] flex items-center justify-end w-[280px] text-right flex-wrap",
              isBreached ? "text-red-500" :
              passwordWarnings.some(w => w.isDetected) ? "text-gray-400" :
              ""
            )}
          >
            {getFeedbackText()}
          </Text>
        )}
      </div>


      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
        <div
          className={cn("h-2.5 rounded-full transition-all duration-300", getColorClass())}
          style={{ width: `${score}%` }}
        ></div>
      </div>

      <div className="mt-2">
        <Text className="py-2">Sua senha deve conter:</Text>
        <div className="flex flex-col gap-1">
          {
            passwordRules.map(rule => (
              <div key={rule.id} className="flex items-center gap-0.5">
                {rule.isFulfilled ? (
                  <SquareCheck size={16} className="text-green-500" />
                ) : <SquareX size={16} className="text-red-500"/>
                }
                <Text className={cn(
                  "transition-colors flex items-center",
                  rule.isFulfilled
                    ? " text-green-500"
                    : "text-balance"
                )}>{rule.label}</Text>
              </div>
            ))
          }
        </div>
      </div>
    </div>
  );
};

export interface PasswordRule {
  id: string;
  label: string;
  isFulfilled: boolean;
}

export interface PasswordWarning {
  id: string;
  label: string;
  isDetected: boolean;
}

/**
 * Verifica se a senha contém padrões comuns, sequências ou caracteres repetidos
 * @param password Senha a ser validada
 * @returns Array de avisos com status de detecção
 */
export const checkPasswordWarnings = (password: string): PasswordWarning[] => {
  if (!password) {
    return [
      { id: 'commonPatterns', label: "padrões comuns (senha123, admin)", isDetected: false },
      { id: 'sequences', label: "sequências previsíveis (123456, qwerty)", isDetected: false },
      { id: 'repeatedChars', label: "caracteres repetidos (aaa, 111)", isDetected: false },
    ];
  }

  const hasCommonPatterns = commonPatterns.some(pattern => pattern.test(password));
  const hasSequences = sequences.some(seq => password.toLowerCase().includes(seq.toLowerCase()));
  const hasRepeatedChars = !!password.match(/(.)\1{2,}/g);

  return [
    {
      id: 'commonPatterns',
      label: "padrões comuns (senha123, admin)",
      isDetected: hasCommonPatterns
    },
    {
      id: 'sequences',
      label: "sequências previsíveis (123456, qwerty)",
      isDetected: hasSequences
    },
    {
      id: 'repeatedChars',
      label: "caracteres repetidos (aaa, 111)",
      isDetected: hasRepeatedChars
    }
  ];
};

/**
 * Verifica as regras de segurança da senha e retorna o status de cada regra
 * @param password Senha a ser validada
 * @returns Array de regras com status de cumprimento
 */
export const checkPasswordRules = (password: string): PasswordRule[] => {
  if (!password) {
    return [
      { id: 'minLength', label: "Mínimo 8 caracteres", isFulfilled: false },
      { id: 'hasLowercase', label: "Letra minúscula", isFulfilled: false },
      { id: 'hasUppercase', label: 'Letra maiúscula', isFulfilled: false },
      { id: 'hasNumbers', label: 'Número', isFulfilled: false },
      { id: 'hasSpecialChars', label: "Caracter especial (Ex: !, @, #, $, %, etc.)", isFulfilled: false },
      // { id: 'noCommonPatterns', label: 'Sem padrões comuns', isFulfilled: false },
      // { id: 'noSequences', label: 'Sem sequências previsíveis', isFulfilled: false },
      // { id: 'noRepeatedChars', label: 'Sem caracteres repetidos', isFulfilled: false },
    ];
  }


  const hasMinLength = password.length >= 8;

  // Para senhas muito curtas, não considere que passou na verificação de padrões comuns
  //const hasNoCommonPatterns = password.length >= 4 && !commonPatterns.some(pattern => pattern.test(password));

  // Para senhas muito curtas, não considere que passou na verificação de sequências
  //const hasNoSequences = password.length >= 4 && !sequences.some(seq => password.toLowerCase().includes(seq.toLowerCase()));

  // Para senhas muito curtas, não considere que passou na verificação de caracteres repetidos
  //const hasNoRepeatedChars = password.length >= 3 && !(password.match(/(.)\1{2,}/g) && password.match(/(.)\1{2,}/g)!.length > 0);

  // Verificação de variedade de caracteres
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);
  const hasSpecialChars = /[^A-Za-z0-9]/.test(password);

  return [
    {
      id: 'minLength',
      label: 'Mínimo 8 caracteres',
      isFulfilled: hasMinLength
    },
    // {
    //   id: 'noCommonPatterns',
    //   label: 'Sem padrões comuns',
    //   isFulfilled: hasNoCommonPatterns
    // },
    // {
    //   id: 'noSequences',
    //   label: 'Sem sequências previsíveis',
    //   isFulfilled: hasNoSequences
    // },
    // {
    //   id: 'noRepeatedChars',
    //   label: 'Sem caracteres repetidos',
    //   isFulfilled: hasNoRepeatedChars
    // },
    {
      id: 'hasLowercase',
      label: 'Letra minúscula',
      isFulfilled: hasLowercase
    },
    {
      id: 'hasUppercase',
      label: 'Letra maiúscula',
      isFulfilled: hasUppercase
    },
    {
      id: 'hasNumbers',
      label: 'Número',
      isFulfilled: hasNumbers
    },
    {
      id: 'hasSpecialChars',
      label: "Caracter especial (Ex: !, @, #, $, %, etc.)",
      isFulfilled: hasSpecialChars
    }
  ];
};



/**
 * Valida se uma senha atende aos requisitos mínimos de segurança NIST
 * @param password Senha a ser validada
 * @returns Objeto com resultado da validação e mensagem de erro
 */
export const validatePasswordNIST = (password: string): {
  isValid: boolean;
  errorMessage: string;
  rules: PasswordRule[];
} => {
  const rules = checkPasswordRules(password);

  if (!password) {
    return {
      isValid: false,
      errorMessage: "A senha deve ser preenchida.",
      rules
    };
  }

  // Comprimento mínimo (NIST recomenda pelo menos 8 caracteres)
  if (!rules.find(r => r.id === 'minLength')?.isFulfilled) {
    return {
      isValid: false,
      errorMessage: "A senha deve conter: mínimo 8 caracteres.",
      rules
    };
  }

  // Verificar padrões comuns
/*   if (!rules.find(r => r.id === 'noCommonPatterns')?.isFulfilled) {
    return {
      isValid: false,
      errorMessage: "A senha contém padrões comuns facilmente adivináveis.",
      rules
    };
  } */

  // Verificar sequências
/*   if (!rules.find(r => r.id === 'noSequences')?.isFulfilled) {
    return {
      isValid: false,
      errorMessage: "A senha contém sequências de caracteres previsíveis.",
      rules
    };
  } */

  // Verificar caracteres repetidos
/*   if (!rules.find(r => r.id === 'noRepeatedChars')?.isFulfilled) {
    return {
      isValid: false,
      errorMessage: "A senha contém caracteres repetidos em sequência.",
      rules
    };
  } */

  // Verificar variedade de caracteres (NIST não exige explicitamente, mas é uma boa prática)
  const hasLowercase = rules.find(r => r.id === 'hasLowercase')!.isFulfilled;
  const hasUppercase = rules.find(r => r.id === 'hasUppercase')!.isFulfilled;
  const hasNumbers = rules.find(r => r.id === 'hasNumbers')!.isFulfilled;
  const hasSpecialChars = rules.find(r => r.id === 'hasSpecialChars')!.isFulfilled;

  const characterVarietyRules = [hasLowercase, hasUppercase, hasNumbers, hasSpecialChars];
  const varietyCount = characterVarietyRules.filter(Boolean).length;

  if (varietyCount < 4) {
    // Criar uma mensagem específica baseada nos requisitos que faltam
    const missingRequirements = [];

    if (!hasLowercase) missingRequirements.push("letra minúscula");
    if (!hasUppercase) missingRequirements.push("letra maiúscula");
    if (!hasNumbers) missingRequirements.push("número");
    if (!hasSpecialChars) missingRequirements.push("caracter especial");

    const missingText = missingRequirements.join(", ").replace(/,([^,]*)$/, " e$1");

    return {
      isValid: false,
      errorMessage: `A senha deve conter: ${missingText}.`,
      rules
    };
  }

  return {
    isValid: true,
    errorMessage: "",
    rules
  };
};

export default PasswordStrengthMeter;
