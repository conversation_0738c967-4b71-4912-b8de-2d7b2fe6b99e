import { ValueWithSource } from "./ValueWithSource";

interface _PartidoPoliticoBase {
    sigla: string;
    uf?: string;
    "data consulta"?: string;
    "label default key"?: string;
}

interface _PartidoPoliticoTSE extends _PartidoPoliticoBase {
    vinculo?: Array<_Vinculo>;
}

interface _Vinculo {
    "data registro"?: string;
    situacao?: string;
    tipo?: string;
    "data desfiliacao"?: string;
    "data cancelamento"?: string;
    "motivo cancelamento"?: string;
}

export type _PartidoPolitico = _PartidoPoliticoBase | _PartidoPoliticoTSE;

export interface Vinculo {
    "data registro": ValueWithSource<string>;
    situacao?: ValueWithSource<string>;
    tipo?: ValueWithSource<string>;
    "data desfiliacao"?: ValueWithSource<string>;
    "data cancelamento"?: ValueWithSource<string>;
    "motivo cancelamento"?: ValueWithSource<string>;
}

export interface FiliacaoPartidaria {
    sigla: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
    vinculos?: Array<Vinculo>;
}
