{"creationDate": "<PERSON><PERSON>, 22 Apr 2025 13:42:53 GMT", "creditsUsed": 10, "data": {"cpf": [{"BancoNacionalDeMonitoramentoDePrisoes": [{"endpoint": "BancoNacionalDeMonitoramentoDePrisoes", "mandado": [{"numero do mandado de prisao": "0001234-56.2023.8.19.0001.01.0001-01", "tipo de mandado": "Preventiva", "situacao": "Pendente de Cumprimento", "numero do processo": "0001234-56.2023.8.19.0001", "tipificacoes penais": "Art. 157, § 2º, I e II do Código Penal - Roubo qualificado", "data de expedicao": "15/03/2023", "data de validade": "15/03/2043", "especie de prisao": "Prisão Preventiva", "municipio": "Rio de Janeiro", "orgao expedidor": "Vara Criminal da Comarca do Rio de Janeiro", "magistrado": "Dr. <PERSON>", "pessoa": [{"nome pai": "<PERSON><PERSON><PERSON><PERSON>", "pena imposta": "0 ano(s) 0 mes(es) 0 dia(s).", "recaptura": "<PERSON><PERSON>"}]}, {"numero do mandado de prisao": "0007890-12.2022.8.26.0001.01.0002-01", "tipo de mandado": "Definitiva", "situacao": "<PERSON><PERSON><PERSON><PERSON>", "numero do processo": "0007890-12.2022.8.26.0001", "tipificacoes penais": "Art. 33 da <PERSON><PERSON> 11.343/06 - <PERSON><PERSON><PERSON><PERSON><PERSON> de Drogas", "data de expedicao": "10/11/2022", "data de validade": "10/11/2042", "especie de prisao": "<PERSON><PERSON><PERSON> Definitiva", "municipio": "São Paulo", "orgao expedidor": "2ª Vara de Execuções Criminais de São Paulo", "magistrado": "<PERSON><PERSON><PERSON>", "pessoa": [{"nome pai": "<PERSON><PERSON><PERSON><PERSON>", "pena imposta": "5 anos e 10 meses de reclusão em regime semi-aberto", "recaptura": "<PERSON>m"}]}]}], "CadastroNacionalPJ": [{"endpoint": "CadastroNacionalPJ", "pessoa": [{"empresa": [{"cnpj": "98.765.432/0001-10", "razao social": "Tech Inovação S.A.", "procon": "Não consta", "info restricao": "Nada consta", "status receita": "Ativo", "data de fundacao": "15/03/2015", "data_abertura": "15/03/2015", "sequencial": "9876543210", "porte": "GRANDE", "tipo de imposto": "LUCRO REAL", "total de funcionarios": "250", "quantidade de funcionarios acima de 5 salarios": "75", "quantidade de funcionarios abaixo de 5 salarios": "175", "cnae": "6209-1/00", "descricao do cnae": "Suporte técnico, manutenção e outros serviços em tecnologia da informação", "cnae1": "6209-1/00", "cnae2": "6311-9/00", "cnae secundario": "6311-9/00", "cnae secundario2": "6202-3/00", "label default key": "socio", "location": [{"logradouro": "Avenida <PERSON>", "numero": "1500", "complemento": "Andar 15", "bairro": "Bela Vista", "city": "São Paulo", "area": "Centro", "cep ou zipcode": "01310-200", "area code": "11", "telefone relacionado": "3456-7890", "label default key": "<PERSON><PERSON>"}, {"logradouro": "Rua da Tecnologia", "numero": "789", "complemento": "Bloco B", "bairro": "Alphaville", "city": "<PERSON><PERSON><PERSON>", "area": "Grande São Paulo", "cep ou zipcode": "06454-000", "area code": "11", "telefone relacionado": "4567-8901", "label default key": "Filial"}], "phonenumber": [{"operadora": "CLARO", "phone number": "(11) 3456-7890", "whatsapp": "<PERSON><PERSON>"}, {"operadora": "CLARO", "phone number": "(11) 4567-8901", "whatsapp": "<PERSON><PERSON>"}, {"operadora": "CLARO", "phone number": "(11) 99876-5432", "whatsapp": "<PERSON>m"}]}]}]}], "Escavador": [{"endpoint": "Escavador", "processos": [{"numero do processo": "0001234-56.2023.8.26.0100", "instancia": "1ª Instância", "orgao": "Tribunal de Justiça de São Paulo", "data da remessa": "2023-05-15", "data instauracao": "2023-01-10", "movimentacoes": "ata - Conteudo - Tipo,2025-01-21 - Intimação do <PERSON><PERSON> <PERSON>, <PERSON><PERSON>. <PERSON><PERSON>, Dra. <PERSON> Moraes e Dr<PERSON> <PERSON> Barros Vidal do despacho de f. 4448, que segue transcrito: Ciência às partes acerca do Acórdão de f. 4426-39. <PERSON><PERSON><PERSON>, cumpram-se as determinações contidas na sentença de f. 4122-47. Às providências necessárias.\" Campo Grande, 13 de janeiro de 2025. CARLOS ALBERTO GARCETE Juiz de Direito em Substituição Legal - PUBLICACAO,2024-12-02 - <PERSON><PERSON><PERSON> <PERSON> - 2ª Vara do Tribunal do Júri EMENTA - APELAÇÃO CRIMINAL - HOMICÍDIO QUALIFICADO - RECURSO INTERPOSTO PELO MINISTÉRIO PÚBLICO ESTADUAL - IMPRONÚNCIA MANTIDA - AUSÊNCIA DE INDÍCIOS MÍNIMOS E SUFICIENTES APTOS A CONVENCER DA MATERIALIDADE DO FATO E DA EXISTÊNCIA DE INDÍCIOS SUFICIENTES DA AUTORIA/PARTICIPAÇÃO - RECURSO DESPROVIDO. 1. <PERSON> bem sabe, o sistema processual penal brasileiro adota duas fases distintas para o processamento dos crimes dolosos contra a vida: a primeira, denominada de jus accusationis, tem objetivo voltado ao juízo de admissibilidade da acusação; já a segunda", "advogado": [{"cpf": "12345678900", "full name": "<PERSON>", "label default key": "Advogado", "oab": "OAB/SP 12345"}, {"cpf": "98765432100", "full name": "<PERSON>", "label default key": "Advogado", "oab": "OAB/SP 54321"}], "pessoa": [{"cpf": "11122233344", "full name": "<PERSON>", "label default key": "Autor"}, {"cpf": "44455566677", "full name": "<PERSON>", "label default key": "Autor"}, {"cpf": "55566677788", "full name": "Dr. <PERSON>", "label default key": "<PERSON><PERSON>"}, {"cpf": "99988877766", "full name": "Dr. <PERSON>", "label default key": "<PERSON><PERSON>"}, {"cpf": "99988877766", "full name": "<PERSON>", "label default key": "Representante"}, {"cpf": "11122233344", "full name": "<PERSON>", "label default key": "Representante"}], "empresa": [{"cnpj": "12345678000190", "label default key": "<PERSON><PERSON><PERSON>", "razao social": "Empresa ABC Ltda."}, {"cnpj": "98765432000110", "label default key": "<PERSON><PERSON><PERSON>", "razao social": "Empresa XYZ S.A."}]}, {"numero do processo": "0007890-12.2022.8.26.0200", "instancia": "2ª Instância", "orgao": "Tribunal de Justiça de São Paulo", "data da remessa": "2022-11-20", "data instauracao": "2022-08-05", "movimentacoes": "20/11/2022 - Recurso recebido pelo Tribunal\n15/10/2022 - Recurso interposto pela parte autora\n30/09/2022 - Sentença proferida: procedente em parte\n15/09/2022 - Conclusos para sentença\n05/08/2022 - Distribuição do processo", "advogado": [{"cpf": "22233344455", "full name": "<PERSON>", "label default key": "Advogado", "oab": "OAB/SP 78901"}, {"cpf": "55566677788", "full name": "<PERSON>", "label default key": "Advogado", "oab": "OAB/SP 54321"}], "empresa": [{"cnpj": "98765432000110", "label default key": "Recorrente", "razao social": "Empresa XYZ S.A."}, {"cnpj": "98765432000110", "label default key": "Recorrente", "razao social": "Empresa XYZ S.A."}], "pessoa": [{"cpf": "44455566677", "full name": "<PERSON>", "label default key": "Recorrido"}, {"cpf": "11122233344", "full name": "<PERSON>", "label default key": "Recorrido"}]}]}], "EscavadorDOCPF": [{"endpoint": "EscavadorDOCPF", "diario oficial": [{"local": "<PERSON><PERSON><PERSON> União", "data": "15/05/2023", "descricao": "Nomear JOÃO DA SILVA, CPF 123.456.789-00, para exercer o cargo de Analista Administrativo do quadro de pessoal do Ministério da Economia.", "dados adicionais": "Se<PERSON> 2, <PERSON><PERSON><PERSON><PERSON> 45", "link": "https://www.in.gov.br/web/dou/-/portaria-de-15-de-maio-de-2023"}, {"local": "Diário de Justiça do Estado de São Paulo", "data": "22/06/2023", "descricao": "Processo nº 0001234-56.2023.8.26.0100. Autor: <PERSON>. Réu: Empresa XYZ Ltda. Juiz: Dr. <PERSON>. Decisão: <PERSON><PERSON> procedente o pedido...", "dados adicionais": "Cade<PERSON> 4, <PERSON><PERSON><PERSON><PERSON> 123", "link": "https://www.dje.tjsp.jus.br/cdje/consultaSimples.do?cdVolume=15&nuDiario=3528"}]}], "EscavadorDONome": [{"endpoint": "EscavadorDONome", "diario oficial": [{"local": "Minister<PERSON>o", "data": "2010-03-03", "descricao": "...fevereiro de 2010, ELISETE...", "dados adicionais": "Diario Oficial do Estado do Rio de Janeiro", "link": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1"}, {"local": "Poder Executivo", "data": "2015-08-12", "descricao": "Portaria nº 123/2015 - Nomear MARIA SILVA para exercer o cargo...", "dados adicionais": "Diário Oficial do Estado de São Paulo", "link": "https://www.escavador.com/diarios/123456/DOESP/poder-executivo/2015-08-12?page=5"}]}], "PAIscpf": [{"endpoint": "PAIscpf", "PAIs": [{"aplicacao": "casasbahia", "found": "Não", "alerta": "Não", "tipo_alerta": "Não se Aplica"}, {"aplicacao": "Estacionamento Digital", "found": "<PERSON>m", "alerta": "Não", "tipo_alerta": "Não se Aplica"}]}], "ProvedorDeAplicacacaoDaInternetcpf": [{"endpoint": "ProvedorDeAplicacacaoDaInternetcpf", "provedor de aplicacoes de internet": [{"aplicacao": "Facebook", "found": "<PERSON>m", "alerta": "<PERSON>m", "tipo_alerta": "Notificação"}, {"aplicacao": "Instagram", "found": "<PERSON>m", "alerta": "Não", "tipo_alerta": "Não se Aplica"}]}], "QueridoDiarioCPF": [{"endpoint": "QueridoDiarioCPF", "diario oficial": [{"local": "São Paulo", "uf": "SP", "data": "10/07/2023", "edicao extra?": "Não", "link": "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx", "frase": [{"texto": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses."}]}, {"local": "Rio de Janeiro", "uf": "RJ", "data": "05/08/2023", "edicao extra?": "<PERSON>m", "link": "https://doweb.rio.rj.gov.br/", "frase": [{"texto": "EXTRATO DE CONTRATO Nº 123/2023. Partes: Município do Rio de Janeiro e João da Silva ME. Objeto: Prestação de serviços de manutenção predial. Valor: R$ 150.000,00. Prazo: 12 meses."}]}]}], "QueridoDiarioNome": [{"endpoint": "QueridoDiarioNome", "diario oficial": [{"local": "Belo Horizonte", "uf": "MG", "data": "2022-05-20", "edicao extra?": "Não", "link": "https://dom-web.pbh.gov.br/", "frase": [{"texto": "PORTARIA SMASAC Nº 055/2022 - A Secretária Municipal de Assistência Social, Segurança Alimentar e Cidadania, no uso de suas atribuições legais, resolve nomear MARIA SILVA, matrícula 123.456-7, para a função de Coordenadora do Centro de Referência de Assistência Social - CRAS Norte."}]}]}], "SNAP": [{"endpoint": "SNAP", "phonenumber": [{"phone number": "31991701966", "operadora": "CLARO"}, {"phone number": "31973113426", "operadora": "VIVO", "whatsapp": "<PERSON>m"}, {"phone number": "21976886755", "operadora": "TIM"}], "emailaddress": [{"email address": "<EMAIL>"}, {"email address": "<EMAIL>"}], "empresa": [{"cnpj": "12345678000190", "razao social": "EMPRESA EXEMPLO LTDA", "nome fantasia": "EXEMPLO COMERCIO", "label default key": "outros_contatos", "phonenumber": [{"phone number": "1133334444", "operadora": "CLARO"}], "location": [{"logradouro": "AV PAULISTA", "numero": "1000", "complemento": "ANDAR 10", "bairro": "BELA VISTA", "city": "SAO PAULO", "area": "SP", "cep ou zipcode": "01310100"}]}, {"cnpj": "12.345.678/0001-90", "razao social": "Empresa ABC Ltda.", "procon": "Não consta", "info restricao": "Nada consta", "status receita": "Ativo", "data de fundacao": "01/01/2000", "label default key": "socio", "location": [{"logradouro": "<PERSON><PERSON>", "numero": "123", "complemento": "Apto 101", "bairro": "<PERSON><PERSON><PERSON>", "city": "São Paulo", "area": "Zona Sul", "cep ou zipcode": "01234-567", "area code": "11", "telefone relacionado": "98765-4321", "label default key": "Residencial"}, {"logradouro": "Avenida <PERSON>", "numero": "1578", "complemento": "Sala 304", "bairro": "Bela Vista", "city": "São Paulo", "area": "Centro", "cep ou zipcode": "01310-200", "area code": "11", "telefone relacionado": "3456-7890", "label default key": "Comercial"}], "phonenumber": [{"operadora": "CLARO", "phone number": "(11) 98765-4321", "whatsapp": "<PERSON><PERSON>"}, {"operadora": "CLARO", "phone number": "(11) 3456-7890", "whatsapp": "<PERSON><PERSON>"}]}], "pessoa": [{"full name": "AILTON RAMOS DA SILVA JUNIOR", "cpf": "08936532766", "data nascimento": "04/06/1982", "label default key": "outros_contatos", "mae": "ANTONIA MARIA O RAMOS SILVA", "phonenumber": [{"phone number": "552124669210", "operadora": "OI"}], "location": [{"logradouro": "RMAGNO MARTINS", "numero": "128", "complemento": "CS 2105", "bairro": "FREGUESIA", "city": "RIO DE JANEIRO", "area": "RJ", "cep ou zipcode": "21911430"}]}, {"full name": "CARLOS EDUARDO SANTOS", "cpf": "12345678901", "data nascimento": "15/08/1975", "label default key": "outros_contatos", "phonenumber": [{"phone number": "5521987654321", "operadora": "VIVO"}]}, {"bookmark": 4, "full name": "<PERSON>", "idade": "35", "sexo": "M", "nacionalidade": "Brasileira", "pais do passaporte": "Brasil", "data nascimento": "10/05/1989", "cpf": "123.456.789-10", "identidade": "MG-12.345.678", "estadocivil": "<PERSON><PERSON>iro", "status receita": "Ativo", "info restricao": "Sem restrições", "titulo de eleitor": "1234 5678 9012", "pis/pasep": "123.45678.90-1", "ctps": "123456 - SP", "procon": "<PERSON><PERSON><PERSON>", "escolaridade": "Superior Completo", "grauinstrucao": "Graduação", "descricao cbo": "Desenvolvedor de Software", "cbo": "2123-05", "renda estimada": "R$ 5.000,00", "renda presumida": "R$ 5.000,00", "data de admissao": "2020-01-15", "pessoa": [{"full name": "<PERSON>", "label default key": "parente MAE", "nome1": "<PERSON>", "cpf": "123.456.789-00", "titulo de eleitor": "1234 5678 9012", "data nascimento": "01/01/1970", "sexo": "F", "procon": "Não consta", "location": [{"logradouro": "<PERSON><PERSON>", "numero": "123", "complemento": "Casa 101", "bairro": "<PERSON><PERSON><PERSON>", "area": "SP", "cep ou zipcode": "01234-567"}], "phonenumber": [{"phone number": "31991701966", "operadora": "VIVO"}, {"phone number": "31973113426", "operadora": "OI"}]}]}], "location": [{"logradouro": "<PERSON><PERSON>", "numero": "123", "complemento": "Apto 101", "bairro": "<PERSON><PERSON><PERSON>", "city": "São Paulo", "area": "Zona Sul", "cep ou zipcode": "01234-567", "cidade": "São Paulo", "estado ou regiao": "SP", "label default key": "31991701966"}, {"logradouro": "Avenida <PERSON>", "numero": "1578", "complemento": "Sala 304", "bairro": "Bela Vista", "area": "Centro", "cep ou zipcode": "01310-200", "cidade": "São Paulo", "estado ou regiao": "SP", "label default key": "31973113426"}], "remuneracao": [{"data admissao": "23/04/2015", "empresa": [{"cnpj": "16.701.716/0001-56", "razao social": "STELLANTIS AUTOMOVEIS BRASIL LTDA"}], "empresa pagadora": "STELLANTIS AUTOMOVEIS BRASIL LTDA", "valor": "R$ 8.500,00", "situacao": "Ativo", "cargo": "<PERSON><PERSON><PERSON> de Si<PERSON>mas"}, {"data admissao": "01/11/2017", "empresa": [{"cnpj": "03.492.162/0001-82", "razao social": "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA"}], "empresa pagadora": "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA", "valor": "R$ 2.089,92", "situacao": "Inativo", "cargo": "Assistente Administrativo", "data demissao": "15/03/2019"}, {"data admissao": "08/02/2010", "empresa": [{"cnpj": "02.982.267/0003-19", "razao social": "NAVI CARNES INDUSTRIA E COMERCIO LTDA"}], "empresa pagadora": "NAVI CARNES INDUSTRIA E COMERCIO LTDA", "valor": "R$ 540,60", "situacao": "Inativo", "cargo": "Auxiliar de Produção", "data demissao": "10/08/2010"}, {"data admissao": "11/05/2010", "empresa": [{"cnpj": "03.501.566/0001-95", "razao social": "PREFEITURA MUNICIPAL DE ROCHEDO"}], "empresa pagadora": "MUNICIPIO DE ROCHEDO", "valor": "R$ 577,08", "situacao": "Inativo", "cargo": "Assistente Administrativo", "data demissao": "30/12/2010"}, {"data admissao": "13/11/2014", "empresa": [{"cnpj": "19.314.695/0001-04", "razao social": "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA"}], "empresa pagadora": "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA", "valor": "R$ 900,00", "situacao": "Inativo", "cargo": "<PERSON><PERSON><PERSON>", "data demissao": "05/03/2015"}]}], "SintegraMA": [{"endpoint": "SintegraMA", "phonenumber": [{"phone number": "21988563455", "operadora": "CLARO"}]}], "PortalDaTransparenciaDeMinasGerais": [{"endpoint": "PortalDaTransparenciaDeMinasGerais", "pessoa": [{"nome do orgao": "Secretaria de Estado de Educação", "valor pago": "R$ 15.750,00", "valor empenhado": "R$ 16.000,00", "valor liquidado": "R$ 15.750,00"}], "despesas publicas recursos recebidos": [{"nome do orgao": "Secretaria de Estado de Educação", "valor pago": "R$ 30.950,00", "valor empenhado": "R$ 28.000,00", "valor liquidado": "R$ 30.950,00"}]}], "PortalDaTransparenciaDoAmazonas": [{"endpoint": "PortalDaTransparenciaDoAmazonas", "despesas publicas recursos recebidos": [{"nome do orgao": "Secretaria de Estado de Educação", "codigo do orgao": "00123", "pagamento execicio anterior": "R$ 0,00", "numero de ordem bancaria": "2023OB00123", "numero da nota de lancamento": "2023NL00456", "numero do empenho": "2023EM00789", "fonte do recurso": "Re<PERSON>rs<PERSON>", "classificacao": "Educação"}]}], "TransparenciaSC": [{"endpoint": "TransparenciaSC", "despesas publicas recursos recebidos": [{"nome do orgao": "Secretaria de Estado da Saúde", "data": "10/06/2023", "credor": "<PERSON>", "unidade gestora": "Fundo Estadual de Saúde", "status pagamento": "Pago", "pagamento": "Integral", "valor": "R$ 7.800,00", "numero de ordem bancaria": "2023OB00456", "historico": "Prestação de serviços de consultoria"}]}], "TransparenciaManausCPF": [{"endpoint": "TransparenciaManausCPF", "empenho": [{"numero": "2023NE00321", "favorecido": "<PERSON>", "unidade gestora": "Secretaria Municipal de Educação", "data": "05/04/2023", "valor empenhado": "R$ 22.500,00", "acrescimo": "R$ 0,00", "anulado": "R$ 0,00", "pago": "R$ 22.500,00", "liquidado": "R$ 22.500,00"}]}], "TransparenciaDF": [{"endpoint": "TransparenciaDF", "despesas publicas recursos recebidos": [{"valor": "R$ 18.900,00", "data": "12/07/2023", "credor": "<PERSON>", "valor empenhado": "R$ 19.000,00", "valor liquidado": "R$ 18.900,00"}], "viagem": [{"unidade gestora": "Secretaria de Estado de Economia", "ano_mes": "2023/07", "credor": "<PERSON>", "tipo de despesa": "<PERSON><PERSON><PERSON><PERSON>", "id": "DF2023-0789", "esfera": "Fiscal", "programa de trabalho": "04.122.8203.8517.0051", "funcao": "Administração", "subfuncao": "Administração Geral", "programa": "Gestão Pública", "categoria economica": "Despesas Correntes", "grupo de natureza da despesa": "Outras Despesas Correntes", "modalidade de aplicacao": "Aplicação Direta", "elemento": "Diárias - Civil", "subelemento": "Diárias no País", "fonte de recurso": "Re<PERSON>rs<PERSON>", "empenhado": "R$ 3.500,00", "liquidado": "R$ 3.500,00", "pago ex": "R$ 0,00", "pago rpp": "R$ 0,00", "pago rpnp": "R$ 0,00", "pago ret": "R$ 0,00", "valor": "R$ 3.500,00"}]}], "TransparenciaPRNome": [{"endpoint": "TransparenciaPRNome", "servidor publico": [{"nome": "<PERSON>", "carreira": "<PERSON><PERSON><PERSON> de Si<PERSON>mas", "instituicao": "Secretaria de Estado da Administração e da Previdência", "quadro funcional": "Quadro Próprio do Poder Executivo - QPPE", "municipio": "Curitiba"}, {"nome": "<PERSON>", "carreira": "Professor", "instituicao": "Secretaria de Estado da Educação", "quadro funcional": "Quadro Próprio do Magistério - QPM", "municipio": "<PERSON><PERSON><PERSON>"}]}], "TSEFiliacaoPartidaria": [{"endpoint": "TSEFiliacaoPartidaria", "partido politico": [{"sigla": "PT", "uf": "SP", "data consulta": "15/05/2023", "vinculo": [{"data registro": "10/03/2010", "situacao": "Regular", "tipo": "Filiação"}]}, {"sigla": "PSDB", "uf": "RJ", "data consulta": "15/05/2023", "vinculo": [{"data registro": "05/04/2015", "situacao": "<PERSON><PERSON><PERSON><PERSON>", "tipo": "Desfiliação"}]}, {"sigla": "MDB", "uf": "MG", "data consulta": "15/05/2023", "vinculo": [{"data registro": "12/06/2018", "situacao": "Regular", "tipo": "Filiação"}, {"data registro": "20/09/2020", "situacao": "Cancelado", "tipo": "Cancelamento"}]}]}], "TSEDoacoes": [{"endpoint": "TSEDoadoresEFornecimento", "pessoa": [{"candidato": [{"nome": "<PERSON>", "cpf": "123.456.789-00", "ano": "2022", "cargo_eleitoral": "Vereador", "unidade_eleitoral": "São Paulo / SP", "numero_candidato": "12345", "partido_eleitoral": "Partido ABC", "vinculo": [{"valor": "R$ 1.000,00", "data": "10/05/2022", "recibo": "123456789", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "tipo": "Financeira"}, {"valor": "R$ 500,00", "data": "15/06/2022", "recibo": "987654321", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "tipo": "Financeira"}]}, {"nome": "<PERSON>", "cpf": "987.654.321-00", "ano": "2022", "cargo_eleitoral": "<PERSON><PERSON><PERSON>", "unidade_eleitoral": "Rio de Janeiro / RJ", "numero_candidato": "54321", "partido_eleitoral": "Partido XYZ", "vinculo": [{"valor": "R$ 2.000,00", "data": "20/07/2022", "recibo": "246813579", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "tipo": "Financeira"}]}]}], "candidato": [{"empresa": [{"razao_social": "Empresa ABC Ltda", "cnpj": "12.345.678/0001-90", "cnae": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "vinculo": [{"valor": "R$ 5.000,00", "data": "15/08/2022", "documento": "DOC123456", "recibo": "REC987654", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos de pessoa jurídica", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "tipo": "Financeira"}, {"valor": "R$ 3.000,00", "data": "20/09/2022", "documento": "DOC654321", "recibo": "REC123456", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos de pessoa jurídica", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "tipo": "Financeira"}]}, {"razao_social": "XYZ Comércio e Serviços S.A.", "cnpj": "98.765.432/0001-10", "cnae": "4751-2/01 - Comércio varejista especializado de equipamentos e suprimentos de informática", "vinculo": [{"valor": "R$ 10.000,00", "data": "05/10/2022", "documento": "DOC789012", "recibo": "REC345678", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos de pessoa jurídica", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Doação direta", "tipo": "Financeira"}]}], "pessoa": []}, {"empresa": [], "pessoa": [{"nome_completo": "<PERSON>", "cpf": "123.456.789-00", "vinculo": [{"valor": "R$ 1.500,00", "data": "12/07/2022", "documento": "DOC456789", "recibo": "REC567890", "descricao": "Doação para campanha eleitoral", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "tipo": "Financeira"}]}, {"nome_completo": "<PERSON>", "cpf": "987.654.321-00", "vinculo": [{"valor": "R$ 800,00", "data": "25/08/2022", "documento": "DOC234567", "recibo": "REC678901", "descricao": "Doação para material de campanha", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa Física", "fonte": "Doação direta", "tipo": "Financeira"}]}]}]}], "TSEFornecimento": [{"endpoint": "TSEDoadoresEFornecimento", "candidato": [{"empresa": [{"razao_social": "Empresa XYZ Ltda", "cnpj": "12.345.678/0001-90", "cnae": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "vinculo": [{"valor": "R$ 3.500,00", "data": "10/05/2022", "documento": "DOC123456", "recibo": "REC987654", "descricao": "Fornecimento de material gráfico para campanha", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Fornecimento direto", "tipo": "Financeira"}, {"valor": "R$ 1.200,00", "data": "15/06/2022", "documento": "DOC654321", "recibo": "REC123456", "descricao": "Fornecimento de serviços de consultoria", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Fornecimento direto", "tipo": "Financeira"}]}]}], "pessoa": [{"candidato": [{"nome": "<PERSON>", "cpf": "123.456.789-00", "ano_da_eleicao": "2022", "cargo_eleitoral": "Vereador", "unidade_eleitoral": "São Paulo / SP", "numero_candidato": "12345", "partido_eleitoral": "Partido ABC", "vinculo": [{"valor": "R$ 3.000,00", "data": "10/05/2022", "documento": "DOC123456", "recibo": "REC987654", "descricao": "Fornecimento de material gráfico para campanha", "especie": "Transferência eletrônica", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Fornecimento direto", "tipo": "Financeira"}, {"valor": "R$ 1.500,00", "data": "15/06/2022", "documento": "DOC654321", "recibo": "REC123456", "descricao": "Fornecimento de serviços de consultoria", "especie": "Depósito em espécie", "natureza": "Recursos próprios", "natureza_estimavel": "Não se aplica", "origem": "Pessoa <PERSON>í<PERSON>", "fonte": "Fornecimento direto", "tipo": "Financeira"}]}]}]}], "metadata": {"creationDate": "<PERSON><PERSON>, 22 Apr 2025 13:42:53 GMT", "lastModified": "<PERSON><PERSON>, 22 Apr 2025 13:42:53 GMT", "reportId": "dg6IzARA5F7tF1vcMDTt", "searchArgs": {"cpf": ["10268682461"]}}}]}, "lastModified": "<PERSON><PERSON>, 22 Apr 2025 13:42:53 GMT", "omittedNodes": [], "reportName": "CPF 10268682461", "reportStatus": "Complete", "reportType": "cpf", "requestDate": "<PERSON><PERSON>, 22 Apr 2025 13:33:12 GMT", "searchArgs": {"cpf": ["10268682461"]}}