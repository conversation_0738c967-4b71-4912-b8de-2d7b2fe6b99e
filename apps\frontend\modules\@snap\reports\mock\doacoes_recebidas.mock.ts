import { DoacaoRecebida, DoacaoRecebidaEmpresa, DoacaoRecebidaPessoa } from "../model/DoacoesRecebidas";

// Mock for a donation from a company
const doacaoRecebidaEmpresa1: DoacaoRecebidaEmpresa = {
    razao_social: {
        value: "Empresa ABC Ltda",
        label: "Razão Social",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "12.345.678/0001-90",
            label: "CNPJ",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        cnae: {
            value: "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
            label: "CNAE",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor: {
                    value: "R$ 5.000,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "15/08/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC123456",
                    label: "Documento",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC987654",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para campanha eleitoral",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos de pessoa jurídica",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }   
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        {
            value: {
                valor: {
                    value: "R$ 3.000,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "20/09/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC654321",
                    label: "Documento",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC123456",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para material de campanha",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Depósito em espécie",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos de pessoa jurídica",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Mock for another donation from a company
const doacaoRecebidaEmpresa2: DoacaoRecebidaEmpresa = {
    razao_social: {
        value: "XYZ Comércio e Serviços S.A.",
        label: "Razão Social",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "98.765.432/0001-10",
            label: "CNPJ",
            source: ["TSEDoacoes"],
            is_deleted: false
        },
        cnae: {
            value: "4751-2/01 - Comércio varejista especializado de equipamentos e suprimentos de informática",
            label: "CNAE",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor: {
                    value: "R$ 10.000,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "05/10/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC789012",
                    label: "Documento",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC345678",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para campanha eleitoral",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos de pessoa jurídica",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Mock for a donation from a person
const doacaoRecebidaPessoa1: DoacaoRecebidaPessoa = {
    nome_completo: {
        value: "Carlos Eduardo Santos",
        label: "Nome Completo",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cpf: {
            value: "123.456.789-00",
            label: "CPF",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor: {
                    value: "R$ 1.500,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "12/07/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC456789",
                    label: "Documento",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC567890",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para campanha eleitoral",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Física",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Mock for another donation from a person
const doacaoRecebidaPessoa2: DoacaoRecebidaPessoa = {
    nome_completo: {
        value: "Ana Paula Oliveira",
        label: "Nome Completo",
        source: ["TSEDoacoes"],
        is_deleted: false
    },
    detalhes: {
        cpf: {
            value: "987.654.321-00",
            label: "CPF",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    },
    doacoes: [
        {
            value: {
                valor:{
                    value: "R$ 800,00",
                    label: "Valor",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                data: {
                    value: "25/08/2022",
                    label: "Data",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC234567",
                    label: "Documento",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC678901",
                    label: "Recibo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                descricao: {
                    value: "Doação para material de campanha",
                    label: "Descrição",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                especie: {
                    value: "Depósito em espécie",
                    label: "Espécie",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Física",
                    label: "Origem",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                fonte: {
                    value: "Doação direta",
                    label: "Fonte",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEDoacoes"],
                    is_deleted: false
                }
            },
            label: "Doação",
            source: ["TSEDoacoes"],
            is_deleted: false
        }
    ]
};

// Export the complete mock
export const doacoesRecebidasMock: DoacaoRecebida[] = [
    doacaoRecebidaEmpresa1,
    doacaoRecebidaEmpresa2,
    doacaoRecebidaPessoa1,
    doacaoRecebidaPessoa2
];
