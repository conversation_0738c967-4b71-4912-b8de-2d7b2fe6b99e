import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  fetchUserData,
  postVerifier,
} from "~/services/gateways/user.gateway";
import type { EncryptedData, UserData } from "~/types/global";
import { useEncryption } from "./useEncryption";

/**
 * Custom hook for user CRUD operations using TanStack Query.
 * - `userQuery`: Fetches the current user data (cached indefinitely).
 * - `updateCreditsMutation`: A mutation to update the user's report_type_credits.
 *
 * The mutation automatically updates the cached user data on success.
 */
export const useUserCRUD = (sub?: string) => {
  const { encryptData } = useEncryption();
  const queryClient = useQueryClient();
  const queryKey = ["user"];
  const saltKey = ["user", "salt"];

  const userQueryUser = useQuery<UserData>({
    queryKey: queryKey,
    queryFn: fetchUserData,
    staleTime: Infinity,
    retry: false,
  });

  const addNewVerifierMutation = useMutation({
    mutationFn: async (data: string) => {
      try {
        const { data: encrypted } = await encryptData(data);
        await postVerifier(encrypted as EncryptedData);
        return true;
      } catch (error) {
        console.error("addNewVerifierMutation Error:", error);
        return false;
      }
    },
    onSuccess: (ok) => {
      if (ok) {
        invalidateUser();
        //toast.success("Senha criada com sucesso!")
      }
    },
    onError: (error) => {
      console.error("Error updating user:", error);
      toast.error("Erro ao salvar.", {
        description: "Erro ao tentar salvar senha",
      });
    },
  });

  const invalidateUser = async () => {
    await queryClient.invalidateQueries({
      queryKey: queryKey,
      exact: true,
    });
  };

  const invalidateSalt = async () => {
    await queryClient.invalidateQueries({
      queryKey: saltKey,
      exact: true,
    });
  };

  return { userQueryUser, addNewVerifierMutation };
};
