import { ValueWithSource } from "./ValueWithSource";

export interface _Endereco {
  logradouro: ValueWithSource;
  numero?: ValueWithSource;
  complemento?: ValueWithSource;
  bairro?: ValueWithSource;
  city?: ValueWithSource;
  area?: ValueWithSource;
  "cep ou zipcode"?: ValueWithSource;
  "area code"?: ValueWithSource;
  "telefone relacionado"?: ValueWithSource;
  cidade?: ValueWithSource;
  "estado ou regiao"?: ValueWithSource;
  "label default key"?: ValueWithSource;
}

export interface Endereco {
  detalhes: Array<ValueWithSource<_Endereco>>;
}
