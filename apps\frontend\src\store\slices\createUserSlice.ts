/* eslint-disable no-unused-vars */
import type { StateCreator } from "zustand";
import type { UserData } from "~/types/global";

export interface UserSlice {
  user: { details: UserData | null };
  salt: string | null;
  setUserDetails: (details: UserData) => void;
  clearUserDetails: () => void;
  setUserSalt: (salt: string) => void;
}

const createUserSlice: StateCreator<UserSlice> = (set) => ({
  salt: null,
  user: {
    details: null,
  },

  setUserDetails: (details: UserData) =>
    set(() => ({
      user: {
        details: details,
      },
    })),

  setUserSalt: (salt: string) => {
    set(() => ({
      salt: salt,
    }));
  },

  clearUserDetails: () =>
    set(() => ({
      salt: null,
      user: {
        details: null,
      },
    })),
});

export default createUserSlice;
