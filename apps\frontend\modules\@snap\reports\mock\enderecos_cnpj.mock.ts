import { <PERSON>ere<PERSON>, _Endereco } from "../model/Enderecos";

export const enderecoMockCNPJ: Endereco = {
  detalhes: [
    {
      value: {
        logradouro: {
          value: "AL DA SERRA",
          label: "Logradouro",
          source: ["SNAP"],
          is_deleted: false
        },
        numero: {
          value: "400",
          label: "Númer<PERSON>",
          source: ["SNAP"],
          is_deleted: false
        },
        complemento: {
          value: "3",
          label: "Complemento",
          source: ["SNAP"],
          is_deleted: false
        },
        bairro: {
          value: "VILA DA SERRA",
          label: "<PERSON>rro",
          source: ["SNAP"],
          is_deleted: false
        },
        area: {
          value: "MG",
          label: "Área",
          source: ["SNAP"],
          is_deleted: false
        },
        "cep ou zipcode": {
          value: "34000000",
          label: "CEP",
          source: ["SNAP"],
          is_deleted: false
        },
        "telefone relacionado": {
          value: "3132111863",
          label: "Telefone Relacionado",
          source: ["SNAP"],
          is_deleted: false
        },
        cidade: {
          value: "NOVA LIMA",
          label: "Cidade",
          source: ["SNAP"],
          is_deleted: false
        },
        "estado ou regiao": {
          value: "MG",
          label: "Estado ou Região",
          source: ["SNAP"],
          is_deleted: false
        },
      },
      label: "Endereço",
      source: ["SNAP"],
      is_deleted: false
    },
    {
      value: {
        logradouro: {
          value: "RUA SERGIPE",
          label: "Logradouro",
          source: ["SNAP"],
          is_deleted: false
        },
        numero: {
          value: "1014",
          label: "Número",
          source: ["SNAP"],
          is_deleted: false
        },
        complemento: {
          value: "SALA 502",
          label: "Complemento",
          source: ["SNAP"],
          is_deleted: false
        },
        bairro: {
          value: "SAVASSI",
          label: "Bairro",
          source: ["SNAP"],
          is_deleted: false
        },
        area: {
          value: "MG",
          label: "Área",
          source: ["SNAP"],
          is_deleted: false
        },
        "cep ou zipcode": {
          value: "30130174",
          label: "CEP",
          source: ["SNAP"],
          is_deleted: false
        },
        "telefone relacionado": {
          value: "3132862339",
          label: "Telefone Relacionado",
          source: ["SNAP"],
          is_deleted: false
        },
        cidade: {
          value: "BELO HORIZONTE",
          label: "Cidade",
          source: ["SNAP"],
          is_deleted: false
        },
        "estado ou regiao": {
          value: "MG",
          label: "Estado ou Região",
          source: ["SNAP"],
          is_deleted: false
        },
      },
      label: "Endereço",
      source: ["SNAP"],
      is_deleted: false
    }
  ]
};