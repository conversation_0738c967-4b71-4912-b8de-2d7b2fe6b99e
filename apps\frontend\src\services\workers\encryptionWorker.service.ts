export interface EncryptionRequest {
  data: any;
  action: "encrypt" | "decrypt";
  key?: <PERSON><PERSON>yBuffer;
}

export interface EncryptionResponse {
  result: any;
  error?: string;
}

export class EncryptionWorkerService {
  private worker: Worker | null = null;
  private isTerminated = false;
  private static instance: EncryptionWorkerService | null = null;
  private readonly TIMEOUT_DURATION = 30000;
  private requestCounter = 0;
  private pendingRequests: Map<
    number,
    { resolve: (value: any) => void; reject: (reason?: any) => void }
  > = new Map();

  private constructor() {
    if (typeof window === "undefined") {
      throw new Error(
        "Workers can only be instantiated in browser environment"
      );
    }
  }

  public static getInstance(): EncryptionWorkerService {
    if (!EncryptionWorkerService.instance) {
      EncryptionWorkerService.instance = new EncryptionWorkerService();
    }
    return EncryptionWorkerService.instance;
  }

  private initWorker(): void {
    console.log("Initializing worker...");
    try {
      if (!this.worker && !this.isTerminated) {
        if (typeof Worker === "undefined") {
          throw new Error("Web Workers are not supported in this environment");
        }

        this.worker = new Worker(
          new URL("../../workers/encryption.worker.ts?worker", import.meta.url),
          { type: "module" }
        );

        console.log("Worker created successfully");

        this.worker.onmessage = (
          event: MessageEvent<{
            id: number;
            cmd: string;
            result?: any;
            error?: string;
          }>
        ) => {
          const { id, cmd, result, error } = event.data;
          const pending = this.pendingRequests.get(id);
          if (!pending) return;

          this.pendingRequests.delete(id);
          if (error) {
            pending.reject(new Error(error));
          } else {
            pending.resolve(result);
          }
        };

        this.worker.onerror = (error) => {
          console.error("Worker error:", error);
          this.terminateWorker();
        };

        this.worker.onmessageerror = (error) => {
          console.error("Worker message error:", error);
          this.terminateWorker();
        };
      }
    } catch (error) {
      console.error("Failed to initialize worker:", error);
      throw new Error(
        `Worker initialization failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  public async encrypt(data: any, key?: ArrayBuffer): Promise<any> {
    return this.processData({ data, action: "encrypt", key });
  }

  public async decrypt(data: any, key?: ArrayBuffer): Promise<any> {
    return this.processData({ data, action: "decrypt", key });
  }

  private async processData(request: EncryptionRequest): Promise<any> {
    try {
      this.initWorker();
      if (!this.worker) {
        throw new Error("Encryption worker failed to initialize");
      }
      const requestId = this.requestCounter++;
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          this.pendingRequests.delete(requestId);
          reject(new Error(`${request.action} operation timed out`));
        }, this.TIMEOUT_DURATION);

        this.pendingRequests.set(requestId, {
          resolve: (value) => {
            clearTimeout(timeoutId);
            resolve(value);
          },
          reject: (error) => {
            clearTimeout(timeoutId);
            reject(error);
          },
        });

        this.worker?.postMessage({
          cmd: request.action,
          id: requestId,
          payload: {
            data: request.data,
            key: request.key,
          },
        });
      });
    } catch (error) {
      this.terminateWorker();
      throw error;
    }
  }

  private terminateWorker(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
      this.isTerminated = true;
    }
  }

  public dispose(): void {
    this.terminateWorker();
    EncryptionWorkerService.instance = null;
  }

  public isWorkerActive(): boolean {
    return !!this.worker && !this.isTerminated;
  }

  public resetWorker(): void {
    this.terminateWorker();
    this.isTerminated = false;
    this.initWorker();
  }
}

// Export singleton instance
export const encryptionWorker = EncryptionWorkerService.getInstance();
