import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { DadosPessoais } from "../../model/DadosPessoais";
import { ValueWithSource } from "../../model/ValueWithSource";
import { parseValue, translatePropToLabel } from "../../helpers";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export class RenderDadosPessoas
  implements RenderStrategy<DadosPessoais> {
  validateKeys = (keys: Array<keyof DadosPessoais>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (dadosPessoais?: DadosPessoais) => React.ReactElement | null
  > = {
      detalhes: (dadosPessoais?: DadosPessoais) => {
        if (!dadosPessoais?.detalhes) return null;

        return (
          <CustomGridContainer cols={3} columnFirst>
            {Object.entries(dadosPessoais.detalhes).map(([key, value], index) => {
              // Cast value to ValueWithSource type
              const valorComFonte = value as ValueWithSource<string>;
              return (
                <GridItem key={`detalhes-${index}-${key}`} cols={1} className="group">
                  <CustomReadOnlyInputField
                    label={`${(translatePropToLabel(valorComFonte.label)).toUpperCase()}`}
                    value={parseValue(String(valorComFonte.value))}
                    tooltip={renderSourceTooltip(valorComFonte.source)}
                  />
                </GridItem>
              );
            })}
          </CustomGridContainer>
        );
      },
    };

  render = (dadosPessoais: DadosPessoais): React.ReactElement[] => {
    const keys = Object.keys(dadosPessoais) as Array<keyof DadosPessoais>;

    if (!this.validateKeys(keys)) {
      console.log(keys);
      //throw new Error("Chaves inválidas");
    }

    return keys
      .map((chave) => this.formatByKey[chave]?.(dadosPessoais))
      .filter((el): el is React.ReactElement => el !== null);
  };
}
