import { create } from "zustand";
import { describe, expect, test, beforeEach } from "vitest";
import createUserSlice, { UserSlice } from "~/store/slices/createUserSlice";
import type { UserData } from "~/types/global";

// A stub factory for UserData
const makeUserDataStub = (overrides: Partial<UserData> = {}): UserData => ({
  sub: "fake-id-" + Math.random().toString(36).slice(2, 11),
  name: "<PERSON>",
  email: "<EMAIL>",
  image: "default.png",
  report_type_credits: {
    cpf: 10,
    cnpj: 10,
    telefone: 10,
    email: 10,
  },
  total_credits: 40,
  ...overrides,
});

// Utility to create a fresh store for each test
const createTestStore = () => {
  return create<UserSlice>((set, get, api) => ({
    ...createUserSlice(set, get, api),
  }));
};

describe("createUserSlice", () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  test("initial user details should be null", () => {
    expect(store.getState().user.details).toBeNull();
  });

  test("setUserDetails updates user details", () => {
    // Arrange: Create a stub user
    const userData = makeUserDataStub();
    // Act: Set user details
    store.getState().setUserDetails(userData);
    // Assert: The state's user details should match the stub
    expect(store.getState().user.details).toEqual(userData);
  });

  test("clearUserDetails resets user details to null", () => {
    // Arrange: Set user details first
    const userData = makeUserDataStub({ name: "Jane Doe" });
    store.getState().setUserDetails(userData);
    expect(store.getState().user.details).toEqual(userData);

    // Act: Clear user details
    store.getState().clearUserDetails();
    // Assert: The state's user details should now be null
    expect(store.getState().user.details).toBeNull();
  });
});
