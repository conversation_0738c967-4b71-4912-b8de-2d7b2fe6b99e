import { Settings } from "lucide-react";
import { useEffect } from "react";
import { Navigate, useOutletContext, useParams } from "react-router";
import { OutletContextType } from "~/types/global";
import { useUserData } from "~/store/userStore";
import UserAccountContainer from "./UserAccountContainer";

const AccountContainer = () => {
  const { setBreadcrumbs } = useOutletContext<OutletContextType>();
  const userData = useUserData();

  useEffect(() => {
    setBreadcrumbs([
      {
        title: "Configurações",
        icon: <Settings size={16} className="text-primary" />,
      },
    ]);

    return () => {
      setBreadcrumbs([]);
    };
  }, []);

  if (!userData) {
    return <Navigate to="/login" replace />;
  }

  return (
    <UserAccountContainer />
  )
}

export default AccountContainer