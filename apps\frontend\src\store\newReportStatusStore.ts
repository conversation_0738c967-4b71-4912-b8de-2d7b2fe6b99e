import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  ReportData,
  ReportStatusProps,
  NewReportResponse,
} from "~/types/global";
import { REPORT_CONSTANTS } from "~/helpers/constants";

interface ReportActions {
  setPendingList: (list: NewReportResponse[]) => void;
  clearPendingList: () => void;
}

interface ReportState {
  pendingList: NewReportResponse[];
  actions: ReportActions;
}

const useNewReportStore = create<ReportState>()(
  devtools(
    persist(
      (set) => ({
        pendingList: [],
        actions: {
          setPendingList: (list: NewReportResponse[]) => {
            const pending = list.filter((report) => {
              const statusObj = report[
                REPORT_CONSTANTS.new_report.report_status
              ] as ReportStatusProps;
              return (
                statusObj[REPORT_CONSTANTS.new_report.status_report] ===
                REPORT_CONSTANTS.status.pending
              );
            });
            set({ pendingList: pending });
          },
          clearPendingList: () => {
            set(() => ({ pendingList: [] }));
          },
        },
      }),
      {
        name: "new-report-status",
        partialize: (state) => ({
          pendingList: state.pendingList,
        }),
      }
    )
  )
);

export const useNewPendingReports = () =>
  useNewReportStore((state) => state.pendingList);
export const useNewPendingReportsActions = () =>
  useNewReportStore((state) => state.actions);
