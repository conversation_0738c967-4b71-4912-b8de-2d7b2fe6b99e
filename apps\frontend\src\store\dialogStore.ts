import { create } from "zustand";

interface DialogContentProps {
  title?: string;
  icon?: React.ReactNode;
  description?: string;
  content?: React.ReactNode;
  footer?: React.ReactNode;
}

interface DialogState {
  isOpen: boolean;
  dialogProps: DialogContentProps | null;
  actions: {
    openDialog: (dialogProps: DialogContentProps) => void;
    closeDialog: () => void;
  };
}

const useDialogStore = create<DialogState>((set) => ({
  isOpen: false,
  dialogProps: null,
  actions: {
    openDialog: (dialogProps: DialogContentProps) =>
      set({ isOpen: true, dialogProps }),
    closeDialog: () => set({ isOpen: false, dialogProps: null }),
  },
}));

export const useIsDialogIsOpen = () =>  useDialogStore(state=> state.isOpen)
export const useDialogProps = () => useDialogStore((state) => state.dialogProps);
export const useDialogActions = () => useDialogStore((state) => state.actions);