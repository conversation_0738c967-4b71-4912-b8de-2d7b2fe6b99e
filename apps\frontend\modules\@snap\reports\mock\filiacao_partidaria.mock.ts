import { FiliacaoPartidaria, <PERSON><PERSON><PERSON> } from "../model/FiliacaoPartidaria";

const filiacaoPartidaria1: FiliacaoPartidaria = {
    sigla: {
        value: "PT",
        label: "Sigla",
        source: ["TSEFiliacaoPartidaria"],
        is_deleted: false
    },
    detalhes: {
        uf: {
            value: "SP",
            label: "Estado",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        },
        "data consulta": {
            value: "15/05/2023",
            label: "Data da Consulta",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        }
    },
    vinculos: [
        {
            "data registro": {
                value: "10/03/2010",
                label: "Data Registro",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            situacao: {
                value: "Regular",
                label: "Situação",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            tipo: {
                value: "Filia<PERSON>",
                label: "Tipo",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            }
        }
    ]
};

const filiacaoPartidaria2: FiliacaoPartidaria = {
    sigla: {
        value: "PSDB",
        label: "Sigla",
        source: ["TSEFiliacaoPartidaria"],
        is_deleted: false
    },
    detalhes: {
        uf: {
            value: "RJ",
            label: "Estado",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        },
        "data consulta": {
            value: "15/05/2023",
            label: "Data da Consulta",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        }
    },
    vinculos: [
        {
            "data registro": {
                value: "05/06/2005",
                label: "Data Registro",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            situacao: {
                value: "Desfiliado",
                label: "Situação",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            tipo: {
                value: "Filiação",
                label: "Tipo",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            "data desfiliacao": {
                value: "12/08/2015",
                label: "Data Desfiliação",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            }
        }
    ]
};

const filiacaoPartidaria3: FiliacaoPartidaria = {
    sigla: {
        value: "MDB",
        label: "Sigla",
        source: ["TSEFiliacaoPartidaria"],
        is_deleted: false
    },
    detalhes: {
        uf: {
            value: "MG",
            label: "Estado",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        },
        "data consulta": {
            value: "15/05/2023",
            label: "Data da Consulta",
            source: ["TSEFiliacaoPartidaria"],
            is_deleted: false
        }
    },
    vinculos: [
        {
            "data registro": {
                value: "20/01/2018",
                label: "Data Registro",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            situacao: {
                value: "Cancelado",
                label: "Situação",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            tipo: {
                value: "Filiação",
                label: "Tipo",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            "data cancelamento": {
                value: "15/03/2020",
                label: "Data Cancelamento",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            },
            "motivo cancelamento": {
                value: "Duplicidade de filiação",
                label: "Motivo Cancelamento",
                source: ["TSEFiliacaoPartidaria"],
                is_deleted: false
            }
        }
    ]
};

export const filiacaoPartidariaMock: FiliacaoPartidaria[] = [
    filiacaoPartidaria1,
    filiacaoPartidaria2,
    filiacaoPartidaria3
];
