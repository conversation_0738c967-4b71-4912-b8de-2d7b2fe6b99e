import { PossivelPessoaRelacionada } from "../model/PossiveisPessoasRelacionadas";

export const possiveisPessoasRelacionadasMock: PossivelPessoaRelacionada[] = [
    {
        "nome_completo": {
            "value": "JOAO AVERSA",
            "label": "Nome Completo",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        "detalhes": [
            {
                "value": {
                    "data nascimento": {
                        "value": "26/12/1984",
                        "label": "Data de Nascimento",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "Detalhes",
                "source": ["IRBISLuna"],
                "is_deleted": false
            }
        ],
        "telefones": [
            {
                "value": {
                    "phone number": {
                        "value": "5521999891661",
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "Telefone",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            {
                "value": {
                    "phone number": {
                        "value": "5521985592439",
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "Telefone",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            {
                "value": {
                    "phone number": {
                        "value": "5521999891661",
                        "label": "Telefone",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "Telefone",
                "source": ["IRBISLuna"],
                "is_deleted": false
            }
        ],
        "imagens": [
            {
                "value": {
                    "url": {
                        "value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720",
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "URL",
                "source": ["SNAP"],
                "is_deleted": false
            },
            {
                "value": {
                    "url": {
                        "value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720",
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "URL",
                "source": ["SNAP"],
                "is_deleted": false
            },
            {
                "value": {
                    "url": {
                        "value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720",
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "URL",
                "source": ["SNAP"],
                "is_deleted": false
            },
            {
                "value": {
                    "url": {
                        "value": "https://lh3.googleusercontent.com/a-/AAuE7mBVcOo3EVpk5Md0fRxg7iDGQ52NpXsDwyFILpsBfw=s720",
                        "label": "URL",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                },
                "label": "URL",
                "source": ["SNAP"],
                "is_deleted": false
            },
        ],
        "enderecos": [
            {
                "value": {
                    "logradouro": {
                        "value": "AVENIDA PAULISTA",
                        "label": "Logradouro",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "numero": {
                        "value": "1000",
                        "label": "Número",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "bairro": {
                        "value": "BELA VISTA",
                        "label": "Bairro",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "city": {
                        "value": "SÃO PAULO",
                        "label": "Cidade",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "area": {
                        "value": "SP",
                        "label": "Área",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "cep ou zipcode": {
                        "value": "01310100",
                        "label": "CEP",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                },
                "label": "Endereço",
                "source": ["IRBISLuna"],
                "is_deleted": false
            }
        ],
        "redes_sociais": {
            "facebook": {
                "value": {
                    "link": {
                        "value": "http://www.facebook.com/joao.aversa.3",
                        "label": "Link",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "nome de usuario": {
                        "value": "joao.aversa.3",
                        "label": "Nome de Usuário",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "id": {
                        "value": "100001296267978",
                        "label": "ID",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                },
                "label": "Facebook",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
            "twitter": {
                "value": {
                    "link": {
                        "value": "http://www.twitter.com/joao_aversa",
                        "label": "Link",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    },
                    "nome de usuario": {
                        "value": "joao_aversa",
                        "label": "Nome de Usuário",
                        "source": ["IRBISLuna"],
                        "is_deleted": false
                    }
                },
                "label": "Twitter",
                "source": ["IRBISLuna"],
                "is_deleted": false
            },
        }
    }
]