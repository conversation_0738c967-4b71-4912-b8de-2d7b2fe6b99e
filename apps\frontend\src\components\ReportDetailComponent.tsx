import React from "react";
import {
  Accordion,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
  AccordionContent,
} from "~/components/ui/accordion";
import { Badge } from "./ui/badge";
import { getStrategyMap } from "root/domain/models/strategy/ReportStrategyFactory";
import { useReportSections } from "~/store/reportDetailStore";

const ReportDetailComponent: React.FC = () => {
  const reportSections = useReportSections();
  const strategyMap = getStrategyMap();

  return (
    <Accordion type="multiple" className="">
      {reportSections?.map((section, index) => {
        const printer = strategyMap[section.title];
        if (!printer) {
          console.warn(`No strategy for ${section.title}`);
          return null;
        }

        return (
          <AccordionItem key={index} value={`section-${index}`}>
            <AccordionTrigger className="bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline cursor-pointer">
              <div className="flex items-center justify-start gap-4">
                <h3 className="font-mono text-lg uppercase no-underline">
                  {section.title}
                </h3>
                <Badge
                  variant="secondary"
                  className="rounded-2xl px-4 py-0.5 bg-gray-400"
                >
                  {section.data_count}
                </Badge>
              </div>
            </AccordionTrigger>

            <AccordionContent className="bg-card px-5">
              <AccordionContent>
                {section.data.map((item: any, index) => (
                  <div
                    key={index}
                    className="pt-5"
                  >
                    {printer
                      .render(item)
                      .map((el: React.ReactNode, i: number) => (
                        <div key={i}>{el}</div>
                      ))}
                  </div>
                ))}
              </AccordionContent>
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};

export default ReportDetailComponent;