import { useEffect, useRef } from "react";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import ReportsList from "~/containers/report/ReportsList";
import { useDialogActions } from "~/store/dialogStore";
import { CreateReportDialog } from "~/containers/report/CreateReportDialog";
import { Folder, KeyRound } from "lucide-react";
import { toast } from "sonner";
import { SecretKeyDialog } from "../SecretKeyDialog";
import MissingKey from "~/components/MissingKey";
import { useModalControl } from "@snap/design-system";
import { useReportList } from "~/store/reportListStore";
import { useUserData, useUserIsVerified } from "~/store/userStore";

const ReportListContainer = () => {
  const { open } = useModalControl();
  const { openDialog } = useDialogActions();
  const { reportListQuery } = useReportCRUD();
  const isVerified = useUserIsVerified();
  const { data: reportDataResponse, isFetched: isReportsFetched } =
    reportListQuery;
  const reportList = useReportList();
  const hasShownToastKey = "reports_list_toast_shown";
  const firstLoadRef = useRef(
    sessionStorage.getItem(hasShownToastKey) !== "true"
  );
  const userData = useUserData();

  useEffect(() => {
    if (!isVerified && userData) {
      handleOpenUserSecretKeyDialog();
    }
  }, [isVerified, userData]);

  useEffect(() => {
    if (!reportDataResponse) return;

    const { isScheduledRefetch } = reportDataResponse;

    if (firstLoadRef.current || isScheduledRefetch) {
      toast.success("Lista atualizada com sucesso!");
      firstLoadRef.current = false;
      sessionStorage.setItem(hasShownToastKey, "true");
    }
  }, [reportDataResponse]);

  const handleOpenUserSecretKeyDialog = () => {
    
    console.log("handleOpenUserSecretKeyDialog: ", "verifier: ", userData?.verifier );
    open({
      modal: () => ({
        title: userData?.verifier ? "INSERIR SENHA" : "CRIAR SENHA",
        icon: <KeyRound />,
        content: <SecretKeyDialog.Content />,
        footer: <SecretKeyDialog.Footer onOpen={handleOpenUserSecretKeyDialog}/>,
      }),
      config: {
        content: {
          className: "max-w-xl",
        }
      },
    });
  };

  function handleOpenDialog() {
    // TODO - trocar pelo modal do DS quando corrigir bug que quebra altura do componente
    openDialog({
      title: "Criar Novo Relatório ou Pasta",
      icon: <Folder />,
      content: <CreateReportDialog.Content />,
      footer: <CreateReportDialog.Footer />,
    });

    /*  open({
      modal: () => ({
        title: "Criar Novo Relatório ou Pasta",
        icon: <Folder />,
        content: <CreateReportDialog.Content />,
        footer: <CreateReportDialog.Footer />,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    }); */
  }
  const renderListContent = () => {
    if (!isVerified) {
      return <MissingKey onInsertKey={handleOpenUserSecretKeyDialog} />;
    }

    return (
      <ReportsList
        list={reportList}
        onNewReport={handleOpenDialog}
        isFetched={isReportsFetched}
      />
    );
  };

  return renderListContent();
};

export default ReportListContainer;
