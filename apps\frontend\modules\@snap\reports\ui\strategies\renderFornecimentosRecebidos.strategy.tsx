import React from "react";
import { FornecimentoRecebido } from "../../model/FornecimentosRecebidos";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel, parseValue } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderFornecimentosRecebidos implements RenderStrategy<FornecimentoRecebido> {

    validateKeys = (keys: Array<keyof FornecimentoRecebido>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatBy<PERSON>ey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the fornecimentos information for a FornecimentoRecebido
     * @param fornecimentoRecebido The FornecimentoRecebido object
     * @returns React element or null if no data
     */
    renderFornecimentos = (fornecimentoRecebido?: FornecimentoRecebido): React.ReactElement | null => {
        return renderValidArray('vinculos', fornecimentoRecebido || {});
    };

    formatByKey: Record<
        string,
        (fornecimentoRecebido?: FornecimentoRecebido) => React.ReactElement | null
    > = {
            candidato: (fornecimentoRecebido?: FornecimentoRecebido) => {
                if (!fornecimentoRecebido?.candidato) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(fornecimentoRecebido.candidato.label || "Candidato").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(fornecimentoRecebido.candidato.value)}
                                tooltip={renderSourceTooltip(fornecimentoRecebido.candidato.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (fornecimentoRecebido?: FornecimentoRecebido) => {
                if (!fornecimentoRecebido?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(fornecimentoRecebido.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={parseValue(String(value.value))}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            vinculos: (fornecimentoRecebido?: FornecimentoRecebido) => {
                return this.renderFornecimentos(fornecimentoRecebido);
            }
        };

    render = (fornecimentoRecebido: FornecimentoRecebido): React.ReactElement[] => {
        const keys = Object.keys(fornecimentoRecebido) as Array<keyof FornecimentoRecebido>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof FornecimentoRecebido> = [
            'candidato',
            'detalhes',
            'vinculos'
        ];

        // Filter the keys to only include those that exist in the fornecimentoRecebido object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(fornecimentoRecebido);
                }

                // Check if the value is an array with the expected structure
                const value = fornecimentoRecebido[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, fornecimentoRecebido);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
