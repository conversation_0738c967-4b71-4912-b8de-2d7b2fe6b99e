import React from 'react'
import { Input, Textarea } from '@snap/design-system'
import { ReportsCustomLabel, TooltipProps } from './ReportsCustomLabel'

interface InputFieldProps {
  label?: string
  value: string | number
  icon?: React.ReactNode
  element?: 'input' | 'textarea'
  tooltip?: TooltipProps
  colorClass?: string
  className?: string
  labelTextClass?: string
}

export const CustomReadOnlyInputField: React.FC<InputFieldProps> = ({
  label,
  value,
  icon = undefined,
  element = 'input',
  tooltip = undefined,
  colorClass = 'bg-border',
  className = '',
  labelTextClass = '',
}) => {
  return (
    <div className="space-y-1">
      {label && <ReportsCustomLabel label={label} icon={icon} tooltip={tooltip} colorClass={colorClass} labelTextClass={labelTextClass} />}

      {
        value !== '' && (element === 'textarea' ? (
          <Textarea
            onFocus={(e) => e.target.select()}
            value={String(value)}
            readOnly
            className="rounded-none border-0 w-full border-b border-dashed border-neutral-400"
          />
        ) : (
          <Input
            onFocus={(e) => e.target.select()}
            value={String(value)}
            readOnly
            className={`rounded-none border-0 w-full border-b-2 border-dashed pl-0 ${className}`}
          />
        ))
      }
    </div>
  )
}
