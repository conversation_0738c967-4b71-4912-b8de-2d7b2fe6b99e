import React from "react";
import { GridContainer } from "@snap/design-system";
import { useReportMode } from "../../context/ReportContext";

interface GridContainerProps {
  children: React.ReactNode;
  gap?: "none" | "sm" | "md" | "lg";
  cols?: 1 | 2 | 3;
  className?: string;
  columnFirst?: boolean;
}

interface CustomGridContainerProps extends GridContainerProps {
}

export const CustomGridContainer: React.FC<CustomGridContainerProps> = ({
  cols = 3,
  ...restProps
}) => {
  const mode = useReportMode();
  const effectiveCols = mode === "trash" ? 1 : cols;

  return (
    <GridContainer
      cols={effectiveCols}
      {...restProps}
    />
  );
};
