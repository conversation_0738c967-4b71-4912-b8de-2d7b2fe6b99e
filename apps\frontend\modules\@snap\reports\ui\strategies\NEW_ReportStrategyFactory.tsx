import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
// Hooks para renderizar as seções
import { useRenderDadosPessoasArray } from "./NEW_renderDadosPessoaisArray.strategy";
import { useRenderEmailsArray } from "./NEW_renderEmailsArray.strategy";
import { useRenderEnderecosArray } from "./NEW_renderEnderecosArray.strategy";
import { useRenderDiariosOficiaisCPFArray } from "./NEW_renderDiariosOficiaisCPFArrayFixed.strategy";
import { useRenderTelefonesArray } from "./NEW_renderTelefones.strategy";

export const useNewStrategyMap = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // CPF
  const cpfStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoasArray(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmailsArray(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecosArray(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cpf]: useRenderDiariosOficiaisCPFArray(REPORT_SECTIONS.diarios_oficiais_cpf)
  };

  const cnpjStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_empresa]: useRenderDadosPessoasArray(REPORT_SECTIONS.dados_empresa),
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmailsArray(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecosArray(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cnpj]: useRenderDiariosOficiaisCPFArray(REPORT_SECTIONS.diarios_oficiais_cpf)
  };

  switch (reportType) {
    case "cnpj":
      return cnpjStrategyMap;
    case "telefone":
      return cpfStrategyMap;
    case "cpf":
    default:
      return cpfStrategyMap;
  }
};