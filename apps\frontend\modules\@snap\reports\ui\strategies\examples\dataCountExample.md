# Data Count Update Implementation Example

## How to implement data_count updates for other sections

### Example for a hypothetical "Emails" section:

```typescript
// In your render strategy file (e.g., NEW_renderEmailsArray.strategy.tsx)

// 1. Create a data count calculation function specific to your section
const calculateDataCount = (section: any): number => {
  if (!Array.isArray(section.data)) return 0;
  
  return section.data.reduce((count: number, entry: any) => {
    if (!entry.emails) return count;
    
    // Count non-deleted emails
    const nonDeletedEmails = entry.emails.filter(
      (email: any) => email.is_deleted !== true
    ).length;
    
    return count + nonDeletedEmails;
  }, 0);
};

// 2. Use it in your updateSectionEntries call
const onToggleField = (emailIndex: number) => {
  actions.updateSectionEntries!(
    sectionTitle,
    (entry: any, i: number) => {
      if (i === entryIndex) {
        const email = entry.emails?.[emailIndex];
        if (email) email.is_deleted = !email.is_deleted;
      }
    },
    testEntryDeleted,
    testSectionDeleted,
    calculateDataCount  // <- Add your calculation function here
  );
};
```

### Example for "Endereços" section:

```typescript
const calculateDataCount = (section: any): number => {
  if (!Array.isArray(section.data)) return 0;
  
  return section.data.reduce((count: number, entry: any) => {
    if (!entry.detalhes) return count;
    
    // Count non-deleted address fields across all address blocks
    const nonDeletedFields = entry.detalhes.reduce((fieldCount: number, detalhe: any) => {
      if (!detalhe.value) return fieldCount;
      
      const activeFields = Object.values(detalhe.value).filter(
        (field: any) => field.is_deleted !== true
      ).length;
      
      return fieldCount + activeFields;
    }, 0);
    
    return count + nonDeletedFields;
  }, 0);
};
```

## Key Points:

1. **Section-specific logic**: Each section defines how to count its "active" items
2. **Flexible counting**: You can count individual fields, entire objects, or any combination
3. **Backward compatibility**: Existing sections continue to work without changes
4. **Performance**: Calculation only happens when items are toggled, not on every render

## For "Dados Pessoais" specifically:

The implementation counts each non-deleted field in the `detalhes` object. So if a person has:
- Nome Completo (not deleted)
- CPF (not deleted) 
- Idade (deleted)
- Sexo (not deleted)

The data_count would be 3 (counting the non-deleted fields).
