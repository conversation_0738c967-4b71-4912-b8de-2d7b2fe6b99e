import { FornecimentoRecebido } from "../model/FornecimentosRecebidos";

// Mock for a fornecimento to a candidate
const fornecimentoRecebido1: FornecimentoRecebido = {
    candidato: {
        value: "<PERSON>",
        label: "Candida<PERSON>",
        source: ["TSEFornecimento"],
        is_deleted: false
    },
    detalhes: {
        cpf: {
            value: "123.456.789-00",
            label: "CPF",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        "ano da eleição": {
            value: "2022",
            label: "Ano Da Eleição",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        "cargo eleitoral": {
            value: "Vereador",
            label: "Cargo Eleitoral",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        "unidade eleitoral": {
            value: "São Paulo / SP",
            label: "Unidade Eleitoral",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        "número do candidato": {
            value: "12345",
            label: "Número",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        "partido eleitoral": {
            value: "Partido ABC",
            label: "Partido",
            source: ["TSEFornecimento"],
            is_deleted: false
        }
    },
    fornecimentos: [
        {
            value: {
                valor: {
                    value: "R$ 3.000,00",
                    label: "Valor",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                data: {
                    value: "10/05/2022",
                    label: "Data",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC123456",
                    label: "Documento",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC987654",
                    label: "Recibo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                descricao: {
                    value: "Fornecimento de material gráfico para campanha",
                    label: "Descrição",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                especie: {
                    value: "Transferência eletrônica",
                    label: "Espécie",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                fonte: {
                    value: "Fornecimento direto",
                    label: "Fonte",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                }
            },
            label: "Fornecimento",
            source: ["TSEFornecimento"],
            is_deleted: false
        },
        {
            value: {
                valor: {
                    value: "R$ 1.500,00",
                    label: "Valor",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                data: {
                    value: "15/06/2022",
                    label: "Data",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                documento: {
                    value: "DOC654321",
                    label: "Documento",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                recibo: {
                    value: "REC123456",
                    label: "Recibo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                descricao: {
                    value: "Fornecimento de serviços de consultoria",
                    label: "Descrição",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                especie: {
                    value: "Depósito em espécie",
                    label: "Espécie",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                natureza: {
                    value: "Recursos próprios",
                    label: "Natureza",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                "natureza estimavel": {
                    value: "Não se aplica",
                    label: "Natureza Estimável",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                origem: {
                    value: "Pessoa Jurídica",
                    label: "Origem",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                fonte: {
                    value: "Fornecimento direto",
                    label: "Fonte",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                },
                Tipo: {
                    value: "Financeira",
                    label: "Tipo",
                    source: ["TSEFornecimento"],
                    is_deleted: false
                }
            },
            label: "Fornecimento",
            source: ["TSEFornecimento"],
            is_deleted: false
        }
    ]
};

// Export the complete mock
export const fornecimentosRecebidosMock: FornecimentoRecebido[] = [
    fornecimentoRecebido1
];
