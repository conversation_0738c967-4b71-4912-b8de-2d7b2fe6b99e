/* eslint-disable no-unused-vars */
import type { StateCreator } from "zustand";
import type { ReportType, ReportListItem, ReportDetails } from "~/types/global";

export interface ReportSlice {
  reports: {
    list: ReportListItem[];
    details: ReportDetails;
    reportType: ReportType;
    inputValue: string;
  };
  setReportList: (list: ReportListItem[]) => void;
  addReport: (report: ReportListItem) => void;
  removeReport: (id: string) => void;
  setReportDetails: (details: ReportDetails) => void;
  setReportType: (type: string) => void;
  setInputValue: (value: string) => void;
}

const createReportSlice: StateCreator<ReportSlice> = (set) => ({
  reports: {
    list: [],
    details: null,
    reportType: null,
    inputValue: "",
  },

  setReportList: (list) =>
    set((state) => ({
      reports: { ...state.reports, list },
    })),

  addReport: (report) =>
    set((state) => ({
      reports: {
        ...state.reports,
        list: [...state.reports.list, report],
      },
    })),

  removeReport: (id) =>
    set((state) => ({
      reports: {
        ...state.reports,
        list: state.reports.list.filter((r) => r.id !== id),
      },
    })),

  setReportDetails: (details) =>
    set((state) => ({
      reports: { ...state.reports, details },
    })),

  setReportType: (type: string) =>
    set((state) => ({
      reports: { ...state.reports, reportType: type as ReportType },
    })),

  setInputValue: (value) =>
    set((state) => ({
      reports: { ...state.reports, inputValue: value },
    })),
});

export default createReportSlice;
