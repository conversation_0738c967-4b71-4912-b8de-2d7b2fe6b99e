import express from "express";
import cors from "cors";
import cookieParser from 'cookie-parser';
import reportsRouter from "./routes/reports.routes";
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import dotenv from 'dotenv';
import path from 'path';
import { errorHandler } from './middlewares/error.middleware';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3001;

// Add cookie parser
app.use(cookieParser());

// CORS middleware
app.use(cors({
  origin:[ process.env.FRONTEND_URL || 'http://localhost:3000', "http://***************:80", "http://***************:3000"],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

app.use(express.json());

app.use("/api/reports", reportsRouter);
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);

// Error handling middleware
app.use(errorHandler);

app.listen(PORT, () => {
  console.log(`BFF server listening on port ${PORT}`);
});
