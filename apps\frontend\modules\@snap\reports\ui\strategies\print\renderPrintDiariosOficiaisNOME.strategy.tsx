import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintDiariosOficiaisNOMEProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      local?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      ["texto correspondente"]?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: {
        [key: string]: {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintDiariosOficiaisNOME: React.FC<RenderPrintDiariosOficiaisNOMEProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((diario, diarioIndex) => (
        <View key={`diario-${diarioIndex}`} style={styles.diarioContainer}>
          {/* Local */}
          {diario.local && !diario.local.is_deleted && (
            <View style={styles.localContainer}>
              <View style={styles.localLabelContainer}>
                <Text style={styles.localLabel}>
                  {(diario.local.label || "Local").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {diario.local.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.localValue}>{diario.local.value}</Text>
            </View>
          )}

          {/* Texto Correspondente */}
          {diario["texto correspondente"] && !diario["texto correspondente"].is_deleted && (
            <View style={styles.textoContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>
                  {translatePropToLabel(diario["texto correspondente"].label || "Descrição").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {diario["texto correspondente"].source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <View style={styles.textoContent}>
                <Text style={styles.textoText} wrap>
                  {diario["texto correspondente"].value}
                </Text>
              </View>
            </View>
          )}

          {/* Detalhes */}
          {diario.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(diario.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  localLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  localLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  localValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  diarioContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  localContainer: {
    marginBottom: 12,
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  textoContainer: {
    padding: 8,
    marginBottom: 12,
    borderRadius: 4,
    backgroundColor: '#F5F5F5',
  },
  textoContent: {
    paddingLeft: 8,
  },
  textoText: {
    fontSize: 9,
    lineHeight: 1.4,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});