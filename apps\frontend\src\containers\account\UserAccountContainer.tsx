import { useUserData } from "~/store/userStore";
import AccountUserProfile from "./AccountUserProfile";
import DefaultUserAccount from "./DefaultUserAccount";
import { USER_CONSTANTS } from "~/helpers/constants";
import TabContainerAccount from "./TabContainerAccount";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const UserAccountContainer = ({ }: UserConfigDialogContentProps) => {
  const userData = useUserData();

  const renderUserContent = () => {
   const profile = userData?.[USER_CONSTANTS.user_data.account_type as keyof typeof userData];
   const admin = USER_CONSTANTS.profile_types.administrador
   const investigador = USER_CONSTANTS.profile_types.investigador

    switch (profile) {
      case admin:
        return <TabContainerAccount />;
      case investigador:
        return <DefaultUserAccount />;
      default:
        return <DefaultUserAccount />;
    }
  };

  return (
    <div className="flex gap-8 px-8">
      {renderUserContent()}

      <div className="flex flex-col flex-1/4 max-w-sm">
        <AccountUserProfile />
      </div>
    </div>
  );
}

export default UserAccountContainer;