import { Telefone, _Telefone } from "../model/Telefones";

// Pensando que telefones podem trazer outras propriedades além do phone number futuramente.
export const telefonesMock: Telefone = {
    detalhes: [
        {
            value: {
                "phone number": {
                    value: "31991701966",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "31973113426",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "21976886755",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        },
        {
            value: {
                "phone number": {
                    value: "21988563455",
                    label: "Número do Telefone",
                    source: ["SintegraMA"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SintegraMA"],
            is_deleted: false
        },

    ]
};