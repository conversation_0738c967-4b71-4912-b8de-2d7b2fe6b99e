import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { GridContainer, GridItem } from "@snap/design-system";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { Parente } from "../../model/Parentes";
import { isValidArray, renderValidArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderParentes implements RenderStrategy<Parente> {

  validateKeys = (keys: Array<keyof Parente>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (parente?: Parente) => React.ReactElement | null
  > = {
      parentesco: (parente?: Parente) => {
        if (!parente?.parentesco) return null;

        return (

          <GridContainer cols={3}>
            <GridItem cols={1} className="mb-6 group">
              <CustomReadOnlyInputField
                label={(parente.parentesco.label || "Parentesco").toUpperCase()}
                colorClass="bg-primary"
                labelTextClass="text-accent"
                value={parseValue(parente.parentesco.value)}
                tooltip={renderSourceTooltip(parente.parentesco.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },
      detalhes: (parente?: Parente) => {
        if (!parente?.detalhes) return null;

        return (
          <GridContainer cols={2} columnFirst className="mb-6">
            {Object.entries(parente.detalhes).map(([key, value]) => (
              <GridItem key={key} cols={1} className="group">
                <CustomReadOnlyInputField
                  label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                  value={String(value.value)}
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  tooltip={renderSourceTooltip(value.source)}
                />
              </GridItem>
            ))}
          </GridContainer>
        );
      },
      pessoa: (parente?: Parente) => {
        return renderValidArray('pessoa', parente || {});
      },
      telefones: (parente?: Parente) => {
        return renderValidArray('telefones', parente || {}, ['operadora', 'whatsapp'], false /* hasLabel */);
      },
      enderecos: (parente?: Parente) => {
        return renderValidArray('enderecos', parente || {});
      },
    };

  render = (parente: Parente): React.ReactElement[] => {
    const keys = Object.keys(parente) as Array<keyof Parente>;

    if (!this.validateKeys(keys)) {
      console.log(keys);
      //throw new Error("Chaves inválidas");
    }

    const orderedKeys: Array<keyof Parente> = [
      'parentesco',
      'detalhes',
      'pessoa',
      'telefones',
      'enderecos'
    ];

    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => {
        // If the key is in formatByKey, use that formatter
        if (chave in this.formatByKey) {
          return this.formatByKey[chave]?.(parente);
        }

        // Check if the value is an array with the participant format
        const value = parente[chave];
        if (isValidArray(value)) {
          return renderValidArray(chave as string, parente);
        }

        // Otherwise return null
        return null;
      })
      .filter((el): el is React.ReactElement => el !== null);
  };
}
