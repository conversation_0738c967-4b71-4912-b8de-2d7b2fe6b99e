import React from "react";
import { RecursoPublico } from "../../model/RecursosPublicos";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export class RenderRecursosPublicos implements RenderStrategy<RecursoPublico> {

    validateKeys = (keys: Array<keyof RecursoPublico>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    formatByKey: Record<
        string,
        (recursosPublicos?: RecursoPublico) => React.ReactElement | null
    > = {
            orgao: (recursosPublicos?: RecursoPublico) => {
                const item = recursosPublicos?.orgao;
                if (!item) return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6">
                            <CustomReadOnlyInputField
                                label={"Nome do Orgão".toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={"Não Informado"}
                            />
                        </GridItem>
                    </CustomGridContainer>
                )

                const itemLabel = !recursosPublicos?.orgao?.label ? "Nome do Orgão" : recursosPublicos.orgao.label;
                const itemValue = !recursosPublicos?.orgao?.value ? "Não Informado" : recursosPublicos?.orgao?.value;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(itemLabel).toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={itemValue}
                                tooltip={renderSourceTooltip(item.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (recursosPublicos?: RecursoPublico) => {
                if (!recursosPublicos?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="">
                        {Object.entries(recursosPublicos.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={String(value.value)}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            }
        }

    render = (recursosPublicos?: RecursoPublico): React.ReactElement[] => {
        if (!recursosPublicos) return [];

        const keys = Object.keys(recursosPublicos) as Array<keyof RecursoPublico>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof RecursoPublico> = [
            'orgao',
            'detalhes'
        ];

        // Filter the keys to only include those that exist in the recursosPublicos object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(recursosPublicos);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
