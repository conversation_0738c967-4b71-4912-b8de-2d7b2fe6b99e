CREATE SCHEMA IF NOT EXISTS keycloak AU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> keycloak;

GRANT ALL PRIVILEGES ON SCHEMA keycloak TO keycloak;

ALTER ROLE keycloak SET search_path TO keycloak, public;




CREATE TABLE IF NOT EXISTS public.report_types (
  "report_types_id" BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('UTC', now()),
  "credits_required" INTEGER NOT NULL,
  "type" CHARACTER VARYING NOT NULL,
  CONSTRAINT ReportTypes_pkey PRIMARY KEY ("report_types_id")
);

CREATE TABLE IF NOT EXISTS public.users (
  "user_id" UUID NOT NULL,
  "image" TEXT NULL,
  "name" TEXT NULL,
  "credits" BIGINT NULL,
  "email" TEXT NULL,
  "last_login" TIMES<PERSON>MP WITH TIME ZONE NULL,
  "report_types" <PERSON><PERSON><PERSON> NULL,
  "salt" TEXT NULL,
  "verifier" J<PERSON><PERSON> NULL,
  CONSTRAINT users_pkey PRIMARY KEY ("user_id")
);

CREATE TABLE IF NOT EXISTS public.user_reports (
  "user_id" UUID NOT NULL,
  "user_reports_id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "data" JSONB NULL,
  "created_at" TIMESTAMPTZ NULL,
  "modified_at" TIMESTAMPTZ NULL,
  "report_name" JSONB NULL,
  "report_status" JSONB NULL,
  "report_type" TEXT NULL,
  "subject_name" JSONB NULL,
  "subject_mother_name" JSONB NULL,
  "subject_age" INTEGER NULL,
  "subject_sex" JSONB NULL,
  "report_search_args" JSONB NULL,
  -- "requestDate" TIMESTAMP WITH TIME ZONE NULL,
  "omitted_notes" JSONB NULL,
  CONSTRAINT userReports_pkey PRIMARY KEY ("user_id", "user_reports_id"),
  CONSTRAINT userReports_userId_fkey FOREIGN KEY ("user_id") REFERENCES public.users ("user_id"),
  CONSTRAINT user_reports_id_unique UNIQUE ("user_reports_id")
);

CREATE INDEX idx_user_reports_subject_age ON user_reports(subject_age);
CREATE INDEX idx_user_reports_created_at ON user_reports(created_at);
CREATE INDEX idx_user_reports_modified_at ON user_reports(modified_at);
CREATE INDEX idx_user_reports_report_type ON user_reports(report_type);

CREATE TABLE IF NOT EXISTS public.user_columns_hmac (
  hmac TEXT NOT NULL,
  column_name TEXT NOT NULL,
  user_reports_id UUID NOT NULL REFERENCES public.user_reports(user_reports_id),
  PRIMARY KEY (hmac, column_name, user_reports_id)
);

CREATE INDEX idx_user_columns_hmac_hmac_column ON public.user_columns_hmac(hmac, column_name);

