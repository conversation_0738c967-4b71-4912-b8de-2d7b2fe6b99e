import React from "react";
import { Imagem } from "../../model/Imagens";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { ReadOnlyInputField } from "@snap/design-system";
import { isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderImagens implements RenderStrategy<Imagem> {
    validateKeys = (keys: Array<keyof Imagem>): boolean => {
        return keys.some((campo) => campo in this.formatByKey);
    };

    formatByKey: Record<
        string,
        (imagens?: Imagem) => React.ReactElement | null
    > = {
            detalhes: (imagens?: Imagem) => {
                if (!imagens?.detalhes?.length) return null;

                return (
                    <CustomGridContainer cols={1} className="">
                        <GridItem fullWidth>
                            <CustomGridContainer cols={2}>
                                {imagens.detalhes.map((detalhe, index) => (
                                    <GridItem key={`imagem-detalhe-column-${index}`} cols={1}>

                                        <div className="">
                                            {Object.entries(detalhe.value).map(
                                                ([key, valueObj]) => {
                                                    return (
                                                        <div key={`detalhe-${index}-${key}`}>
                                                            <GridItem

                                                                cols={1}
                                                                className="py-2 group"
                                                            >
                                                                <CustomReadOnlyInputField
                                                                    label={`${(detalhe.label || key).toUpperCase()} ${index + 1}`}
                                                                    value={String(
                                                                        typeof valueObj === 'object' && valueObj?.value
                                                                            ? valueObj.value
                                                                            : valueObj
                                                                    )}
                                                                    tooltip={renderSourceTooltip(valueObj.source)}
                                                                />
                                                                {
                                                                    isValidUrl(String(valueObj.value)) && (

                                                                        <img
                                                                            src={String(valueObj.value)}
                                                                            alt={`Imagem ${index + 1}`}
                                                                            className="max-w-full h-auto pt-4"
                                                                        />

                                                                    )
                                                                }
                                                            </GridItem>
                                                        </div>
                                                    )
                                                }
                                            )}
                                        </div>

                                    </GridItem>
                                ))}
                            </CustomGridContainer>
                        </GridItem>
                    </CustomGridContainer>
                );
            },
        };

    validateData = (data?: Imagem): boolean => {
        try {
            if (!data || typeof data !== 'object') {
                console.warn('Invalid image data received:', data);
                return false;
            }
            return true;
        } catch (error) {
            console.warn('Error validating image data:', error);
            return false;
        }
    };

    render = (data: Imagem): React.ReactElement[] => {
        try {
            if (!this.validateData(data)) {
                console.warn('Invalid data structure received');
                return [];
            }

            const keys = Object.keys(data!) as Array<keyof Imagem>;

            if (!this.validateKeys(keys)) {
                console.warn('Invalid keys in data:', keys);
                return [];
            }

            return keys
                .map((chave) => this.formatByKey[chave]?.(data))
                .filter((el): el is React.ReactElement => el !== null);
        } catch (error) {
            console.warn('Error validating image data:', error);
            return [];
        }

    };
}
