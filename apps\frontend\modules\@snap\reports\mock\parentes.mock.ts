import { <PERSON>rent<PERSON> } from "../model/Parentes";

export const parenteMock1: Parente = {
  parentesco: {
    value: "parente MAE",
    label: "Parentes<PERSON>",
    source: ["SNAP"],
    is_deleted: false
  },
  detalhes: {
    "full name": {
      value: "<PERSON>",
      label: "Nome Completo",
      source: ["SNAP"],
      is_deleted: false
    },
    nome1: {
      value: "<PERSON>",
      label: "Nome",
      source: ["SNAP"],
      is_deleted: false
    },
    cpf: {
      value: "123.456.789-00",
      label: "CPF",
      source: ["SNAP"],
      is_deleted: false
    },
    "titulo de eleitor": {
      value: "1234 5678 9012",
      label: "<PERSON><PERSON><PERSON><PERSON>eitor",
      source: ["SNAP"],
      is_deleted: false
    },
    "data nascimento": {
      value: "01/01/1970",
      label: "Data de Nascimento",
      source: ["SNAP"],
      is_deleted: false
    },
    sexo: {
      value: "<PERSON><PERSON><PERSON>",
      label: "<PERSON><PERSON>",
      source: ["SNAP"],
      is_deleted: false
    },
    procon: {
      value: "Não consta",
      label: "PROCON",
      source: ["SNAP"],
      is_deleted: false
    }
  },
  telefones: [
    {
      value: {
        "phone number": {
          value: "31991701966",
          label: "Número do Telefone",
          source: ["SNAP"],
          is_deleted: false
        },
      },
      label: "Telefone",
      source: ["SNAP"],
      is_deleted: false
    },
    {
      value: {
        "phone number": {
          value: "31973113426",
          label: "Número do Telefone",
          source: ["SNAP"],
          is_deleted: false
        },
      },
      label: "Telefone",
      source: ["SNAP"],
      is_deleted: false
    }
  ],
  enderecos: [
    {
      value: {
        logradouro: {
          value: "Rua das Flores",
          label: "Logradouro",
          source: ["SNAP"],
          is_deleted: false
        },
        numero: {
          value: "123",
          label: "Número",
          source: ["SNAP"],
          is_deleted: false
        },
        complemento: {
          value: "Casa 101",
          label: "Complemento",
          source: ["SNAP"],
          is_deleted: false
        },
        bairro: {
          value: "Jardim Primavera",
          label: "Bairro",
          source: ["SNAP"],
          is_deleted: false
        },
        area: {
          value: "SP",
          label: "Área",
          source: ["SNAP"],
          is_deleted: false
        },
        "cep ou zipcode": {
          value: "01234-567",
          label: "CEP",
          source: ["SNAP"],
          is_deleted: false
        },
      },
      label: "Endereço",
      source: ["SNAP"],
      is_deleted: false
    }
  ]
};

export const parenteMock2: Parente = {
  parentesco: {
    value: "parente PAI",
    label: "Parentesco",
    source: ["BancoNacionalDeMonitoramentoDePrisoes"],
  },
  detalhes: {
    "full name": {
      value: "Antônio Francisco da Silva",
      label: "Nome Completo",
      source: ["SNAP"],
      is_deleted: false
    }
  }
};

export const parentesMock: Parente[] = [parenteMock1, parenteMock2];
