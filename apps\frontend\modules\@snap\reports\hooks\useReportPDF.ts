import { useEffect, useState, useCallback, useRef } from "react";
import Worker from "../workers/pdf.worker?worker";
import type { ReportDocumentProps } from "../ui/components/ReportDocument";

// Worker wrapper that mimics the Comlink API
class PDFWorkerWrapper {
  private worker: Worker;
  private messageId = 0;
  private pendingPromises: Map<number, { resolve: (value: any) => void; reject: (reason: any) => void }>;

  constructor() {
    this.worker = new Worker();
    this.pendingPromises = new Map();
    
    this.worker.addEventListener('message', (event) => {
      const { id, type, payload } = event.data;
      
      if (type === 'renderPDFResult') {
        const promise = this.pendingPromises.get(id);
        if (promise) {
          promise.resolve(payload);
          this.pendingPromises.delete(id);
        }
      } else if (type === 'renderPDFError') {
        const promise = this.pendingPromises.get(id);
        if (promise) {
          const error = new Error(payload.message);
          error.stack = payload.stack;
          promise.reject(error);
          this.pendingPromises.delete(id);
        }
      }
    });
  }

  renderPDFInWorker(props: ReportDocumentProps): Promise<string> {
    const id = this.messageId++;
    
    return new Promise((resolve, reject) => {
      this.pendingPromises.set(id, { resolve, reject });
      this.worker.postMessage({ id, type: 'renderPDF', payload: props });
    });
  }

  onProgress(enabled: boolean): void {
    const id = this.messageId++;
    this.worker.postMessage({ id, type: 'setLogger', payload: enabled });
  }
}

export const reportPdfWorker = new PDFWorkerWrapper();
reportPdfWorker.onProgress(true);

export const useReportPDF = () => {
  const [state, setState] = useState<{
    loading: boolean;
    error: Error | undefined;
    url: string | undefined;
  }>({
    loading: false,
    error: undefined,
    url: undefined,
  });

  const generatePDF = useCallback(async (props: ReportDocumentProps) => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const url = await reportPdfWorker.renderPDFInWorker(props);
      setState({ loading: false, error: undefined, url });
      return url;
    } catch (error) {
      const err = error as Error;
      setState({ loading: false, error: err, url: undefined });
      throw err;
    }
  }, []);

  // Clean up the blob URL when component unmounts or URL changes
  useEffect(() => {
    return () => {
      if (state.url) {
        URL.revokeObjectURL(state.url);
      }
    };
  }, [state.url]);

  return {
    generatePDF,
    loading: state.loading,
    error: state.error,
    url: state.url
  };
};