import { ValueWithSource } from "./ValueWithSource";

export interface _Fornecimento {
    valor: ValueWithSource;
    data: ValueWithSource;
    documento: ValueWithSource;
    recibo: ValueWithSource;
    descricao: ValueWithSource;
    especie: ValueWithSource;
    natureza: ValueWithSource;
    "natureza estimavel": ValueWithSource;
    origem: ValueWithSource;
    fonte: ValueWithSource;
    Tipo: ValueWithSource;
}

export interface _Empresa {
    "razao social": string;
    cnpj: string;
    cnae: string;
}

export interface FornecimentoEnviado {
    razao_social: ValueWithSource<_Empresa["razao social"]>;
    detalhes: {
        cnpj: ValueWithSource<string>;
        cnae: ValueWithSource<string>;
    };
    fornecimentos: Array<ValueWithSource<_Fornecimento>>;
}
