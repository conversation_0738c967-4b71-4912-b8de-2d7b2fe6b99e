import { ContatoEmpresa, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ssivelContato } from "../model/PossiveisContatos";

// Mock for person contact
const contatoPessoa1: ContatoPessoa = {
    nome_completo: {
        value: "AILTON RAMOS DA SILVA JUNIOR",
        label: "Nome",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        mae: {
            value: "ANTONIA MARIA O RAMOS SILVA",
            label: "<PERSON>ã<PERSON>",
            source: ["SNAP"],
            is_deleted: false
        },
        cpf: {
            value: "08936532766",
            label: "CPF",
            source: ["SNAP"],
            is_deleted: false
        },
        "data nascimento": {
            value: "04/06/1982",
            label: "Data de Nascimento",
            source: ["SNAP"],
            is_deleted: false
        },
        "label default key": {
            value: "outros_contatos",
            label: "Tipo de Contato",
            source: ["SNAP"],
            is_deleted: false
        },
        sexo: {
            value: "M",
            label: "<PERSON><PERSON>",
            source: ["SNAP"],
            is_deleted: false
        }
    },
    telefones: [
        {
            value: {
                "phone number": {
                    value: "552124669210",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        }
    ],
    enderecos: [
        {
            value: {
                logradouro: {
                    value: "RMAGNO MARTINS",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "128",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                complemento: {
                    value: "CS 2105",
                    label: "Complemento",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "FREGUESIA",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "RJ",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "21911430",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Endereço",
            source: ["SNAP"],
            is_deleted: false
        }
    ]
};

// Mock for person contact - Another example
const contatoPessoa2: ContatoPessoa = {
    nome_completo: {
        value: "MARIA OLIVEIRA SANTOS",
        label: "Nome",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        pai: {
            value: "JOSE OLIVEIRA SANTOS",
            label: "Pai",
            source: ["SNAP"],
            is_deleted: false
        },
        cpf: {
            value: "12345678900",
            label: "CPF",
            source: ["SNAP"],
            is_deleted: false
        },
        "data nascimento": {
            value: "15/08/1975",
            label: "Data de Nascimento",
            source: ["SNAP"],
            is_deleted: false
        },
        "titulo de eleitor": {
            value: "123456789012",
            label: "Título de Eleitor",
            source: ["SNAP"],
            is_deleted: false
        },
        "label default key": {
            value: "undefined",
            label: "Tipo de Contato",
            source: ["SNAP"],
            is_deleted: false
        },
        sexo: {
            value: "F",
            label: "Sexo",
            source: ["SNAP"],
            is_deleted: false
        }
    },

    telefones: [
        {
            value: {
                "phone number": {
                    value: "5511987654321",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        }
    ],
    enderecos: [
        {
            value: {
                logradouro: {
                    value: "RUA DAS FLORES",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "456",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "JARDIM PRIMAVERA",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "SÃO PAULO",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "SP",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "04567890",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Endereço",
            source: ["SNAP"],
            is_deleted: false
        }
    ]
};

// Mock for company contact
const contatoEmpresa1: ContatoEmpresa = {
    razao_social: {
        value: "EMPRESA XYZ LTDA",
        label: "Razão Social",
        source: ["SNAP"],
        is_deleted: false
    },
    detalhes: {
        cnpj: {
            value: "12345678000190",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        "label default key": {
            value: "outros_contatos",
            label: "Tipo de Contato",
            source: ["SNAP"],
            is_deleted: false
        }
    },
    telefones: [
        {
            value: {
                "phone number": {
                    value: "551133334444",
                    label: "Número do Telefone",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Telefone",
            source: ["SNAP"],
            is_deleted: false
        }
    ],
    enderecos: [
        {
            value: {
                logradouro: {
                    value: "AVENIDA PAULISTA",
                    label: "Logradouro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                numero: {
                    value: "1000",
                    label: "Número",
                    source: ["SNAP"],
                    is_deleted: false
                },
                bairro: {
                    value: "BELA VISTA",
                    label: "Bairro",
                    source: ["SNAP"],
                    is_deleted: false
                },
                city: {
                    value: "SÃO PAULO",
                    label: "Cidade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                area: {
                    value: "SP",
                    label: "Área",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cep ou zipcode": {
                    value: "01310100",
                    label: "CEP",
                    source: ["SNAP"],
                    is_deleted: false
                }
            },
            label: "Endereço",
            source: ["SNAP"],
            is_deleted: false
        }
    ]
};

// Export the complete mock
export const possiveisContatosMock: PossivelContato[] = [
    contatoPessoa1,
    contatoPessoa2,
    contatoEmpresa1
];
