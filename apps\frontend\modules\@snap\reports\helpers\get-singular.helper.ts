import { normalizeString } from "./normalize-string.helper"; 
import { Dictionary } from "../global";

const getSingularWord: Dictionary = {
  aplicativos: "aplicativo",
  enderecos: "endereço",
  veiculos: "ve<PERSON><PERSON>lo",
  diarios: "di<PERSON><PERSON>",
  oficiais: "oficial",
  sources: "fonte",
  socios: "sócio",
  movimentacoes: "movimentação",
  emails: "email",
  telefones: "telefone",
  pessoas: "pessoa",
  empresas: "empresa",
  processos: "processo",
};

export const getSingular = (key: string): string => {
  if (!key) return "";
  const normalizedkey = normalizeString(key, { case: "lower" });
  return getSingularWord[normalizedkey] || key;
};