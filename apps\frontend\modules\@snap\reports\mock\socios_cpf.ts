import { Socio } from "../model/Socios";

/* 
Dados para adicionar ao sociosCPFMock:

              {
                "full name": "JAIME RODRIGUES BARBOSA NETO",
                "cpf": "***701396**",
                "faixa etaria": "Entre 41 a 50 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio-Administrador"
              },
              {
                "full name": "LUCIANA BISPO DA SILVA GALAO",
                "cpf": "***216301**",
                "faixa etaria": "Entre 41 a 50 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio"
              },
              {
                "full name": "LUIZ HENRIQUE DE SOUZA BORGES",
                "cpf": "***262496**",
                "faixa etaria": "Entre 31 a 40 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio"
              },
              {
                "full name": "LUIZ MARCOS DE ALMEIDA BATISTA",
                "cpf": "***001406**",
                "faixa etaria": "Entre 51 a 60 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio-Administrador"
              },
              {
                "full name": "MARCIO JOSE ROSA GOMES",
                "cpf": "***224971**",
                "faixa etaria": "Entre 51 a 60 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio"
              },
              {
                "full name": "RAFAEL VELASQUEZ SAAVEDRA DA SILVA",
                "cpf": "***560106**",
                "faixa etaria": "Entre 41 a 50 anos",
                "identificador socio": "Pessoa Fisica",
                "pais": "NAO DECLARADOS",
                "qualificacao societaria": "Socio"
              }
            ]
          }
        ]
      }
    ]
  }
]

*/

export const sociosCPFMock: Socio = {
    detalhes: [
        {
            value: {
                "full name": {
                    value: "GIOVANI THIBAU CHRISTOFARO",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO ADMINISTRADOR",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "68000367653",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "04/09/2006",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },

        {
            value: {
                "full name": {
                    value: "JAIME RODRIGUES BARBOSA NETO",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO ADMINISTRADOR",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "03770139607",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "08/09/2016",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "LUCIANA BISPO DA SILVA GALAO",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84421630187",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "LUIZ HENRIQUE DE SOUZA BORGES",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84426249607",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "LUIZ MARCOS DE ALMEIDA BATISTA",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO ADMINISTRADOR",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84400140607",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "MARCIO JOSE ROSA GOMES",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84422497104",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        },
        {
            value: {
                "full name": {
                    value: "RAFAEL VELASQUEZ SAAVEDRA DA SILVA",
                    label: "Nome Completo",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "cargo em sociedade": {
                    value: "SOCIO",
                    label: "Cargo em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
                cpf: {
                    value: "84456010607",
                    label: "CPF",
                    source: ["SNAP"],
                    is_deleted: false
                },
                "entradasociedade": {
                    value: "23/10/2017",
                    label: "Entrada em Sociedade",
                    source: ["SNAP"],
                    is_deleted: false
                },
            },
            label: "Sócio",
            source: ["SNAP"],
            is_deleted: false,
        }
    ]
}