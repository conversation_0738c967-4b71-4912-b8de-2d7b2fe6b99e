# REGRAS RELATÓRIO CNPJ

## Seções existentes para o tipo de relatório "CNPJ" suas respectivas fontes de dados:

- Dados Empresa
  - Fontes de dados: "SNAP", "CadastroNacionalPJ"

- Telefones
  - Fontes de dados: "SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"

- Emails
  - Fontes de dados: "SNAP", "CadastroNacionalPJ"

- Endereços
  - Fontes de dados: "SNAP", "CadastroNacionalPJ", "SintegraMTCNPJ", "SintegraMACNPJ", "SintegraDeSaoPauloCNPJ", "SintegraPBCNPJ", "SintegraSECNPJ", "SintegraMGCNPJ", "SintegraCECNPJ", "SintegraPRCNPJ"

- Sócios
  - Fontes de dados: "SNAP", "CadastroNacionalPJ"

- Juntas Comerciais
  - Fontes de dados: "JUCESP"

- Processos
  - Fontes de dados: "Escavador"

- Diários Oficiais - CNPJ
  - Fontes de dados: "EscavadorDOCNPJ"

- Diários Oficiais - NOME
  - Fontes de dados: "EscavadorDONome"

- Recursos Públicos Recebidos
  - Fontes de dados: "PortalDaTransparenciaDeMinasGeraisCNPJ", "TransparenciaDFCNPJ", "PortalDaTransparenciaDoAmazonasCNPJ", "TransparenciaPRCNPJ", "TransparenciaManausCNPJ"

- Doações Enviadas Campanha
  - Fontes de dados: "TSEDoacoes"

- Doações Recebidas Campanha
  - Fontes de dados: "TSEDoacoes"

- Fornecimentos Enviados Campanha
  - Fontes de dados: "TSEFornecimento"

- Fornecimentos Recebidos Campanha
  - Fontes de dados: "TSEFornecimento"

## SEÇÃO: "Dados Empresa"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"empresa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a empresa de interesse associada ao número do CNPJ buscado.

- Buscar no resultado da chave **"CadastroNacionalPJ"** a lista com chave **"empresa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a empresa de interesse associada ao número do CNPJ buscado.

- Adicionar as propriedades (chave e valor) deste objeto na seção "Dados Empresa", sem listas e objetos, apenas os valores primitivos.
  
- Remover as seguintes propriedades dos objetos:
  - "bookmark"
  - "nome1"
  - "razao social1"

**Regras para a formatação da seção:**

- Ordenar as propriedades inseridas na seção "Dados Empresa" colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "razao social"
  - "cnpj"
  - "procon"
  - "info restricao"
  - "status receita"
  - "data de fundacao"
  - "data_abertura"
  - "sequencial"
  - "porte"
  - "status receita"
  - "tipo de imposto"
  - "total de funcionarios"
  - "quantidade de funcionarios acima de 5 salarios"
  - "quantidade de funcionarios abaixo de 5 salarios"
  - "cnae"
  - "descricao do cnae"
  - restante das propriedades...

- O objeto que constitui a seção **"Dados Empresa"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Dados Empresa"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP", "CadastroNacionalPJ"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- detalhes

```json
  "data": [
    {
        "detalhes": { // objeto com os dados
            "razao social": {
                "value": "TECHBIZ FORENSE DIGITAL LTDA",
                "label": "Razão Social",
                "source": ["SNAP"],
                "is_deleted": false // propriedade que indica se o dado foi deletado
            }
            //outros objetos da seção
        }
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "empresa": {
                "bookmark": 4,
                "capital": "600000.00",
                "cnpj": "05.757.597/0002-18",
                "cpf": "05757597000218",
                "data de fundacao": "20090624",
                "data_abertura": "24/06/2009",
                "datasituacaocadastral": "24/06/2009",
                "desc_naturesa": "SOCIEDADE EMPRESARIA LIMITADA",
                "desc_ramo_atividade": "COMERCIO VAREJISTA ESPECIALIZADO DE EQUIPAMENTOS E SUPRIMENTOS DE INFORMATICA",
                "email_empresa": "<EMAIL>",
                "info restricao": "* NADA CONSTA *",
                "matriz_filial": "FILIAL",
                "natureza": "2062",
                "nome1": "TECHBIZ FORENSE DIGITAL LTDA",
                "pais_nascimento": "BRASIL",
                "porte": "DEMAIS EMPRESA",
                "porte_empresa": "DEMAIS",
                "procon": "(NAO TEM)",
                "quantidade de funcionarios abaixo de 5 salarios": "NAODIVULGADO",
                "quantidade de funcionarios acima de 5 salarios": "NAODIVULGADO",
                "ramo_atividade": "4751201",
                "razao social": "TECHBIZ FORENSE DIGITAL LTDA",
                "razao social1": "TECHBIZ FORENSE DIGITAL SA",
                "sequencial": "1",
                "situacao": "ATIVA",
                "status receita": "ATIVA",
                "total de funcionarios": "35"
            }
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
  {
    "title": "Dados Empresa",
    "subtitle": "Dados consultados na API SNAP.",
    "source": [
      "SNAP"
    ],
    "data_count": 14,
    "data": [
      {
        "detalhes": {
          "razao social": {
            "value": "TECHBIZ FORENSE DIGITAL LTDA",
            "label": "Razão Social",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "cnpj": {
            "value": "05.757.597/0002-18",
            "label": "CNPJ",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "procon": {
            "value": "(NAO TEM)",
            "label": "Procon",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "info restricao": {
            "value": "* NADA CONSTA *",
            "label": "Info Restrição",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "status receita": {
            "value": "ATIVA",
            "label": "Status na Receita",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "data de fundacao": {
            "value": "24/06/2009",
            "label": "Data de Fundação",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "data_abertura": {
            "value": "24/06/2009",
            "label": "Data de Abertura",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "sequencial": {
            "value": "1",
            "label": "Sequencial",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "porte": {
            "value": "DEMAIS EMPRESA",
            "label": "Porte",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "tipo de imposto": {
            "value": "ISENTO",
            "label": "Tipo de Imposto",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "total de funcionarios": {
            "value": "35",
            "label": "Total de Funcionários",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "quantidade de funcionarios acima de 5 salarios": {
            "value": "10",
            "label": "Funcionários acima de 5 salários",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          },
          "quantidade de funcionarios abaixo de 5 salarios": {
            "value": "25",
            "label": "Funcionários abaixo de 5 salários",
            "source": [
              "SNAP"
            ],
            "is_deleted": false
          }
        }
      }
    ]
  }
]
```
</div> 
</div>



## SEÇÃO: "Telefones":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** -> lista com chave **"phonenumber"**.

- Buscar no resultado da chave **"CadastroNacionalPJ"** -> lista com chave **"empresa"** -> lista com chave **"phonenumber"**  ("CadastroNacionalPJ[0].empresa[0].phonenumber"). 
- 
- Buscar no resultado da chave **""SintegraMACNPJ""** -> lista com chave **"phonenumber"**.
  
- Buscar no resultado da chave **""SintegraPBCNPJ""** -> lista com chave **"endereco"** -> lista com chave **"phonenumber"**  (SintegraPBCNPJ[0].endereco[0].phonenumber). 

- Adicionar todos os objetos destas listas no **"data"** da seção **"Telefones"**.

- Trocar o valor de todas as chaves **"phone number"** para **"Telefone"** mais o index + 1.

- Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
    - "operadora",
    - "whatsapp",
    - "bookmark",
    - "data de instalacao",
    - "data de instalacao",

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Telefones"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Telefones"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"] 
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [ // lista com os telefones
            {
                "value": {
                    "phone number": {
                        "value": "553125860228",
                        "label": "Número do Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "label": "Telefone",
                "source": ["SNAP"],
                "is_deleted": false // propriedade que indica se o telefone foi deletado
            }
            //outros objetos da seção
        ]
    }
]
```
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "phonenumber":[
                {
                    "operadora": "CLARO",
                    "phone number": "553125860228"
                },
                {
                    "operadora": "CLARO",
                    "phone number": "5531983462818",
                    "whatsapp": "Nao"
                },
            ]
            //outras propriedades
        }
    ],
    "SintegraMACNPJ": [
        {
            "endpoint": "SintegraMACNPJ",
            "phonenumber":[
                {
                    "operadora": "CLARO",
                    "phone number": "5531988684136"
                },
            ]
            //outras propriedades
        }
    ]
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Telefones",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "SintegraMACNPJ", "CadastroNacionalPJ", "SintegraPBCNPJ"],
        "data_count": 3,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "phone number": {
                                "value": "553125860228",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5531983462818",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5531988684136",
                                "label": "Número do Telefone",
                                "source": ["SintegraMACNPJ"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SintegraMACNPJ"],
                        "is_deleted": false
                    }
                    //outros objetos da seção
                ]
            }
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Emails":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"emailaddress"**.
- Buscar no resultado da chave **"CadastroNacionalPJ"** -> lista com chave **"empresa"** -> lista com chave **"email"** (CadastroNacionalPJ[0].empresa[0].email).

- Adicionar todos os objetos destas listas no **"data"** da seção **"Emails"**.

- Trocar o valor de todas as chaves **"email address"** para **"Email"** mais o index + 1.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Emails"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Emails"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP", "CadastroNacionalPJ"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [
            {
                "value": {
                    "email address": {
                        "value": "<EMAIL>",
                        "label": "Email",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                },
                "label": "Email",
                "source": ["SNAP"],
                "is_deleted": false
            }
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "emailaddress":[
                {
                    "email address": "<EMAIL>"
                },
                {
                    "email address": "<EMAIL>"
                },
            ]
            //outras propriedades
        }
    ]
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Emails",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "CadastroNacionalPJ"],
        "data_count": 2,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "email address": {
                                "value": "<EMAIL>",
                                "label": "Email",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Email",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "email address": {
                                "value": "<EMAIL>",
                                "label": "Email",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Email",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                    //outros objetos da seção
                ]
            }
        ]
    }
    //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Endereços"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"** -> lista com a chave **"location"** (SNAP[0].location).
- No resultado da chave **"SNAP"** -> lista com a chave **"pessoa"** -> lista com a chave **"location"**  ("SNAP[0].pessoa[n].location").
- No resultado da chave **"CadastroNacionalPJ"** -> lista com chave **"empresa"** -> lista com chave **"email"** (CadastroNacionalPJ[0].empresa[0].location).
- No resultado da chave **"SintegraMTCNPJ"** -> lista com a chave **"endereco"** (SintegraMTCNPJ[0].endereco)
- No resultado da chave **"SintegraMACNPJ"** -> lista com a chave **"endereco"** (SintegraMACNPJ[0].endereco)
- No resultado da chave **"SintegraDeSaoPauloCNPJ"** -> lista com a chave **"endereco"** (SintegraDeSaoPauloCNPJ[0].endereco)
- No resultado da chave **"SintegraPBCNPJ"** -> lista com a chave **"endereco"** (SintegraPBCNPJ[0].endereco)
- No resultado da chave **"SintegraSECNPJ"** -> lista com a chave **"endereco"** (SintegraSECNPJ[0].endereco)
- No resultado da chave **"SintegraMGCNPJ"** -> lista com a chave **"endereco"** (SintegraMGCNPJ[0].endereco)
- No resultado da chave **"SintegraCECNPJ"** -> lista com a chave **"endereco"** (SintegraCECNPJ[0].endereco)
- No resultado da chave **"SintegraPRCNPJ"** -> lista com a chave **"endereco"** (SintegraPRCNPJ[0].endereco)

- Caso o endereço não tenha propriedade logradouro, usar a propriedade **"nome"** como logradouro ou propriedade **"endereco"**.

- Adicionar todos os objetos provenientes dessas listas no **"data"** da seção **"Endereços"**.

- Ordenar as propriedades de cada objeto inserido na seção "Endereços", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "logradouro"
  - "numero"
  - "complemento"
  - "bairro"
  - "cidade"
  - "area"
  - "cep ou zipcode"
  - "estado ou regiao"

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "label default key"
   - "city"
   - "nome" (vira logradouro)
   - "endereco" (vira logradouro)

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Endereços"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Endereços"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "CadastroNacionalPJ", "SintegraMTCNPJ", "SintegraMACNPJ", "SintegraDeSaoPauloCNPJ", "SintegraPBCNPJ", "SintegraSECNPJ", "SintegraMGCNPJ", "SintegraCECNPJ", "SintegraPRCNPJ"]. Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
         {
            "endpoint": "SNAP",
            "location": [
                {
                    "area": "MG",
                    "bairro": "VILA DA SERRA",
                    "cep ou zipcode": "34000000",
                    "city": "NOVA LIMA",
                    "complemento": "3",
                    "endereco": "AL DA SERRA",
                    "label default key": "3132111863",
                    "nome": "AL DA SERRA",
                    "numero": "400",
                    "telefone relacionado": "3132111863"
                }
        ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Endereços",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "CadastroNacionalPJ", "SintegraMTCNPJ", "SintegraMACNPJ", "SintegraDeSaoPauloCNPJ", "SintegraPBCNPJ", "SintegraSECNPJ", "SintegraMGCNPJ", "SintegraCECNPJ", "SintegraPRCNPJ"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
          {
            "detalhes": [
                    {
                        "value": {
                            "logradouro": {
                                "value": "AL DA SERRA",
                                "label": "Logradouro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "numero": {
                                "value": "400",
                                "label": "Número",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "complemento": {
                                "value": "3",
                                "label": "Complemento",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "VILA DA SERRA",
                                "label": "Bairro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cidade": {
                                "value": "NOVA LIMA",
                                "label": "Cidade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "area": {
                                "value": "MG",
                                "label": "Área",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "34000000",
                                "label": "CEP",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "estado ou regiao": {
                                "value": "MG",
                                "label": "Estado",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Endereço",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    //outros endereços
                ]
          }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Sócios"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"** -> lista com a chave **"pessoa"** (SNAP[0].pessoa).
- No resultado da chave **"CadastroNacionalPJ"** -> lista com a chave **"empresa"** -> lista com a chave **"pessoa"** (CadastroNacionalPJ[0].empresa[0].pessoa).

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "bookmark"
   - "surname"
   - "first names"
   - "credilink label"
   - "pessoa"
   - "location"
   - "phonenumber"
   - "label default key"

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Sócios"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Sócios"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "CadastroNacionalPJ"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [
            {
                "value": {
                    "full name": {
                        "value": "GIOVANI THIBAU CHRISTOFARO",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cargo em sociedade": {
                        "value": "SOCIO ADMINISTRADOR",
                        "label": "Cargo em Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cpf": {
                        "value": "68000367653",
                        "label": "CPF",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "entradasociedade": {
                        "value": "04/09/2006",
                        "label": "Entrada em Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "label": "Sócio",
                "source": ["SNAP"],
                "is_deleted": false
            },
            //outros sócios
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "cargo em sociedade": "SOCIO ADMINISTRADOR",
                    "cpf": "68000367653",
                    "credilink_label": "socio",
                    "dtiniciosocio": "20060904",
                    "entradasociedade": "04/09/2006",
                    "first names": "GIOVANI",
                    "full name": "GIOVANI THIBAU CHRISTOFARO",
                    "label default key": "socio",
                    "participacaosociedade": "49",
                    "surname": "THIBAU CHRISTOFARO"
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Sócios",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "CadastroNacionalPJ"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
           {
                "detalhes": [
                    {
                        "value": {
                            "full name": {
                                "value": "GIOVANI THIBAU CHRISTOFARO",
                                "label": "Nome Completo",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cargo em sociedade": {
                                "value": "SOCIO ADMINISTRADOR",
                                "label": "Cargo em Sociedade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cpf": {
                                "value": "68000367653",
                                "label": "CPF",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "entradasociedade": {
                                "value": "04/09/2006",
                                "label": "Entrada em Sociedade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Sócio",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    //outros sócios
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Juntas Comerciais"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"JUCESPCNPJ"** -> lista com chave **"empresa jucesp"** (JUCESPCNPJ[0]['empresa jucesp'])

**Regras para a formatação da seção:**

- Ordenar as propriedades de cada objeto inserido na seção "Juntas Comerciais", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "razao social"
  - "nire"
  - "uf"
  - ...restante das propriedades...

- O objeto que constitui a seção **"Juntas Comerciais"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Juntas Comerciais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["JUCESP"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "razao_social"
- "detalhes"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
   "JUCESPCNPJ":
   [            
        {
            "empresa jucesp": [
                {
                    "nire": "35225174099",
                    "razao social": "FACEBOOK SERVICOS ONLINE DO BRASIL LTDA.",
                    "uf": "SAO PAULO"
                }
            ],
            "endpoint": "JUCESP"
        }
    ]
    //outros retornos
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Juntas Comerciais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["JUCESP"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
            {
                "razao_social": {
                    "value": "FACEBOOK SERVICOS ONLINE DO BRASIL LTDA.",
                    "label": "Razão Social",
                    "source": ["JUCESP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "nire": {
                        "value": "35225174099",
                        "label": "NIRE",
                        "source": ["JUCESP"],
                        "is_deleted": false
                    },
                    "uf": {
                        "value": "SAO PAULO",
                        "label": "UF",
                        "source": ["JUCESP"],
                        "is_deleted": false
                    }
                    //restante das propriedades
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>




## SEÇÃO: "Processos":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"Escavador"** -> lista **"processos"** (Escavador[0].processos)

**Importante:** Na estrutura de cada processo, é necessário renderizar a propriedade "numero do processo" separadamente, no mesmo nível que "detalhes" (que contém as propriedades do processo que não são listas) e "movimentacoes". Além disso, deve-se criar listas para cada tipo de participante com base no valor da propriedade "label default key" dos objetos em "pessoa" e "empresa".

As listas de participantes devem seguir a ordem abaixo e usar o formato snake_case para as chaves. Em cada processo, esses valores são buscados dentro da lista pessoa ou empresa, e dependendo da origem, leva o valor da propriedade "label default key" e o sufixo da origem para que a interface possa identificar:

**Lista de todas as chaves possíveis para participantes (em ordem):**
- advogado
- autor_pessoa
- autor_empresa
- reu_pessoa
- reu_empresa
- requerido_pessoa
- requerido_empresa
- executado_pessoa
- executado_empresa
- juizo_deprecante_pessoa
- juizo_deprecante_empresa
- deprecado_pessoa
- deprecado_empresa
- recorrente_pessoa
- recorrente_empresa
- outro_pessoa
- outro_empresa
- impetrante_pessoa
- impetrante_empresa
- paciente_pessoa
- paciente_empresa
- correu_pessoa
- correu_empresa
- juiz
- impetrado_pessoa
- impetrado_empresa
- interessado_pessoa
- interessado_empresa
- apelante_pessoa
- apelante_empresa
- apelado_pessoa
- apelado_empresa
- agravado_pessoa
- agravado_empresa
- agravante_pessoa
- agravante_empresa
- requerente_pessoa
- requerente_empresa
- recorrido_pessoa
- recorrido_empresa
- vitima_pessoa
- vitima_empresa
- assistente_pessoa
- assistente_empresa
- representante_pessoa
- representante_empresa
- ministerio_publico_empresa
- litisconsorte_empresa

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Processos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Processos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["Escavador"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "Escavador": [
       {
            "endpoint": "Escavador",
            "processos": [
                {
                    "advogado": [
                        {
                            "cpf": "71108289649",
                            "full name": "FERNANDO DINIZ PACHECO",
                            "label default key": "Advogado",
                            "oab": "123566/MG"
                        }
                    ],
                    "data da remessa": "2018-08-23",
                    "data instauracao": "2018-06-19",
                    "empresa": [
                        {
                            "cnpj": "01109184000195",
                            "label default key": "Recorrido",
                            "razao social": "UNIVERSO ONLINE S.A"
                        },
                    ],
                    "instancia": "Primeiro Grau",
                    "movimentacoes": "Data - Conteudo - Tipo,2018-09-25 - REMETIDOS OS AUTOS...",
                    "numero do processo": "02116589820188130024",
                    "orgao": "Diario de Justica do Estado de Minas Gerais",
                    "pessoa": [
                        {
                            "full name": "ANDREIA FERREIRA DE SOUZA",
                            "label default key": "Recorrente"
                        },
                        {
                            "cpf": "56528221634",
                            "full name": "SILVEMAR JOSE HENRIQUES SALGADO",
                            "label default key": "Juiz"
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Processos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["Escavador"],
        "data_count": 1,
        "data": [
            {
                "numero": {
                    "value": "02116589820188130024",
                    "label": "Número do Processo",
                    "source": ["Escavador"],
                    "is_deleted": false
                },
                "detalhes": {
                    "instancia": {
                        "value": "Primeiro Grau",
                        "label": "Instância",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "orgao": {
                        "value": "Diario de Justica do Estado de Minas Gerais",
                        "label": "Órgão",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "data da remessa": {
                        "value": "2018-08-23",
                        "label": "Data da Remessa",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "data da instauracao": {
                        "value": "2018-06-19",
                        "label": "Data da Instauração",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                },
                "movimentacoes": {
                    "value": "Data - Conteudo - Tipo,2018-09-25 - REMETIDOS OS AUTOS...",
                    "label": "Movimentações",
                    "source": ["Escavador"],
                    "is_deleted": false
                },
                "advogado": [
                    {
                        "value": {
                            "cpf": {
                                "value": "711.082.896-49",
                                "label": "CPF",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "full name": {
                                "value": "FERNANDO DINIZ PACHECO",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "oab": {
                                "value": "123566/MG",
                                "label": "OAB",
                                "source": ["Escavador"],
                                "is_deleted": false
                            }
                        },
                        "label": "Advogado",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "recorrente_pessoa": [
                    {
                        "value": {
                            "full name": {
                                "value": "ANDREIA FERREIRA DE SOUZA",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Recorrente",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "juiz": [
                    {
                        "value": {
                            "cpf": {
                                "value": "565.282.216-34",
                                "label": "CPF",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "full name": {
                                "value": "SILVEMAR JOSE HENRIQUES SALGADO",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Juiz",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "recorrido_empresa": [
                    {
                        "value": {
                            "cnpj": {
                                "value": "01.109.184/0001-95",
                                "label": "CNPJ",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "razao social": {
                                "value": "UNIVERSO ONLINE S.A",
                                "label": "Razão Social",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Recorrido",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Diários Oficiais - CNPJ":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"EscavadorDOCNPJ"** -> lista **"diario oficial"** (EscavadorDOCNPJ[0]["diario oficial"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Diários Oficiais - CNPJ"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Diários Oficiais - CNPJ"
  - **"subtitle":** "Dados consultados na API SNAP através do CNPJ de entrada.",
  - **"source"**: ["EscavadorDOCNPJ"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "local"
- "detalhes"
- "ocorrencia"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "EscavadorDOCNPJ": [
       {
            "endpoint": "EscavadorDOCNPJ",
            "diario oficial": [
                {
                "dados adicionais": "Diario Oficial do Estado de Minas Gerais",
                "data": "01/08/2012",
                "local": "Diario do Executivo",
                "texto correspondente": "...L024431095 664-50 Locadora De Veic Ribeiro Ltda HCO-9084 L029573291 745-50 Locadora Mariana Ltda HFK-3697 L029573849 745-50 Locadora Mariana Ltda HMC-5695 L029572867 745-50 Localiza Rent A Car Sa HGF-7752 L029572978 745-50 Localiza Rent A Car Sa GVQ-5510 L029573683 745-50 Localiza Rent A Car Sa HJH-0322 L029573775 745-50 Localiza Rent A Car Sa GUP...",
                "link": "https://www.escavador.com/diarios/754584/DOEMG/diario-do-executivo/2012-08-01?page=48"
                },
            ]
       }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Diários Oficiais - CNPJ",
        "subtitle": "Dados consultados na API SNAP através do CNPJ de entrada.",
        "data_count": 1,
        "source": ["EscavadorDOCNPJ"],
        "data": [
            {
                "local": {
                    "value": "Diario do Executivo",
                    "label": "Local",
                    "source": ["EscavadorDOCNPJ"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "01/08/2012",
                        "label": "Data",
                        "source": ["EscavadorDOCNPJ"],
                        "is_deleted": false
                    },
                    "dados adicionais": {
                        "value": "Diario Oficial do Estado de Minas Gerais",
                        "label": "Dados Adicionais",
                        "source": ["EscavadorDOCNPJ"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://www.escavador.com/diarios/754584/DOEMG/diario-do-executivo/2012-08-01?page=48",
                        "label": "Link",
                        "source": ["EscavadorDOCNPJ"],
                        "is_deleted": false
                    }
                },
                "ocorrencia": {
                    "value": "...L024431095 664-50 Locadora De Veic Ribeiro Ltda HCO-9084 L029573291 745-50 Locadora Mariana Ltda HFK-3697 L029573849 745-50 Locadora Mariana Ltda HMC-5695 L029572867 745-50 Localiza Rent A Car Sa HGF-7752 L029572978 745-50 Localiza Rent A Car Sa GVQ-5510 L029573683 745-50 Localiza Rent A Car Sa HJH-0322 L029573775 745-50 Localiza Rent A Car Sa GUP...",
                    "label": "Ocorrência",
                    "source": ["EscavadorDOCNPJ"],
                    "is_deleted": false
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Diários Oficiais - NOME":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"EscavadorDONome"** -> lista **"diario oficial"** (EscavadorDONome[0]["diario oficial"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Diários Oficiais - NOME"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Diários Oficiais - NOME"
  - **"subtitle":** "Dados consultados na API SNAP através do NOME da empresa."
  - **"source"**: ["EscavadorDONome"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "local"
- "detalhes"
- "descricao"
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "EscavadorDONome": [
       {
            "endpoint": "EscavadorDONome",
            "diario oficial": [
                {
                    "dados adicionais": "Diario Oficial do Estado do Rio de Janeiro",
                    "data": "2010-03-03",
                    "descricao": "...fevereiro de 2010, ELISETE...",
                    "link": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1",
                    "local": "Ministerio Publico"
                }
            ]
       }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Diários Oficiais - NOME",
        "subtitle": "Dados consultados na API SNAP através do NOME da empresa.",
        "source": ["EscavadorDONome"],
        "data": [
            {
                "local": {
                    "value": "Ministerio Publico",
                    "label": "Local",
                    "source": ["EscavadorDONome"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "2010-03-03",
                        "label": "Data",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    },
                    "dados adicionais": {
                        "value": "Diario Oficial do Estado do Rio de Janeiro",
                        "label": "Dados Adicionais",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1",
                        "label": "Link",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    }
                },
                "descricao": {
                    "value": "...fevereiro de 2010, ELISETE...",
                    "label": "Descrição",
                    "source": ["EscavadorDONome"],
                    "is_deleted": false
                }
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Recursos Públicos Recebidos":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"PortalDaTransparenciaDeMinasGeraisCNPJ"** -> lista **"empresa"** (PortalDaTransparenciaDeMinasGeraisCNPJ[0].empresa)
  - **"PortalDaTransparenciaDeMinasGeraisCNPJ"** -> lista **"despesas publicas recursos recebidos"** (PortalDaTransparenciaDeMinasGerais[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaDFCNPJ"** -> lista **"despesas publicas recursos recebidos"** (TransparenciaDFCNPJ[0]["despesas publicas recursos recebidos"])
  - **"PortalDaTransparenciaDoAmazonasCNPJ"** -> lista **"despesas publicas recursos recebidos"** (PortalDaTransparenciaDoAmazonasCNPJ[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaPRCNPJ"** -> lista **"despesas publicas recursos recebidos"** (TransparenciaPRCNPJ[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaPRCNPJ"** -> lista **"empenho"** (TransparenciaPRCNPJ[0].empenho)
  - **"TransparenciaManausCNPJ"** -> lista **"empenho"** (TransparenciaManausCNPJ[0].empenho)
  - **"TransparenciaDFCNPJ"** -> lista **"viagem"** (TransparenciaDFCNPJ[0].viagem)

- Ordem das propriedades (propriedades dependem da fonte de dados):

```json
[
  "valor",
  "valor pago",
  "valor empenhado",
  "Valor Empenhado",
  "valor liquidado",
  "categoria economica",
  "data",
  "grupo de despesa",
  "credor",
  "nome do orgao",
  "codigo do orgao",
  "pagamento execicio anterior",
  "numero de ordem bancaria",
  "numero da nota de lancamento",
  "numero do empenho",
  "fonte do recurso",
  "classificacao",
  "unidade gestora",
  "status pagamento",
  "pagamento",
  "historico",
  "numero",
  "tipo de licitacao",
  "natureza",
  "situacao",
  "codigo despesa",
  "data de publicacao",
  "item",
  "acrescimo",
  "anulado",
  "pago",
  "liquidado",
  "ano_mes",
  "tipo de despesa",
  "id",
  "esfera",
  "programa de trabalho",
  "funcao",
  "subfuncao",
  "programa",
  "grupo de natureza da despesa",
  "modalidade de aplicacao",
  "elemento",
  "subelemento",
  "fonte de recurso",
  "empenhado",
  "pago ex",
  "pago rpp",
  "pago rpnp",
  "pago ret"
]
```

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Recursos Públicos Recebidos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Recursos Públicos Recebidos"
  - **"subtitle":** "Dados consultados em portais de Transparência estaduais e federal."
  - **"source"**: ["PortalDaTransparenciaDeMinasGeraisCNPJ", "TransparenciaDFCNPJ", "PortalDaTransparenciaDoAmazonasCNPJ", "TransparenciaPRCNPJ", "TransparenciaManausCNPJ"] uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "orgao" (Caso não tenha propriedade com o nome do orgão, criar um objeto com a propriedade "value" com o valor "Não Informado")
- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json

```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Recursos Públicos Recebidos",
        "subtitle": "Dados consultados em portais de Transparência estaduais e federal.",
        "source": ["PortalDaTransparenciaDeMinasGeraisCNPJ", "TransparenciaDFCNPJ", "PortalDaTransparenciaDoAmazonasCNPJ", "TransparenciaPRCNPJ", "TransparenciaManausCNPJ"],
        "data_count": 0,
        "data": [
            {
                "orgao": {
                    "value": "Prefeitura de Manaus",
                    "label": "Órgão",
                    "source": ["TransparenciaManausCPF"],
                    "is_deleted": false
                },
                "detalhes": {
                    "ano": {
                        "value": "2023",
                        "label": "Ano",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    },
                    "mes": {
                        "value": "07",
                        "label": "Mês",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    },
                    "valor": {
                        "value": "R$ 1.000,00",
                        "label": "Valor (R$)",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Doações Enviadas Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEDoacoes"** -> lista **"empresa"** -> lista **"candidato"** (TSEDoacoes[0].empresa[0].candidato[n])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Doações Enviadas Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Doações Enviadas Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEDoacoes"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de doação enviada contém:
  - `candidato`: Informação sobre o candidato (nome completo)
  - `detalhes`: Objeto com informações detalhadas do candidato (cpf, ano, cargo eleitoral, etc.)
  - `doacoes`: Array de objetos com informações sobre as doações realizadas
  
### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEDoacoes": [
       {
            "endpoint": "TSEDoacoes",
            "empresa": [{
                "candidato": [
                    {
                        "full name": "Marcos Santos",
                        "cpf": "123.456.789-00",
                        "ano da eleição": "2022",
                        "cargo eleitoral": "VEREADOR",
                        "unidade eleitoral": "BELO HORIZONTE",
                        "número do candidato": "12345",
                        "partido eleitoral": "PXX"
                    }
                ]
            }]
       }
    ],
    //outros resultados
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Doações Enviadas Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEDoacoes"],
        "data_count": 1,
        "data": [
            {
                "candidato": {
                    "value": "Marcos Santos",
                    "label": "Candidato",
                    "source": ["TSEDoacoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "123.456.789-00",
                        "label": "CPF",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "ano": {
                        "value": "2022",
                        "label": "Ano",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "cargo eleitoral": {
                        "value": "Vereador",
                        "label": "Cargo Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "unidade eleitoral": {
                        "value": "Belhorizonte",
                        "label": "Unidade Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "número do candidato": {
                        "value": "12345",
                        "label": "Número",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "partido eleitoral": {
                        "value": "PXX",
                        "label": "Partido",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                },
                "doacoes": [
                    {
                        "valor": {
                            "value": "R$ 1.000,00",
                            "label": "Valor (R$)",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "data": {
                            "value": "10/05/2022",
                            "label": "Data",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "recibo": {
                            "value": "123456789",
                            "label": "Recibo",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "descricao": {
                            "value": "Doação para campanha eleitoral",
                            "label": "Descrição",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "especie": {
                            "value": "Transferência eletrônica",
                            "label": "Espécie",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "natureza": {
                            "value": "Recursos próprios",
                            "label": "Natureza",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "natureza estimavel": {
                            "value": "Não se aplica",
                            "label": "Natureza Estimável",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "origem": {
                            "value": "Pessoa Jurídica",
                            "label": "Origem",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "fonte": {
                            "value": "Doação direta",
                            "label": "Fonte",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        },
                        "Tipo": {
                            "value": "Financeira",
                            "label": "Tipo",
                            "source": ["TSEDoacoes"],
                            "is_deleted": false
                        }
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>

## SEÇÃO: "Doações Recebidas Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEDoacoes"** -> lista **"candidato"** -> lista **"empresa"** (TSEDoacoes[0].candidato[0].empresa[n])
  - **"TSEDoacoes"** -> lista **"candidato"** -> lista **"pessoa"** (TSEDoacoes[0].candidato[0].pessoa[n])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Doações Recebidas Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Doações Recebidas Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEDoacoes"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
   - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Existem dois tipos de doações recebidas:
  1. **Doações de Empresas**:
     - `razao_social`: Informação sobre a empresa doadora
     - `detalhes`: Objeto com informações detalhadas da empresa (cnpj, cnae)
     - `doacoes`: Array de objetos com informações sobre as doações realizadas

  2. **Doações de Pessoas Físicas**:
     - `nome_completo`: Informação sobre a pessoa doadora
     - `detalhes`: Objeto com informações detalhadas da pessoa (cpf)
     - `doacoes`: Array de objetos com informações sobre as doações realizadas

### Renderização:
- O nome da empresa (razão social) ou da pessoa (nome completo) é exibido em destaque
- Os detalhes do doador são exibidos em uma grade de duas colunas
- As doações são renderizadas usando a função `renderValidArray` que exibe cada doação com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/DoacoesRecebidas.ts`
- Mock: `apps/frontend/domain/mocks/doacoes_recebidas.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderDoacoesRecebidas.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEDoacoes": [
       {
            "endpoint": "TSEDoacoes",
            "candidato": [
                {
                    "pessoa": [/* lista de dados */],
                    "empresa": [/* lista de dados */],
                },
                {
                    "pessoa": [/* lista de dados */],
                    "empresa": [/* lista de dados */],
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Doações Recebidas Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "data_count": 2,
        "source": ["TSEDoacoes"],
        "data": [
            {
                "razao_social": {
                    "value": "Empresa ABC Ltda",
                    "label": "Razão Social",
                    "source": ["TSEDoacoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "12.345.678/0001-90",
                        "label": "CNPJ",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "cnae": {
                        "value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
                        "label": "CNAE",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                },
                "doacoes": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 5.000,00",
                                "label": "Valor",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "15/08/2022",
                                "label": "Data",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Doação para campanha eleitoral",
                                "label": "Descrição",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos de pessoa jurídica",
                                "label": "Natureza",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Doação direta",
                                "label": "Fonte",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            }
                        },
                        "label": "Doação",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                ]
            },
            // Outros doadores
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Fornecimentos Enviados Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEFornecimento"** -> lista **"candidado"** -> lista **"empresa"** (TSEFornecimento[0].candidato[0].empresa[n])
  - **"TSEFornecimento"** -> lista **"candidado"** -> lista **"pessoa"** (TSEFornecimento[0].candidato[0].pessoa[n])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Fornecimentos Enviados Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Fornecimentos Enviados Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEFornecimento"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de fornecimento enviado contém:
  - `razao_social`: Informação sobre a empresa (razão social)
  - `detalhes`: Objeto com informações detalhadas da empresa (cnpj, cnae)
  - `fornecimentos`: Array de objetos com informações sobre os fornecimentos realizados

### Renderização:
- A razão social da empresa é exibida em destaque
- Os detalhes da empresa são exibidos em uma grade de duas colunas
- Os fornecimentos são renderizados usando a função `renderValidArray` que exibe cada fornecimento com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/FornecimentosEnviados.ts`
- Mock: `apps/frontend/domain/mocks/fornecimentos_enviados.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderFornecimentosEnviados.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEFornecimento": [
       {
            "endpoint": "TSEFornecimento",
            "candidato": [
                {
                    "empresa": [/* lista de dados */]
                },
                {
                    "empresa": [/* lista de dados */]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Fornecimentos Enviados Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEFornecimento"],
        "data_count": 2,
        "data": [
            {
                "razao_social": {
                    "value": "Empresa XYZ Ltda",
                    "label": "Razão Social",
                    "source": ["TSEFornecimento"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "12.345.678/0001-90",
                        "label": "CNPJ",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "cnae": {
                        "value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
                        "label": "CNAE",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                },
                "fornecimentos": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 3.500,00",
                                "label": "Valor",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "10/05/2022",
                                "label": "Data",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Fornecimento de material gráfico para campanha",
                                "label": "Descrição",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Fornecimento direto",
                                "label": "Fonte",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            }
                        },
                        "label": "Fornecimento",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                ]
            },
           // Outros fornecedores
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Fornecimentos Recebidos Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEFornecimento"** -> lista **"empresa"** -> lista **"candidato"** (TSEFornecimento[0].empresa[0].candidato[n])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Fornecimentos Recebidos Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Fornecimentos Recebidos Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEFornecimento"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
 - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de fornecimento recebido contém:
  - `candidato`: Informação sobre o candidato (nome completo)
  - `detalhes`: Objeto com informações detalhadas do candidato (cpf, ano da eleição, cargo eleitoral, etc.)
  - `fornecimentos`: Array de objetos com informações sobre os fornecimentos recebidos

### Renderização:
- O nome do candidato é exibido em destaque
- Os detalhes do candidato são exibidos em uma grade de duas colunas
- Os fornecimentos são renderizados usando a função `renderValidArray` que exibe cada fornecimento com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/FornecimentosRecebidos.ts`
- Mock: `apps/frontend/domain/mocks/fornecimentos_recebidos.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderFornecimentosRecebidos.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEFornecimento": [
       {
            "endpoint": "TSEFornecimento",
            "pessoa": [
                {
                    "candidato": [/* lista de dados */]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Fornecimentos Recebidos Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEFornecimento"],
        "data_count": 2,
        "data": [
            {
                "candidato": {
                    "value": "João da Silva",
                    "label": "Candidato",
                    "source": ["TSEFornecimento"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "123.456.789-00",
                        "label": "CPF",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "ano da eleição": {
                        "value": "2022",
                        "label": "Ano Da Eleição",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "cargo eleitoral": {
                        "value": "Vereador",
                        "label": "Cargo Eleitoral",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "unidade eleitoral": {
                        "value": "São Paulo / SP",
                        "label": "Unidade Eleitoral",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "número do candidato": {
                        "value": "12345",
                        "label": "Número",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "partido eleitoral": {
                        "value": "Partido ABC",
                        "label": "Partido",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                },
                "fornecimentos": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 3.000,00",
                                "label": "Valor",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "10/05/2022",
                                "label": "Data",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Fornecimento de material gráfico para campanha",
                                "label": "Descrição",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Fornecimento direto",
                                "label": "Fonte",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            }
                        },
                        "label": "Fornecimento",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                ]
            },
            // Outros fornecimentos recebidos
        ]
    }
     //outras seções
]
```
</div>
</div>