import { Button } from "@snap/design-system";
import { MdOutlineVpnKeyOff } from "react-icons/md";

interface MissingKeyProps {
  onInsertKey?: () => void;
}

const MissingKey = ({ onInsertKey }: MissingKeyProps) => {
  return (
    <div className="flex flex-col gap-8 items-center text-gray-300">
      <p className="text-xl font-mono ">Senha do usuário não foi encontrada.</p>
      <MdOutlineVpnKeyOff size={64} />
      <Button variant="destructive" onClick={onInsertKey} className="uppercase">
        Inserir senha
      </Button>
    </div>
  );
};

export default MissingKey;
