import { ValueWithSource } from "./ValueWithSource"

interface _PessoaBase {
  cpf?: ValueWithSource;
  "full name": ValueWithSource;
}

interface _EmpresaBase {
  cnpj: ValueWithSource;
  "razao social": ValueWithSource;
}

// Advogado
interface _Advogado extends _PessoaBase {
  oab: ValueWithSource;
  "label default key"?: "Advogado";
}

// Autor
interface _AutorPessoa extends _PessoaBase {
  "label default key"?: "Autor";
}

interface _AutorEmpresa extends _EmpresaBase {
  "label default key"?: "Autor";
}

// Reu
interface _ReuPessoa extends _PessoaBase {
  "label default key"?: "Reu";
}

interface _ReuEmpresa extends _EmpresaBase {
  "label default key"?: "Reu";
}

// Requerido
interface _RequeridoPessoa extends _PessoaBase {
  "label default key"?: "Requerido";
}

interface _RequeridoEmpresa extends _EmpresaBase {
  "label default key"?: "Requerido";
}

// Executado
interface _ExecutadoPessoa extends _PessoaBase {
  "label default key"?: "Executado";
}

interface _ExecutadoEmpresa extends _EmpresaBase {
  "label default key"?: "Executado";
}

// Juizo Deprecante
interface _JuizoDeprecantePessoa extends _PessoaBase {
  "label default key"?: "Juizo Deprecante";
}

interface _JuizoDeprecantEmpresa extends _EmpresaBase {
  "label default key"?: "Juizo Deprecante";
}

// Deprecado
interface _DeprecadoPessoa extends _PessoaBase {
  "label default key"?: "Deprecado";
}

interface _DeprecadoEmpresa extends _EmpresaBase {
  "label default key"?: "Deprecado";
}

// Recorrente
interface _RecorrentePessoa extends _PessoaBase {
  "label default key"?: "Recorrente";
}

interface _RecorrenteEmpresa extends _EmpresaBase {
  "label default key"?: "Recorrente";
}

// Outro
interface _OutroPessoa extends _PessoaBase {
  "label default key"?: "Outro";
}

interface _OutroEmpresa extends _EmpresaBase {
  "label default key"?: "Outro";
}

// Impetrante
interface _ImpetrantePessoa extends _PessoaBase {
  "label default key"?: "Impetrante";
}

interface _ImpetranteEmpresa extends _EmpresaBase {
  "label default key"?: "Impetrante";
}

// Paciente
interface _PacientePessoa extends _PessoaBase {
  "label default key"?: "Paciente";
}

interface _PacienteEmpresa extends _EmpresaBase {
  "label default key"?: "Paciente";
}

// Correu
interface _CorreuPessoa extends _PessoaBase {
  "label default key"?: "Correu";
}

interface _CorreuEmpresa extends _EmpresaBase {
  "label default key"?: "Correu";
}

// Juiz
interface _Juiz extends _PessoaBase {
  "label default key"?: "Juiz";
}

// Impetrado
interface _ImpetradoPessoa extends _PessoaBase {
  "label default key"?: "Impetrado";
}

interface _ImpetradoEmpresa extends _EmpresaBase {
  "label default key"?: "Impetrado";
}

// Interessado
interface _InteressadoPessoa extends _PessoaBase {
  "label default key"?: "Interessado";
}

interface _InteressadoEmpresa extends _EmpresaBase {
  "label default key"?: "Interessado";
}

// Apelante
interface _ApelantePessoa extends _PessoaBase {
  "label default key"?: "Apelante";
}

interface _ApelanteEmpresa extends _EmpresaBase {
  "label default key"?: "Apelante";
}

// Apelado
interface _ApeladoPessoa extends _PessoaBase {
  "label default key"?: "Apelado";
}

interface _ApeladoEmpresa extends _EmpresaBase {
  "label default key"?: "Apelado";
}

// Agravado
interface _AgravadoPessoa extends _PessoaBase {
  "label default key"?: "Agravado";
}

interface _AgravadoEmpresa extends _EmpresaBase {
  "label default key"?: "Agravado";
}

// Agravante
interface _AgravantePessoa extends _PessoaBase {
  "label default key"?: "Agravante";
}

interface _AgravanteEmpresa extends _EmpresaBase {
  "label default key"?: "Agravante";
}

// Requerente
interface _RequerentePessoa extends _PessoaBase {
  "label default key"?: "Requerente";
}

interface _RequerenteEmpresa extends _EmpresaBase {
  "label default key"?: "Requerente";
}

// Recorrido
interface _RecorridoPessoa extends _PessoaBase {
  "label default key"?: "Recorrido";
}

interface _RecorridoEmpresa extends _EmpresaBase {
  "label default key"?: "Recorrido";
}

// Vitima
interface _VitimaPessoa extends _PessoaBase {
  "label default key"?: "Vitima";
}

interface _VitimaEmpresa extends _EmpresaBase {
  "label default key"?: "Vitima";
}

// Assistente
interface _AssistentePessoa extends _PessoaBase {
  "label default key"?: "Assistente";
}

interface _AssistenteEmpresa extends _EmpresaBase {
  "label default key"?: "Assistente";
}

// Representante
interface _RepresentantePessoa extends _PessoaBase {
  "label default key"?: "Representante";
}

interface _RepresentanteEmpresa extends _EmpresaBase {
  "label default key"?: "Representante";
}

// Ministerio Publico
interface _MinisterioPublicoEmpresa extends _EmpresaBase {
  "label default key"?: "Ministerio Publico";
}

// Litisconsorte
interface _LitisconsorteEmpresa extends _EmpresaBase {
  "label default key"?: "Litisconsorte";
}

// Union types for all pessoa and empresa types
type _Pessoa =
  | _AutorPessoa
  | _ReuPessoa
  | _RequeridoPessoa
  | _ExecutadoPessoa
  | _JuizoDeprecantePessoa
  | _DeprecadoPessoa
  | _RecorrentePessoa
  | _OutroPessoa
  | _ImpetrantePessoa
  | _PacientePessoa
  | _CorreuPessoa
  | _Juiz
  | _ImpetradoPessoa
  | _InteressadoPessoa
  | _ApelantePessoa
  | _ApeladoPessoa
  | _AgravadoPessoa
  | _AgravantePessoa
  | _RequerentePessoa
  | _RecorridoPessoa
  | _VitimaPessoa
  | _AssistentePessoa
  | _RepresentantePessoa
  | _Advogado;

type _Empresa =
  | _AutorEmpresa
  | _ReuEmpresa
  | _RequeridoEmpresa
  | _ExecutadoEmpresa
  | _JuizoDeprecantEmpresa
  | _DeprecadoEmpresa
  | _RecorrenteEmpresa
  | _OutroEmpresa
  | _ImpetranteEmpresa
  | _PacienteEmpresa
  | _CorreuEmpresa
  | _ImpetradoEmpresa
  | _InteressadoEmpresa
  | _ApelanteEmpresa
  | _ApeladoEmpresa
  | _AgravadoEmpresa
  | _AgravanteEmpresa
  | _RequerenteEmpresa
  | _RecorridoEmpresa
  | _VitimaEmpresa
  | _AssistenteEmpresa
  | _RepresentanteEmpresa
  | _MinisterioPublicoEmpresa
  | _LitisconsorteEmpresa;

interface _Processo {
  "numero do processo": string;
  instancia: string;
  movimentacoes: string;
  orgao: string;
  "data da remessa": string;
  "data da instauracao": string;
  advogado: Array<_Advogado>;
  pessoa: Array<_Pessoa>;
  empresa: Array<_Empresa>;
}

export interface Processo {
  numero: ValueWithSource<_Processo["numero do processo"]>;
  detalhes: Record<
    keyof Omit<
      _Processo,
      "numero do processo" | "movimentacoes" | "advogado" | "pessoa" | "empresa"
    >,
    ValueWithSource<string>
  >;
  movimentacoes: ValueWithSource<_Processo["movimentacoes"]>;
  advogado?: Array<ValueWithSource<_Advogado>>;

  // Pessoa
  autor_pessoa?: Array<ValueWithSource<_AutorPessoa>>;
  reu_pessoa?: Array<ValueWithSource<_ReuPessoa>>;
  requerido_pessoa?: Array<ValueWithSource<_RequeridoPessoa>>;
  executado_pessoa?: Array<ValueWithSource<_ExecutadoPessoa>>;
  juizo_deprecante_pessoa?: Array<ValueWithSource<_JuizoDeprecantePessoa>>;
  deprecado_pessoa?: Array<ValueWithSource<_DeprecadoPessoa>>;
  recorrente_pessoa?: Array<ValueWithSource<_RecorrentePessoa>>;
  outro_pessoa?: Array<ValueWithSource<_OutroPessoa>>;
  impetrante_pessoa?: Array<ValueWithSource<_ImpetrantePessoa>>;
  paciente_pessoa?: Array<ValueWithSource<_PacientePessoa>>;
  correu_pessoa?: Array<ValueWithSource<_CorreuPessoa>>;
  juiz?: Array<ValueWithSource<_Juiz>>;
  impetrado_pessoa?: Array<ValueWithSource<_ImpetradoPessoa>>;
  interessado_pessoa?: Array<ValueWithSource<_InteressadoPessoa>>;
  apelante_pessoa?: Array<ValueWithSource<_ApelantePessoa>>;
  apelado_pessoa?: Array<ValueWithSource<_ApeladoPessoa>>;
  agravado_pessoa?: Array<ValueWithSource<_AgravadoPessoa>>;
  agravante_pessoa?: Array<ValueWithSource<_AgravantePessoa>>;
  requerente_pessoa?: Array<ValueWithSource<_RequerentePessoa>>;
  recorrido_pessoa?: Array<ValueWithSource<_RecorridoPessoa>>;
  vitima_pessoa?: Array<ValueWithSource<_VitimaPessoa>>;
  assistente_pessoa?: Array<ValueWithSource<_AssistentePessoa>>;
  representante_pessoa?: Array<ValueWithSource<_RepresentantePessoa>>;

  // Empresa
  autor_empresa?: Array<ValueWithSource<_AutorEmpresa>>;
  reu_empresa?: Array<ValueWithSource<_ReuEmpresa>>;
  requerido_empresa?: Array<ValueWithSource<_RequeridoEmpresa>>;
  executado_empresa?: Array<ValueWithSource<_ExecutadoEmpresa>>;
  juizo_deprecante_empresa?: Array<ValueWithSource<_JuizoDeprecantEmpresa>>;
  deprecado_empresa?: Array<ValueWithSource<_DeprecadoEmpresa>>;
  recorrente_empresa?: Array<ValueWithSource<_RecorrenteEmpresa>>;
  outro_empresa?: Array<ValueWithSource<_OutroEmpresa>>;
  impetrante_empresa?: Array<ValueWithSource<_ImpetranteEmpresa>>;
  paciente_empresa?: Array<ValueWithSource<_PacienteEmpresa>>;
  correu_empresa?: Array<ValueWithSource<_CorreuEmpresa>>;
  impetrado_empresa?: Array<ValueWithSource<_ImpetradoEmpresa>>;
  interessado_empresa?: Array<ValueWithSource<_InteressadoEmpresa>>;
  apelante_empresa?: Array<ValueWithSource<_ApelanteEmpresa>>;
  apelado_empresa?: Array<ValueWithSource<_ApeladoEmpresa>>;
  agravado_empresa?: Array<ValueWithSource<_AgravadoEmpresa>>;
  agravante_empresa?: Array<ValueWithSource<_AgravanteEmpresa>>;
  requerente_empresa?: Array<ValueWithSource<_RequerenteEmpresa>>;
  recorrido_empresa?: Array<ValueWithSource<_RecorridoEmpresa>>;
  vitima_empresa?: Array<ValueWithSource<_VitimaEmpresa>>;
  assistente_empresa?: Array<ValueWithSource<_AssistenteEmpresa>>;
  representante_empresa?: Array<ValueWithSource<_RepresentanteEmpresa>>;
  ministerio_publico_empresa?: Array<ValueWithSource<_MinisterioPublicoEmpresa>>;
  litisconsorte_empresa?: Array<ValueWithSource<_LitisconsorteEmpresa>>;
}
