import { DiarioOficialCPF } from "../model/DiariosOficiaisCPF";

const diarioOficialEscavador1: DiarioOficialCPF = {
    local: {
        value: "Diário Oficial da União",
        label: "Local",
        source: ["EscavadorDOCPF"],
        is_deleted: false
    },
    detalhes: {
        data: {
            value: "15/05/2023",
            label: "Data",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        },
        "dados adicionais": {
            value: "Seção 2, Página 45",
            label: "Dados Adicionais",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        },
        link: {
            value: "https://www.in.gov.br/web/dou/-/portaria-de-15-de-maio-de-2023",
            label: "Link",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        }
    },
    "descrição": {
        value: "Portaria de nomeação para cargo público",
        label: "Descrição",
        source: ["EscavadorDOCPF"],
        is_deleted: false
    },
};

const diarioOficialEscavador2: DiarioOficialCPF = {
    local: {
        value: "Diário de Justiça do Estado de São Paulo",
        label: "Local",
        source: ["EscavadorDOCPF"],
        is_deleted: false
    },
    detalhes: {
        data: {
            value: "22/06/2023",
            label: "Data",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        },
        "dados adicionais": {
            value: "Caderno 4, Página 123",
            label: "Dados Adicionais",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        },
        link: {
            value: "https://www.dje.tjsp.jus.br/cdje/consultaSimples.do?cdVolume=15&nuDiario=3528",
            label: "Link",
            source: ["EscavadorDOCPF"],
            is_deleted: false
        },
    },
    "descrição": {
        value: "Decisão judicial em processo civil",
        label: "Descrição",
        source: ["EscavadorDOCPF"],
        is_deleted: false
    },
};

const diarioOficialQueridoDiario1: DiarioOficialCPF = {
    local: {
        value: "São Paulo / SP",
        label: "Cidade",
        source: ["QueridoDiarioCPF"],
        is_deleted: false
    },
    detalhes: {
        data: {
            value: "10/07/2023",
            label: "Data",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        },
        "edicao extra?": {
            value: "Não",
            label: "Edição Extra?",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        },
        link: {
            value: "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx",
            label: "Link",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        }
    },
    "descrição": {
        value: "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses.",
        label: "Texto",
        source: ["QueridoDiarioCPF"],
        is_deleted: false
    }


};

const diarioOficialQueridoDiario2: DiarioOficialCPF = {
    local: {
        value: "Rio de Janeiro / RJ",
        label: "Cidade",
        source: ["QueridoDiarioCPF"],
        is_deleted: false
    },
    detalhes: {
        data: {
            value: "05/08/2023",
            label: "Data",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        },
        "edicao extra?": {
            value: "Sim",
            label: "Edição Extra?",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        },
        link: {
            value: "https://doweb.rio.rj.gov.br/",
            label: "Link",
            source: ["QueridoDiarioCPF"],
            is_deleted: false
        }
    },
    "descrição":
    {

        value: "EXTRATO DE CONTRATO Nº 123/2023. Partes: Município do Rio de Janeiro e João da Silva ME. Objeto: Prestação de serviços de manutenção predial. Valor: R$ 150.000,00. Prazo: 12 meses.",
        label: "Texto",
        source: ["QueridoDiarioCPF"],
        is_deleted: false

    }

};

export const diariosOficiaisCNPJMock: DiarioOficialCPF[] = [
    diarioOficialEscavador1,
    diarioOficialEscavador2,
    diarioOficialQueridoDiario1,
    diarioOficialQueridoDiario2
];
