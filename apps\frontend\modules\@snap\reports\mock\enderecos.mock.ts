import { <PERSON>ere<PERSON>, _Endereco } from "../model/Enderecos";

export const enderecoMock: Endereco = {
  detalhes: [
    {
      value: {
        logradouro: {
          value: "Rua das Flores",
          label: "Logradouro",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        numero: {
          value: "123",
          label: "Número",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        complemento: {
          value: "Apto 101",
          label: "Complemento",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        bairro: {
          value: "<PERSON>ardim Primavera",
          label: "Bairro",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        city: {
          value: "São Paulo",
          label: "Cidade",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        area: {
          value: "Zona Sul",
          label: "Área",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        "cep ou zipcode": {
          value: "01234-567",
          label: "CEP",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        cidade: {
          value: "São Paulo",
          label: "Cidade",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        "estado ou regiao": {
          value: "SP",
          label: "Estado ou Região",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
      },
      label: "Endereço",
      source: ["SNAP"],
      is_deleted: false
    },
    {
      value: {
        logradouro: {
          value: "Avenida Paulista",
          label: "Logradouro",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        numero: {
          value: "1578",
          label: "Número",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        complemento: {
          value: "Sala 304",
          label: "Complemento",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        bairro: {
          value: "Bela Vista",
          label: "Bairro",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        area: {
          value: "Centro",
          label: "Área",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        "cep ou zipcode": {
          value: "01310-200",
          label: "CEP",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        cidade: {
          value: "São Paulo",
          label: "Cidade",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
        "estado ou regiao": {
          value: "SP",
          label: "Estado ou Região",
          source: ["Cadastro Nacional"],
          is_deleted: false
        },
      },
      label: "Endereço",
      source: ["SNAP"],
      is_deleted: false
    },
  ]
};