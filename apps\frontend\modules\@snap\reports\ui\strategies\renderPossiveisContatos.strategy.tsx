import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel, GridContainer, GridItem } from "@snap/design-system";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { ContatoEmpresa, PossivelContato, ContatoPessoa } from "../../model/PossiveisContatos";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderPossiveisContatos implements RenderStrategy<PossivelContato> {

  validateKeys = (keys: Array<keyof PossivelContato>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  renderTelefones = (contato?: PossivelContato): React.ReactElement | null => {
    return renderValidArray('telefones', contato || {}, ['operadora', 'whatsapp'], false /* hasLabel */);
  };

  renderEnderecos = (contato?: PossivelContato): React.ReactElement | null => {
    return renderValidArray('enderecos', contato || {});
  };

  formatByKey: Record<
    string,
    (contato?: PossivelContato) => React.ReactElement | null
  > = {
      nome_completo: (contato?: ContatoPessoa | null) => {
        if (!contato?.nome_completo) return null;

        return (
          <GridContainer cols={3}>
            <GridItem cols={1} className="mb-6 group">
              <CustomReadOnlyInputField
                label={(contato.nome_completo.label || "Nome Completo").toUpperCase()}
                colorClass="bg-primary"
                labelTextClass="text-accent"
                 value={parseValue(contato.nome_completo.value)}
                tooltip={renderSourceTooltip(contato.nome_completo.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },

      razao_social: (contato?: ContatoEmpresa) => {
        if (!contato?.razao_social) return null;

        return (
          <GridContainer cols={3}>
            <GridItem cols={1} className="mb-6 group">
              <CustomReadOnlyInputField
                label={(contato.razao_social.label || "Nome Completo").toUpperCase()}
                colorClass="bg-primary"
                labelTextClass="text-accent"
                 value={parseValue(contato.razao_social.value)}
                tooltip={renderSourceTooltip(contato.razao_social.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },

      detalhes: (contato?: PossivelContato) => {
        if (!contato?.detalhes) return null;

        return (
          <GridContainer cols={2} columnFirst className="mb-6">
            {Object.entries(contato.detalhes).map(([key, value]) => (
              <GridItem key={key} cols={1} className="group">
                <CustomReadOnlyInputField
                  label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                  value={parseValue(String(value.value))}
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  tooltip={renderSourceTooltip(value.source)}
                />
              </GridItem>
            ))}
          </GridContainer>
        );
      },

      telefones: (contato?: PossivelContato) => {
        return this.renderTelefones(contato);
      },

      enderecos: (contato?: PossivelContato) => {
        return this.renderEnderecos(contato);
      },
    };

  render = (contato: PossivelContato): React.ReactElement[] => {
    const keys = Object.keys(contato) as Array<keyof PossivelContato>;

    if (!this.validateKeys(keys)) {
      console.log(keys);
      //throw new Error("Chaves inválidas");
    }

    const orderedKeys: Array<keyof PossivelContato> = [
      'nome_completo',
      'razao_social',
      'detalhes',
      'telefones',
      'enderecos'
    ];

    // Filter the keys to only include those that exist in the possiveisContas object
    const filteredKeys = orderedKeys.filter(key => keys.includes(key));

    return filteredKeys
      .map((chave) => {
        if (chave in this.formatByKey) {
          return this.formatByKey[chave]?.(contato);
        }

        const value = contato[chave];
        if (isValidArray(value)) {
          return renderValidArray(chave as string, contato);
        }

        return null;
      })
      .filter((el): el is React.ReactElement => el !== null);
  };
}
