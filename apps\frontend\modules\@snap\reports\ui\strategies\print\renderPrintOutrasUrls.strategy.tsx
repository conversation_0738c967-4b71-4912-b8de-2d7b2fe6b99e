import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintOutrasUrlsProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<{
        value: {
          [key: string]: ValueWithSource;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const RenderPrintOutrasUrls: React.FC<RenderPrintOutrasUrlsProps> = ({ section }) => {
  const validEntries = section.data?.filter(entry => entry.detalhes?.length) || [];

  if (!validEntries.length) return null;

  const allUrls = validEntries.flatMap(entry =>
    entry.detalhes?.filter(detalhe => !detalhe.is_deleted) || []
  );

  if (!allUrls.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      <View style={styles.grid}>
        {allUrls.map((detalhe, index) => {
          const urlEntry = Object.entries(detalhe.value).find(
            ([_, valueObj]) => !valueObj.is_deleted
          );

          if (!urlEntry) return null;

          const [key, valueObj] = urlEntry;
          const urlValue = valueObj.value;
          const sourceValue = valueObj.source;

          if (!urlValue) return null;

          return (
            <View key={`url-${index}`} style={styles.cell}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill="#889EA3" />
                </Svg>
                <Text style={styles.label}>URL {index + 1}</Text>
                <Text style={styles.sourceText}>
                  {sourceValue?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.value}>{urlValue}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: { width: 4, height: 4, marginRight: 4, marginTop: 1 },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  grid: {
    paddingLeft: 8,
    paddingTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 8,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
    marginBottom: 2,
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
});