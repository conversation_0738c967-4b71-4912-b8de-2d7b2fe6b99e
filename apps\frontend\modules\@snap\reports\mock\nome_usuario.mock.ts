import { NomeUsuario } from "../model/NomeUsuario";

export const nomeUsuarioMock: NomeUsuario = {
    detalhes: [
        {
            "value": {
                "alias": {
                    "value": "bianca.parreira",
                    "label": "Nome de Usuário",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
            },
            "label": "Nome de Usuário",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        {
            "value": {
                "alias": {
                    "value": "Bianca_Parreira_123",
                    "label": "Nome de Usuário",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
            },
            "label": "Nome de Usuário",
            "source": ["IRBISLuna"],
            "is_deleted": false
        },
        {
            "value": {
                "alias": {
                    "value": "bianca-765343",
                    "label": "Nome de Usuário",
                    "source": ["IRBISLuna"],
                    "is_deleted": false
                },
            },
            "label": "Nome de Usuário",
            "source": ["IRBISLuna"],
            "is_deleted": false
        }
    ]
};


