import { But<PERSON> } from "@snap/design-system"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  maxPageButtons?: number
}

export function Pagination({ currentPage, totalPages, onPageChange, maxPageButtons = 7 }: PaginationProps) {
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = []

    // Always show first page
    if (currentPage > 3) {
      pageNumbers.push(1)

      // Add ellipsis if needed
      if (currentPage > 4) {
        pageNumbers.push("...")
      }
    }

    // Calculate range of pages to show around current page
    const startPage = Math.max(2, currentPage - 1)
    const endPage = Math.min(totalPages - 1, currentPage + 1)

    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i)
    }

    // Always show last page
    if (currentPage < totalPages - 2) {
      // Add ellipsis if needed
      if (currentPage < totalPages - 3) {
        pageNumbers.push("...")
      }
      pageNumbers.push(totalPages)
    }

    return pageNumbers
  }

  const pageNumbers = getPageNumbers()

  return (
    <div className="flex items-center justify-between px-2 mt-auto">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        PÁGINA ANTERIOR
      </Button>

{/*       <div className="flex items-center space-x-1">
        {pageNumbers.map((page, index) =>
          page === "..." ? (
            <span key={`ellipsis-${index}`} className="px-2 text-slate-400">
              ...
            </span>
          ) : (
            <Button
              key={`page-${page}`}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              onClick={() => onPageChange(page as number)}
              className={
                currentPage === page
                  ? "bg-slate-600 text-slate-100"
                  : "bg-slate-800 border-slate-600 text-slate-200 hover:bg-slate-700"
              }
            >
              {page}
            </Button>
          ),
        )}
      </div> */}

      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        PRÓXIMA PÁGINA
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  )
}
