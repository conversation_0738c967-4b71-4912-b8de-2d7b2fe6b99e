global:
  scrape_interval: 15s

scrape_configs:
  # Scrape Prometheus itself
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # Scrape the frontend NGINX service through the metrics exporter
  - job_name: "frontend"
    static_configs:
      - targets: ["host.docker.internal:9113"] # Observe the frontend NGINX service

  # Scrape <PERSON><PERSON> (listens on port 12345)
  - job_name: "grafana-alloy"
    static_configs:
      - targets: ["host.docker.internal:12345"]

  # Scrape Loki
  - job_name: "loki"
    static_configs:
      - targets: ["host.docker.internal:3100"]
