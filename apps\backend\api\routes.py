import logging

logger = logging.getLogger(__name__)

from fastapi import APIRouter

# Try importing the routers
try:
    from api.auth_routes import router as auth_router
    from api.report_routes import router as report_router
    from api.user_routes import router as user_router
except ImportError as e:
    logging.error("Failed to import route modules: %s", e)
    raise



router = APIRouter()

logger.info("Registering route groups...")

# Helper function to safely register routes
def safe_include_router(router_to_include, prefix: str, tags: list[str]):
    try:
        router.include_router(router_to_include, prefix=prefix, tags=tags)
        logger.info("Successfully registered routes: %s under %s", tags[0], prefix)
    except Exception as e:
        logger.error("Failed to register %s routes under %s: %s", tags[0], prefix, e)
        raise

# Register all routers with error handling
safe_include_router(auth_router, prefix="/auth", tags=["Authentication"])
safe_include_router(report_router, prefix="/reports", tags=["Reports"])
safe_include_router(user_router, prefix="/user", tags=["Users"])

logger.info("All routers registered successfully.")
