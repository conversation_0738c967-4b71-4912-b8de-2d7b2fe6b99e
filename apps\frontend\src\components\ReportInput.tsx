import { Input } from "@snap/design-system";
import Inputmask from "inputmask";
import { useEffect, useRef } from "react";

interface ReportInputProps {
  setInputValue: (value: string) => void;
  reportType: string;
  inputValue: string;
}

interface Masks {
  [key: string]: string;
}

export function ReportInput({
  setInputValue,
  reportType,
  inputValue,
}: ReportInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!inputRef.current) return;

    const masks: Masks = {
      cpf: "999.999.999-99",
      cnpj: "99.999.999/9999-99",
      email: "",
      telefone: "(99) 99999-9999",
    };

    const maskPattern = reportType ? masks[reportType] : "";

    Inputmask(maskPattern, {
      placeholder: "_",
      showMaskOnHover: false,
      autoUnmask: true,
    }).mask(inputRef.current);
  }, [reportType]);

  return (
    <Input
      ref={inputRef}
      value={inputValue}
      onChange={(e) => setInputValue(e.target.value)}
      className="w-full"
      placeholder={reportType ? `Digite o ${reportType?.toUpperCase()}` : ""}
      disabled={!reportType}
      data-testid="input-report-value"
    />
  );
}
