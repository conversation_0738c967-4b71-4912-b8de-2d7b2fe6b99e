#!/bin/bash

set -e
STACK_NAME=mystack

CLIENT_SECRET_NAME=client_secret_keycloak
ADMIN_USER_SECRET_NAME=keycloak_admin_user
ADMIN_PASSWORD_SECRET_NAME=keycloak_admin_password

SNAP_API_CLIENT_SECRET_NAME=snap_api_client_secret
CAPTCHA_KEY_NAME=captcha_key

print_title() {
    echo -e "\n\e[1;32m==== $1 ====\e[0m\n"
}

print_title "Starting Deploy Script"
# Init Swarm and Secrets
docker swarm init 2>/dev/null || true
# Initialize variables
CLIENT_ID_GOOGLE=""
CLIENT_SECRET_GOOGLE=""
CLIENT_ID_MICROSOFT=""
CLIENT_SECRET_MICROSOFT=""
MICROSOFT_TENANT="common"
KEYCLOAK_ADMIN_USER="admin"
KEYCLOAK_ADMIN_PASSWORD=""

SNAP_API_CLIENT_SECRET=""
CAPTCHA_KEY=""

# Parse arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --client-id-google) CLIENT_ID_GOOGLE="$2"; shift 2 ;;
    --client-secret-google) CLIENT_SECRET_GOOGLE="$2"; shift 2 ;;
    --client-id-microsoft) CLIENT_ID_MICROSOFT="$2"; shift 2 ;;
    --client-secret-microsoft) CLIENT_SECRET_MICROSOFT="$2"; shift 2 ;;
    --microsoft-tenant) MICROSOFT_TENANT="$2"; shift 2 ;;
    --keycloak-admin-user) KEYCLOAK_ADMIN_USER="$2"; shift 2 ;;
    --keycloak-admin-password) KEYCLOAK_ADMIN_PASSWORD="$2"; shift 2 ;;
    --snap-api-client-secret) SNAP_API_CLIENT_SECRET="$2"; shift 2 ;;
    --captcha-key) CAPTCHA_KEY="$2"; shift 2 ;;
    *) echo "❌ Unknown option: $1"; exit 1 ;;
  esac
done

# VALIDATION
[[ -z "$KEYCLOAK_ADMIN_PASSWORD" ]] && echo "❌ --keycloak-admin-password is required." && exit 1
[[ -z "$SNAP_API_CLIENT_SECRET" ]] && echo "❌ --snap-api-client-secret is required." && exit 1
[[ -z "$CAPTCHA_KEY" ]] && echo "❌ --captcha-key is required." && exit 1
echo "ℹ️ Using Keycloak admin username: $KEYCLOAK_ADMIN_USER"

# Validate OAuth
if { [ -n "$CLIENT_ID_GOOGLE" ] && [ -z "$CLIENT_SECRET_GOOGLE" ]; } || \
   { [ -z "$CLIENT_ID_GOOGLE" ] && [ -n "$CLIENT_SECRET_GOOGLE" ]; }; then
    echo "❌ Both --client-id-google and --client-secret-google must be provided together."; exit 1
fi

if [ "$MICROSOFT_TENANT" != "common" ]; then
    echo "🔐 Microsoft Tenant restriction: $MICROSOFT_TENANT"
    if [[ -z "$CLIENT_ID_MICROSOFT" || -z "$CLIENT_SECRET_MICROSOFT" ]]; then
        echo "❌ Tenant specified but Microsoft client creds missing."; exit 1
    fi
fi

if { [ -n "$CLIENT_ID_MICROSOFT" ] && [ -z "$CLIENT_SECRET_MICROSOFT" ]; } || \
   { [ -z "$CLIENT_ID_MICROSOFT" ] && [ -n "$CLIENT_SECRET_MICROSOFT" ]; }; then
    echo "❌ Both --client-id-microsoft and --client-secret-microsoft must be provided together."; exit 1
fi

if [[ -z "$CLIENT_ID_GOOGLE" && -z "$CLIENT_ID_MICROSOFT" ]]; then
    echo "❌ You must provide at least one provider (Google or Microsoft)."; exit 1
fi

echo "✅ Provider input validated successfully!"

# Remove stack
print_title "Removing existing Docker Stack: $STACK_NAME (if exists)"
docker stack rm "$STACK_NAME" || true


echo "Waiting for Docker stack '$STACK_NAME' to shut down..."
while true; do
  output=$(docker stack services "$STACK_NAME" 2>&1)
  if echo "$output" | grep -q "Nothing found in stack"; then
    echo "✅ Stack '$STACK_NAME' is fully removed."; break
  fi
  echo "Still shutting down..."; sleep 2
done

# Explicitly remove existing conflicting network
print_title "Removing existing Docker network: mystack-net (if exists)"
if docker network inspect mystack-net >/dev/null 2>&1; then
    echo "⚠️ Network mystack-net found. Attempting to remove..."

    # Disconnect any lingering containers
    containers=$(docker network inspect mystack-net -f '{{range .Containers}}{{.Name}} {{end}}')
    if [ -n "$containers" ]; then
        for container in $containers; do
            echo "🔍 Disconnecting container $container from mystack-net"
            docker network disconnect -f mystack-net "$container"
        done
    fi

    docker network rm mystack-net && echo "✅ Network mystack-net removal initiated." || \
        echo "❌ Failed to remove mystack-net (check manually)."

    # Explicit wait loop to ensure full removal
    echo "⏳ Waiting for network mystack-net to be fully removed..."
    while docker network inspect mystack-net >/dev/null 2>&1; do
        echo "Still removing network mystack-net..."; sleep 2
    done
    echo "✅ Network mystack-net fully removed."
else
    echo "✅ No existing mystack-net network found."
fi




create_secret() {
    local SECRET_NAME=$1
    local SECRET_VALUE=$2
    local TMP_FILE="tmp_${SECRET_NAME}.txt"

    echo -n "$SECRET_VALUE" > "$TMP_FILE"

    if docker secret ls --filter name="$SECRET_NAME" -q | grep -q .; then
        echo "⚠️ Secret $SECRET_NAME already exists. Skipping creation."
        rm "$TMP_FILE"
        return
    fi

    docker secret create "$SECRET_NAME" "$TMP_FILE"
    rm "$TMP_FILE"
    echo "✅ Docker secret $SECRET_NAME created!"
}


print_title "🔐 Checking client_secret_keycloak"
if docker secret ls --filter name="$CLIENT_SECRET_NAME" -q | grep -q .; then
  echo "🔒 Secret $CLIENT_SECRET_NAME already exists, skipping generation and injection."
  SKIP_KC_REALM_SECRET=true
else
  CLIENT_SECRET_KEYCLOAK=$(openssl rand -base64 32)

  # Delete secrets (except the one we may reuse)
  for secret in \
    keycloak_admin_user \
    keycloak_admin_password \
    snap_api_client_secret \
    captcha_key
  do
    if docker secret ls --filter name="$secret" -q | grep -q .; then
      echo "Removing secret: $secret"
      docker secret rm "$secret"
    else
      echo "Secret $secret not found, skipping."
    fi
  done

  echo "🆕 Generated CLIENT_SECRET_KEYCLOAK: $CLIENT_SECRET_KEYCLOAK"

  echo -n "$CLIENT_SECRET_KEYCLOAK" > tmp_client_secret.txt
  docker secret create "$CLIENT_SECRET_NAME" tmp_client_secret.txt
  rm tmp_client_secret.txt
  echo "✅ Secret $CLIENT_SECRET_NAME created."
  SKIP_KC_REALM_SECRET=false
fi


if [ "$SKIP_KC_REALM_SECRET" = false ]; then
  print_title "📦 Injecting secrets into realm.json"

  ESC_KC_SECRET=$(printf '%s\n' "$CLIENT_SECRET_KEYCLOAK" | sed 's/[&/\]/\\&/g')
  ESC_GOOGLE_ID=$(printf '%s\n' "$CLIENT_ID_GOOGLE" | sed 's/[&/\]/\\&/g')
  ESC_GOOGLE_SECRET=$(printf '%s\n' "$CLIENT_SECRET_GOOGLE" | sed 's/[&/\]/\\&/g')
  ESC_MS_ID=$(printf '%s\n' "$CLIENT_ID_MICROSOFT" | sed 's/[&/\]/\\&/g')
  ESC_MS_SECRET=$(printf '%s\n' "$CLIENT_SECRET_MICROSOFT" | sed 's/[&/\]/\\&/g')
  ESC_TENANT=$(printf '%s\n' "$MICROSOFT_TENANT" | sed 's/[&/\]/\\&/g')

  mkdir -p apps/keycloak

  sed -e "s/CLIENT_SECRET_KEYCLOAK/$ESC_KC_SECRET/g" \
      -e "s/CLIENT_ID_GOOGLE/$ESC_GOOGLE_ID/g" \
      -e "s/CLIENT_SECRET_GOOGLE/$ESC_GOOGLE_SECRET/g" \
      -e "s/CLIENT_ID_MICROSOFT/$ESC_MS_ID/g" \
      -e "s/CLIENT_SECRET_MICROSOFT/$ESC_MS_SECRET/g" \
      -e "s/MICROSOFT_TENANT/$ESC_TENANT/g" \
      apps/keycloak/realm.json.template > apps/keycloak/realm.json

  echo "✅ realm.json generated successfully"
else
  echo "ℹ️ Skipping realm.json update for Keycloak client secret."
fi





print_title "Creating Docker secrets"

# Always create these
create_secret "$ADMIN_USER_SECRET_NAME" "$KEYCLOAK_ADMIN_USER"
create_secret "$ADMIN_PASSWORD_SECRET_NAME" "$KEYCLOAK_ADMIN_PASSWORD"
create_secret "$SNAP_API_CLIENT_SECRET_NAME" "$SNAP_API_CLIENT_SECRET"
create_secret "$CAPTCHA_KEY_NAME" "$CAPTCHA_KEY"

# Only create the Keycloak client secret if it's new
if [ "$SKIP_KC_REALM_SECRET" = false ]; then
  create_secret "$CLIENT_SECRET_NAME" "$CLIENT_SECRET_KEYCLOAK"
fi


# Build Docker images
print_title "Building Docker Images"
DOCKER_BUILDKIT=0 docker build -t my-bff-image ./apps/bff
DOCKER_BUILDKIT=0 docker build --build-arg VITE_API_URL="http://***************:3001" --build-arg VITE_REPORTS_API_URL="http://***************:8000" -f apps/frontend/Dockerfile -t my-frontend-image .
# DOCKER_BUILDKIT=0 docker build -t my-backend-image ./apps/backend
DOCKER_BUILDKIT=0 docker build -f apps/backend/Dockerfile -t my-backend-image .
DOCKER_BUILDKIT=0 docker build -t my-keycloak-image ./apps/keycloak
DOCKER_BUILDKIT=0 docker build -t my-kafka-image ./apps/kafka
DOCKER_BUILDKIT=0 docker build -f apps/minio/Dockerfile -t my-minio-image .
DOCKER_BUILDKIT=0 docker build -f apps/spark/Dockerfile -t my-spark-image .

print_title "Force removing lingering mystack-net network (if exists)"

# Get the ID of the network
network_id=$(docker network ls --filter name="^mystack-net$" -q)

if [ -n "$network_id" ]; then
  echo "⚠️ Found network mystack-net with ID: $network_id"
  
  echo "🔍 Checking containers attached to mystack-net..."
  attached_containers=$(docker network inspect "$network_id" -f '{{range .Containers}}{{.Name}} {{end}}')

  for container in $attached_containers; do
    echo "⛔ Forcibly removing container $container from network"
    docker rm -f "$container" || true
  done

  echo "🧹 Now removing the network..."
  docker network rm "$network_id" && echo "✅ Network removed." || echo "❌ Failed to remove network."
else
  echo "✅ No network mystack-net found."
fi

sleep 3

# Deploy
print_title "Deploying Docker Stack: $STACK_NAME"
# docker stack deploy -c docker-compose.yml "$STACK_NAME"

COMPOSE_FILES=(
  docker-compose.yml
  docker-compose-postgres.yml
  docker-compose-backend.yml
  docker-compose-keycloak.yml
  docker-compose-bff.yml
  docker-compose-frontend.yml
  docker-compose-ngrok.yml
  docker-compose-kafka.yml
  docker-compose-minio.yml
  docker-compose-spark.yml
)

DEPLOY_CMD="docker stack deploy"
for file in "${COMPOSE_FILES[@]}"; do
  DEPLOY_CMD+=" -c $file"
done
DEPLOY_CMD+=" $STACK_NAME"

print_title "Deploying Docker Stack: $STACK_NAME"
eval "$DEPLOY_CMD"



print_title "Docker Stack Services Status"
docker stack services "$STACK_NAME"

echo -e "\n\e[1;34m✅ Deployment completed successfully!\e[0m\n"
echo "🔐 Keycloak admin username is stored as a Docker secret!"
