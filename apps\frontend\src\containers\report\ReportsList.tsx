import ReportCard from "~/containers/report/ReportCard";
import { ReportData } from "~/types/global";
import { AnimatedFilledButton, Button, Icon } from "@snap/design-system";
import { Plus } from "lucide-react";
import MasonryLayout from "react-layout-masonry";
import { useEffect, useRef, useState } from "react";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useReportListActions, useReportListLoadMore } from "~/store/reportListStore";
import EmptyList from "~/components/EmptyList";

interface ReportListProps {
  list: ReportData[];
  isFetched: boolean;
  onNewReport: () => void;
}

export default function ReportsList({
  list,
  onNewReport,
  isFetched,
}: ReportListProps) {
  const { invalidateReports } = useReportCRUD();
  const { incrementPage } = useReportListActions();
  const loadMore = useReportListLoadMore();
  const [showLoadMore, setShowLoadMore] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  const columnsBreakpoints = {
    300: 1,
    664: 2,
    996: 3,
    1256: 4,
    1800: 5,
    1900: 6,
    2460: 8,
    3756: 10,
  };

  useEffect(() => {
    const section = sectionRef.current;
    if (section) {
      section.addEventListener("scroll", handleScroll);
      return () => section.removeEventListener("scroll", handleScroll);
    }
  }, [loadMore]);

  const loadMoreData = async () => {
    incrementPage()
  }

  const handleLoadMore = async () => {
    console.log("load more");
    await loadMoreData();
    invalidateReports();
  };

  const handleScroll = () => {
    if (!sectionRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = sectionRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 100;
    const isAtTop = scrollTop === 0;
    setShowLoadMore(isAtBottom && !isAtTop);
  };

  return (
    <section
      ref={sectionRef}
      className="flex-1 w-full overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-232px)] px-8 pt-2 pb-8"
    >
      <MasonryLayout columns={columnsBreakpoints} gap={32} className="relative">
        <AnimatedFilledButton
          data-testid="button-new-report"
          onClick={onNewReport}
          icon={<Icon src="/icons/icone_bigplus.svg" />}
        >
          <div className="flex flex-col items-start">
            <p className="text-2xl">Clique para criar:</p>
            <p className="text-2xl font-bold">Novo relatório</p>
            <div className="justify-self-end">
              <Plus size="20%" />
            </div>
          </div>
        </AnimatedFilledButton>
        {isFetched &&
          list?.length > 0 &&
          list.map((item, index) => (
            <div className="z-[1]" key={index}>
              <ReportCard report={item} />
            </div>
          ))}
      </MasonryLayout>
      {isFetched && list?.length === 0 && (
        <EmptyList onReload={() => { }} />
      )}

      {showLoadMore && loadMore && (
        <div className="w-full absolute bottom-2 left-0 flex justify-center py-4 px-8 z-[9999]">
          <Button
            variant="default"
            className="w-full uppercase"
            onClick={handleLoadMore}
          >
            Carregar mais resultados
          </Button>
        </div>
      )}
    </section>
  );
}
