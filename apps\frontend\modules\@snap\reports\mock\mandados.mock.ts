import { Mandado } from "../model/Mandados";

export const mandad<PERSON>Mock: Mandado[] = [
    {
        numero: {
            value: "0001234-56.2023.8.19.0001.01.0001-01",
            label: "Número do Mandado",
            source: ["Banco Nacional de Monitoramento de Prisões"],
            is_deleted: false
        },
        detalhes: {
            "tipo de mandado": {
                value: "Preventiva",
                label: "Tipo de Mandado",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            situacao: {
                value: "Pendente de Cumprimento",
                label: "Situação",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "numero do processo": {
                value: "0001234-56.2023.8.19.0001",
                label: "Número do Processo",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "tipificacoes penais": {
                value: "Art. 157, § 2º, I e II do Código Penal - Rou<PERSON> qualificado",
                label: "Tipificações Penais",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "data de expedicao": {
                value: "15/03/2023",
                label: "Data de Expedição",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "data de validade": {
                value: "15/03/2043",
                label: "Data de Validade",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "especie de prisao": {
                value: "Prisão Preventiva",
                label: "Espécie de Prisão",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            municipio: {
                value: "Rio de Janeiro",
                label: "Município",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "orgao expedidor": {
                value: "Vara Criminal da Comarca do Rio de Janeiro",
                label: "Órgão Expedidor",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            magistrado: {
                value: "Dr. João Silva",
                label: "Magistrado",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            }
        },
        pena: {
            value: {
                "pena imposta": {
                    value: "8 anos e 6 meses de reclusão em regime fechado",
                    label: "Pena Imposta",
                    source: ["Banco Nacional de Monitoramento de Prisões"],
                    is_deleted: false
                },
                recaptura: {
                    value: "Não",
                    label: "Recaptura",
                    source: ["Banco Nacional de Monitoramento de Prisões"],
                    is_deleted: false
                },
            },
            label: "Pena",
            source: ["Banco Nacional de Monitoramento de Prisões"],
            is_deleted: false
        }
    },
    {
        numero: {
            value: "0007890-12.2022.8.26.0001.01.0002-01",
            label: "Número do Mandado",
            source: ["Banco Nacional de Monitoramento de Prisões"],
            is_deleted: false
        },
        detalhes: {
            "tipo de mandado": {
                value: "Definitiva",
                label: "Tipo de Mandado",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            situacao: {
                value: "Cumprido",
                label: "Situação",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "numero do processo": {
                value: "0007890-12.2022.8.26.0001",
                label: "Número do Processo",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "tipificacoes penais": {
                value: "Art. 33 da Lei 11.343/06 - Tráfico de Drogas",
                label: "Tipificações Penais",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "data de expedicao": {
                value: "10/11/2022",
                label: "Data de Expedição",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "data de validade": {
                value: "10/11/2042",
                label: "Data de Validade",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "especie de prisao": {
                value: "Prisão Definitiva",
                label: "Espécie de Prisão",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            municipio: {
                value: "São Paulo",
                label: "Município",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            "orgao expedidor": {
                value: "2ª Vara de Execuções Criminais de São Paulo",
                label: "Órgão Expedidor",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            },
            magistrado: {
                value: "Dra. Maria Oliveira",
                label: "Magistrado",
                source: ["Banco Nacional de Monitoramento de Prisões"],
                is_deleted: false
            }
        },
        pena: {
            value: {
                "pena imposta": {
                    value: "5 anos e 10 meses de reclusão em regime semi-aberto",
                    label: "Pena Imposta",
                    source: ["Banco Nacional de Monitoramento de Prisões"],
                    is_deleted: false
                },
                recaptura: {
                    value: "Sim",
                    label: "Recaptura",
                    source: ["Banco Nacional de Monitoramento de Prisões"],
                    is_deleted: false
                },
            },
            label: "Pena",
            source: ["Banco Nacional de Monitoramento de Prisões"],
            is_deleted: false
        }
    }
];