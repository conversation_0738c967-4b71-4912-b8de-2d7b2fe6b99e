import { <PERSON>rowserRouter } from "react-router";
import Router from "~/components/router/Router";
import ReactQueryWrapper from "./components/ReactQueryWrapper";
import { ThemeProvider } from "~/context/themeContext";
import { AuthEventHandler } from "./components/AuthEventHandler";
import { Toaster } from "~/components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import "./index.css";

const App = () => {
  return (
    <ReactQueryWrapper>
      <ThemeProvider>
        <TooltipProvider>
          <BrowserRouter>
            <AuthEventHandler />
            <Router />
          </BrowserRouter>
          <Toaster />
        </TooltipProvider>
      </ThemeProvider>
    </ReactQueryWrapper>
  );
};

export default App;
