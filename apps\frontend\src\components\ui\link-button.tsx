import React from "react";
import { cn } from "~/lib/utils"
import { Lu<PERSON>rrowRight, LuArrowBigLeft } from "react-icons/lu";
import { Link } from "react-router";

export interface AnchorProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  iconStart?: boolean;
  iconEnd?: boolean;
}

export function LinkButton({
  href,
  className,
  children,
  iconStart,
  iconEnd,
  ...restProps
}: AnchorProps): React.JSX.Element {
  return (
    <Link
      to={href ?? ""}
      className={cn(
        // Base styles
        "inline-flex items-center justify-center gap-2 border-2 border-primary bg-background px-6 py-2",
        "font-mono uppercase tracking-wider text-primary transition-all duration-300",
        // Hover animation for the whole button
        "hover:bg-primary hover:text-primary-foreground",
        // Icon animation classes
        iconStart && "group",
        iconEnd && "group",
        className
      )}
      {...restProps}
    >
      {iconStart && (
        <LuArrowBigLeft
          className={cn(
            "h-4 w-4 transition-transform duration-300",
            "group-hover:-translate-x-1"
          )}
        />
      )}
      {children}
      {iconEnd && (
        <LuArrowRight
          className={cn(
            "h-4 w-4 transition-transform duration-300",
            "group-hover:translate-x-1"
          )}
        />
      )}
    </Link>
  );
}
