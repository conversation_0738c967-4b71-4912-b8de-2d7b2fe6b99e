import {
  Avatar,
  List,
  Separator,
  Text,
  UserMenu as UserProfile,
} from "@snap/design-system";
import { getInitials } from "~/helpers";
import { useUserData } from "~/store/userStore";

interface UserMenuProps {
  userMenuContent: React.ReactNode;
}

export default function UserMenu({ userMenuContent }: UserMenuProps) {
  const userData = useUserData();
  const initials = getInitials(userData?.name);

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  const renderUserProfile = () => {
    const profileProps = {
      Profile: (
        <div className="flex items-center gap-3 w-full">
          <Avatar
            size="sm"
            className="size-9"
            src={getValidImageUrl(userData?.image)}
            fallback={initials || "NA"}
            textAlign="left"
          />
          <List className="space-y-0.5 items-start">
            <span className="text-sm leading-tight">
              {userData?.name || "Sem nome"}
            </span>
            <Separator />
            <Text className="opacity-80">
              {`${userData?.credits} Consultas`}
            </Text>
          </List>
        </div>
      ),
      Menu: userMenuContent,
    };

    return <UserProfile {...profileProps} menuClassName="py-0 px-0" />;
  };

  return renderUserProfile();
}
