{"user_reports_id": "fea0a198-6110-4621-9ef0-16b667317492", "report_status": "success", "report_type": "cpf", "report_search_args": {"cpf": "12345678910"}, "report_name": "cpf 12345678910", "creation_at": "2025-05-19T20:20:56.920580+00:00", "modified_at": "2025-05-19T20:20:56.920580+00:00", "subject_name": "<PERSON>", "subject_mother_name": "<PERSON>", "subject_age": 35, "subject_sex": "M", "data": {"cpf": [{"title": "<PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 23, "data": [{"detalhes": {"full name": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "nome_da_mae": {"value": "<PERSON>", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "idade": {"value": "35", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}, "nacionalidade": {"value": "Brasileira", "label": "Nacionalidade", "source": ["SNAP"], "is_deleted": false}, "pais do passaporte": {"value": "Brasil", "label": "País do Passaporte", "source": ["SNAP"], "is_deleted": false}, "data nascimento": {"value": "10/05/1989", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "123.456.789-10", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "identidade": {"value": "MG-12.345.678", "label": "Identidade", "source": ["SNAP"], "is_deleted": false}, "estadocivil": {"value": "<PERSON><PERSON>iro", "label": "Estado Civil", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "Ativo", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "info restricao": {"value": "Sem restrições", "label": "Informação de Restrição", "source": ["SNAP"], "is_deleted": false}, "titulo de eleitor": {"value": "1234 5678 9012", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "pis/pasep": {"value": "123.45678.90-1", "label": "PIS/PASEP", "source": ["SNAP"], "is_deleted": false}, "ctps": {"value": "123456 - SP", "label": "CTPS", "source": ["SNAP"], "is_deleted": false}, "procon": {"value": "<PERSON><PERSON><PERSON>", "label": "Procon", "source": ["SNAP"], "is_deleted": false}, "escolaridade": {"value": "Superior Completo", "label": "Escolaridade", "source": ["SNAP"], "is_deleted": false}, "grauinstrucao": {"value": "Graduação", "label": "Grau de Instrução", "source": ["SNAP"], "is_deleted": false}, "descricao cbo": {"value": "Desenvolvedor de Software", "label": "Descrição CBO", "source": ["SNAP"], "is_deleted": false}, "cbo": {"value": "2123-05", "label": "CBO", "source": ["SNAP"], "is_deleted": false}, "renda estimada": {"value": "R$ 5.000,00", "label": "Renda Estimada", "source": ["SNAP"], "is_deleted": false}, "renda presumida": {"value": "R$ 5.000,00", "label": "<PERSON><PERSON> Pre<PERSON>mi<PERSON>", "source": ["SNAP"], "is_deleted": false}, "data de admissao": {"value": "2020-01-15", "label": "Data de Admissão", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Mandados de Prisão", "subtitle": "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP).", "source": ["BancoNacionalDeMonitoramentoDePrisoes"], "data_count": 2, "data": [{"numero": {"value": "0001234-56.2023.8.19.0001.01.0001-01", "label": "Número do Mandado", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "detalhes": {"tipo de mandado": {"value": "Preventiva", "label": "Tipo de Mandado", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "situacao": {"value": "Pendente de Cumprimento", "label": "Situação", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "numero do processo": {"value": "0001234-56.2023.8.19.0001", "label": "Número do Processo", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "tipificacoes penais": {"value": "Art. 157, § 2º, I e II do Código Penal - Roubo qualificado", "label": "Tipificaçõ<PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "data de expedicao": {"value": "15/03/2023", "label": "Data de Expedição", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "data de validade": {"value": "15/03/2043", "label": "Data de Validade", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "especie de prisao": {"value": "Prisão Preventiva", "label": "Espécie de Prisão", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "municipio": {"value": "Rio de Janeiro", "label": "Município", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "orgao expedidor": {"value": "Vara Criminal da Comarca do Rio de Janeiro", "label": "Órgão Expedidor", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "magistrado": {"value": "Dr. <PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}, "pena": {"value": {"pena imposta": {"value": "8 anos e 6 meses de reclusão em regime fechado", "label": "<PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "recaptura": {"value": "Não", "label": "Recaptura", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}, {"numero": {"value": "0007890-12.2022.8.26.0001.01.0002-01", "label": "Número do Mandado", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "detalhes": {"tipo de mandado": {"value": "Definitiva", "label": "Tipo de Mandado", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "situacao": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Situação", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "numero do processo": {"value": "0007890-12.2022.8.26.0001", "label": "Número do Processo", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "tipificacoes penais": {"value": "Art. 33 da <PERSON><PERSON> 11.343/06 - <PERSON><PERSON><PERSON><PERSON><PERSON> de Drogas", "label": "Tipificaçõ<PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "data de expedicao": {"value": "10/11/2022", "label": "Data de Expedição", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "data de validade": {"value": "10/11/2042", "label": "Data de Validade", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "especie de prisao": {"value": "<PERSON><PERSON><PERSON> Definitiva", "label": "Espécie de Prisão", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "municipio": {"value": "São Paulo", "label": "Município", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "orgao expedidor": {"value": "2ª Vara de Execuções Criminais de São Paulo", "label": "Órgão Expedidor", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "magistrado": {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}, "pena": {"value": {"pena imposta": {"value": "5 anos e 10 meses de reclusão em regime semi-aberto", "label": "<PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}, "recaptura": {"value": "<PERSON>m", "label": "Recaptura", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Banco Nacional de Monitoramento de Prisões"], "is_deleted": false}}]}, {"title": "Telefones", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 4, "data": [{"detalhes": [{"value": {"phone number": {"value": "31991701966", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "31973113426", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "21976886755", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "21988563455", "label": "Número do Telefone", "source": ["SintegraMA"], "is_deleted": false}}, "label": "Telefone", "source": ["SintegraMA"], "is_deleted": false}]}]}, {"title": "Emails", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 2, "data": [{"detalhes": [{"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}, {"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Endereços", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 2, "data": [{"detalhes": [{"value": {"logradouro": {"value": "<PERSON><PERSON>", "label": "Logradouro", "source": ["Cadastro Nacional"], "is_deleted": false}, "numero": {"value": "123", "label": "Número", "source": ["Cadastro Nacional"], "is_deleted": false}, "complemento": {"value": "Apto 101", "label": "Complemento", "source": ["Cadastro Nacional"], "is_deleted": false}, "bairro": {"value": "<PERSON><PERSON><PERSON>", "label": "Bairro", "source": ["Cadastro Nacional"], "is_deleted": false}, "city": {"value": "São Paulo", "label": "Cidade", "source": ["Cadastro Nacional"], "is_deleted": false}, "area": {"value": "Zona Sul", "label": "Á<PERSON>", "source": ["Cadastro Nacional"], "is_deleted": false}, "cep ou zipcode": {"value": "01234-567", "label": "CEP", "source": ["Cadastro Nacional"], "is_deleted": false}, "cidade": {"value": "São Paulo", "label": "Cidade", "source": ["Cadastro Nacional"], "is_deleted": false}, "estado ou regiao": {"value": "SP", "label": "Estado ou Região", "source": ["Cadastro Nacional"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}, {"value": {"logradouro": {"value": "Avenida <PERSON>", "label": "Logradouro", "source": ["Cadastro Nacional"], "is_deleted": false}, "numero": {"value": "1578", "label": "Número", "source": ["Cadastro Nacional"], "is_deleted": false}, "complemento": {"value": "Sala 304", "label": "Complemento", "source": ["Cadastro Nacional"], "is_deleted": false}, "bairro": {"value": "Bela Vista", "label": "Bairro", "source": ["Cadastro Nacional"], "is_deleted": false}, "area": {"value": "Centro", "label": "Á<PERSON>", "source": ["Cadastro Nacional"], "is_deleted": false}, "cep ou zipcode": {"value": "01310-200", "label": "CEP", "source": ["Cadastro Nacional"], "is_deleted": false}, "cidade": {"value": "São Paulo", "label": "Cidade", "source": ["Cadastro Nacional"], "is_deleted": false}, "estado ou regiao": {"value": "SP", "label": "Estado ou Região", "source": ["Cadastro Nacional"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Parentes", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "BancoNacionalDeMonitoramentoDePrisoes"], "data_count": 2, "data": [{"parentesco": {"value": "parente MAE", "label": "Parentesco", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"full name": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "nome1": {"value": "<PERSON>", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "titulo de eleitor": {"value": "1234 5678 9012", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "data nascimento": {"value": "01/01/1970", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "Feminino", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}, "procon": {"value": "Não consta", "label": "PROCON", "source": ["SNAP"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "31991701966", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "31973113426", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}], "enderecos": [{"value": {"logradouro": {"value": "<PERSON><PERSON>", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "123", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "Casa 101", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "<PERSON><PERSON><PERSON>", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "SP", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "01234-567", "label": "CEP", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}, {"parentesco": {"value": "parente PAI", "label": "Parentesco", "source": ["BancoNacionalDeMonitoramentoDePrisoes"]}, "detalhes": {"full name": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Sociedades", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"], "data_count": 2, "data": [{"razao_social": {"value": "Empresa ABC Ltda.", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "procon": {"value": "Não consta", "label": "PROCON", "source": ["SNAP"], "is_deleted": false}, "info restricao": {"value": "Nada consta", "label": "Info Restrição", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "Ativo", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "data de fundacao": {"value": "01/01/2000", "label": "Data de Fundação", "source": ["SNAP"], "is_deleted": false}}, "enderecos": [{"value": {"logradouro": {"value": "<PERSON><PERSON>", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "123", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "Apto 101", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "<PERSON><PERSON><PERSON>", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "São Paulo", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "Zona Sul", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "01234-567", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "area code": {"value": "11", "label": "Código <PERSON>", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "98765-4321", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "Residencial", "label": "Tipo", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}, {"value": {"logradouro": {"value": "Avenida <PERSON>", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "1578", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "Sala 304", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "Bela Vista", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "São Paulo", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "Centro", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "01310-200", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "area code": {"value": "11", "label": "Código <PERSON>", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "3456-7890", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "Comercial", "label": "Tipo", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}], "telefones": [{"value": {"phone number": {"value": "(11) 98765-4321", "label": "Número de Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "(11) 3456-7890", "label": "Número de Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}], "emails": [{"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}, {"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email", "source": ["SNAP"], "is_deleted": false}]}, {"razao_social": {"value": "Tech Inovação S.A.", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "procon": {"value": "Não consta", "label": "PROCON", "source": ["SNAP"], "is_deleted": false}, "info restricao": {"value": "Nada consta", "label": "Info Restrição", "source": ["SNAP"], "is_deleted": false}, "status receita": {"value": "Ativo", "label": "Status na Receita", "source": ["SNAP"], "is_deleted": false}, "data de fundacao": {"value": "15/03/2015", "label": "Data de Fundação", "source": ["SNAP"], "is_deleted": false}, "data_abertura": {"value": "15/03/2015", "label": "Data de Abertura", "source": ["SNAP"], "is_deleted": false}, "sequencial": {"value": "9876543210", "label": "Sequencial", "source": ["SNAP"], "is_deleted": false}, "porte": {"value": "GRANDE", "label": "Porte", "source": ["SNAP"], "is_deleted": false}, "tipo de imposto": {"value": "LUCRO REAL", "label": "Tipo de Imposto", "source": ["SNAP"], "is_deleted": false}, "total de funcionarios": {"value": "250", "label": "Total de Funcionários", "source": ["SNAP"], "is_deleted": false}, "quantidade de funcionarios acima de 5 salarios": {"value": "75", "label": "Funcionários acima de 5 salários", "source": ["SNAP"], "is_deleted": false}, "quantidade de funcionarios abaixo de 5 salarios": {"value": "175", "label": "Funcionários abaixo de 5 salários", "source": ["SNAP"], "is_deleted": false}, "cnae": {"value": "6209-1/00", "label": "CNAE", "source": ["SNAP"], "is_deleted": false}, "descricao do cnae": {"value": "Suporte técnico, manutenção e outros serviços em tecnologia da informação", "label": "Descrição do CNAE", "source": ["SNAP"], "is_deleted": false}, "cnae1": {"value": "6209-1/00", "label": "CNAE 1", "source": ["SNAP"], "is_deleted": false}, "cnae2": {"value": "6311-9/00", "label": "CNAE 2", "source": ["SNAP"], "is_deleted": false}, "cnae secundario": {"value": "6311-9/00", "label": "CNAE Secundário", "source": ["SNAP"], "is_deleted": false}, "cnae secundario2": {"value": "6202-3/00", "label": "CNAE Secundário 2", "source": ["SNAP"], "is_deleted": false}}, "enderecos": [{"value": {"logradouro": {"value": "Avenida <PERSON>", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "1500", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "Andar 15", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "Bela Vista", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "São Paulo", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "Centro", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "01310-200", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "area code": {"value": "11", "label": "Código <PERSON>", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "3456-7890", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "<PERSON><PERSON>", "label": "Tipo", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço Matriz", "source": ["SNAP"], "is_deleted": false}, {"value": {"logradouro": {"value": "Rua da Tecnologia", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "789", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "Bloco B", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "Alphaville", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "<PERSON><PERSON><PERSON>", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "Grande São Paulo", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "06454-000", "label": "CEP", "source": ["SNAP"], "is_deleted": false}, "area code": {"value": "11", "label": "Código <PERSON>", "source": ["SNAP"], "is_deleted": false}, "telefone relacionado": {"value": "4567-8901", "label": "Telefone Relacionado", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "Filial", "label": "Tipo", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço Filial", "source": ["SNAP"], "is_deleted": false}], "telefones": [{"value": {"phone number": {"value": "(11) 3456-7890", "label": "Número de Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone Matriz", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "(11) 4567-8901", "label": "Número de Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone Filial", "source": ["SNAP"], "is_deleted": false}, {"value": {"phone number": {"value": "(11) 99876-5432", "label": "Número de Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone Celular", "source": ["SNAP"], "is_deleted": false}], "emails": [{"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email <PERSON>iro", "source": ["SNAP"], "is_deleted": false}, {"value": {"email address": {"value": "<EMAIL>", "label": "Email", "source": ["SNAP"], "is_deleted": false}}, "label": "Email RH", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 7, "data": [{"detalhes": [{"value": {"full name": {"value": "GIOVANI THIBAU CHRISTOFARO", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO ADMINISTRADOR", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "68000367653", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "04/09/2006", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "JAIME RODRIGUES BARBOSA NETO", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO ADMINISTRADOR", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "03770139607", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "08/09/2016", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "LUCIANA BISPO DA SILVA GALAO", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84421630187", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "LUIZ HENRIQUE DE SOUZA BORGES", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84426249607", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "LUIZ MARCOS DE ALMEIDA BATISTA", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO ADMINISTRADOR", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84400140607", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "MARCIO JOSE ROSA GOMES", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84422497104", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, {"value": {"full name": {"value": "RAFAEL VELASQUEZ SAAVEDRA DA SILVA", "label": "Nome <PERSON>to", "source": ["SNAP"], "is_deleted": false}, "cargo em sociedade": {"value": "SOCIO", "label": "Cargo em Sociedade", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "84456010607", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "entradasociedade": {"value": "23/10/2017", "label": "Entrada em Sociedade", "source": ["SNAP"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 5, "data": [{"empresa_pagadora": {"value": "STELLANTIS AUTOMOVEIS BRASIL LTDA", "label": "Empres<PERSON>", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "16.701.716/0001-56", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "data admissao": {"value": "23/04/2015", "label": "Ad<PERSON><PERSON>ão", "source": ["SNAP"], "is_deleted": false}, "valor": {"value": "R$ 8.500,00", "label": "Remuneração (R$)", "source": ["SNAP"], "is_deleted": false}, "razao social": {"value": "STELLANTIS AUTOMOVEIS BRASIL LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "situacao": {"value": "Ativo", "label": "Situação", "source": ["SNAP"], "is_deleted": false}, "cargo": {"value": "<PERSON><PERSON><PERSON> de Si<PERSON>mas", "label": "Cargo", "source": ["SNAP"], "is_deleted": false}}}, {"empresa_pagadora": {"value": "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA", "label": "Empres<PERSON>", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "03.492.162/0001-82", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "data admissao": {"value": "01/11/2017", "label": "Ad<PERSON><PERSON>ão", "source": ["SNAP"], "is_deleted": false}, "valor": {"value": "R$ 2.089,92", "label": "Remuneração (R$)", "source": ["SNAP"], "is_deleted": false}, "razao social": {"value": "POLIGONAL ENGENHARIA E CONSTRUCOES LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "situacao": {"value": "Inativo", "label": "Situação", "source": ["SNAP"], "is_deleted": false}, "cargo": {"value": "Assistente Administrativo", "label": "Cargo", "source": ["SNAP"], "is_deleted": false}, "data demissao": {"value": "15/03/2019", "label": "Data de Demissão", "source": ["SNAP"], "is_deleted": false}}}, {"empresa_pagadora": {"value": "NAVI CARNES INDUSTRIA E COMERCIO LTDA", "label": "Empres<PERSON>", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "02.982.267/0003-19", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "data admissao": {"value": "08/02/2010", "label": "Ad<PERSON><PERSON>ão", "source": ["SNAP"], "is_deleted": false}, "valor": {"value": "R$ 540,60", "label": "Remuneração (R$)", "source": ["SNAP"], "is_deleted": false}, "razao social": {"value": "NAVI CARNES INDUSTRIA E COMERCIO LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "situacao": {"value": "Inativo", "label": "Situação", "source": ["SNAP"], "is_deleted": false}, "cargo": {"value": "Auxiliar de Produção", "label": "Cargo", "source": ["SNAP"], "is_deleted": false}, "data demissao": {"value": "10/08/2010", "label": "Data de Demissão", "source": ["SNAP"], "is_deleted": false}}}, {"empresa_pagadora": {"value": "MUNICIPIO DE ROCHEDO", "label": "Empres<PERSON>", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "03.501.566/0001-95", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "data admissao": {"value": "11/05/2010", "label": "Ad<PERSON><PERSON>ão", "source": ["SNAP"], "is_deleted": false}, "valor": {"value": "R$ 577,08", "label": "Remuneração (R$)", "source": ["SNAP"], "is_deleted": false}, "razao social": {"value": "PREFEITURA MUNICIPAL DE ROCHEDO", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "situacao": {"value": "Inativo", "label": "Situação", "source": ["SNAP"], "is_deleted": false}, "cargo": {"value": "Assistente Administrativo", "label": "Cargo", "source": ["SNAP"], "is_deleted": false}, "data demissao": {"value": "30/12/2010", "label": "Data de Demissão", "source": ["SNAP"], "is_deleted": false}}}, {"empresa_pagadora": {"value": "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA", "label": "Empres<PERSON>", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "19.314.695/0001-04", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "data admissao": {"value": "13/11/2014", "label": "Ad<PERSON><PERSON>ão", "source": ["SNAP"], "is_deleted": false}, "valor": {"value": "R$ 900,00", "label": "Remuneração (R$)", "source": ["SNAP"], "is_deleted": false}, "razao social": {"value": "PAULISTAO COMERCIO DE UTILIDADES DOMESTICAS LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "situacao": {"value": "Inativo", "label": "Situação", "source": ["SNAP"], "is_deleted": false}, "cargo": {"value": "<PERSON><PERSON><PERSON>", "label": "Cargo", "source": ["SNAP"], "is_deleted": false}, "data demissao": {"value": "05/03/2015", "label": "Data de Demissão", "source": ["SNAP"], "is_deleted": false}}}]}, {"title": "Processos", "subtitle": "Dados consultados na API SNAP.", "source": ["Escavador"], "data_count": 2, "data": [{"numero": {"value": "0001234-56.2023.8.26.0100", "label": "Número do Processo", "source": ["Escavador"], "is_deleted": false}, "detalhes": {"instancia": {"value": "1ª Instância", "label": "Instância", "source": ["Escavador"], "is_deleted": false}, "orgao": {"value": "Tribunal de Justiça de São Paulo", "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, "data da remessa": {"value": "2023-05-15", "label": "<PERSON> da Remessa", "source": ["Escavador"], "is_deleted": false}, "data da instauracao": {"value": "2023-01-10", "label": "Data da Instauração", "source": ["Escavador"], "is_deleted": false}}, "movimentacoes": {"value": "ata - Conteudo - Tipo,2025-01-21 - Intimação do <PERSON><PERSON> <PERSON>, <PERSON><PERSON>. <PERSON><PERSON>, Dra. <PERSON> Mo<PERSON>s e Dr<PERSON> <PERSON> Barros Vidal do despacho de f. 4448, que segue transcrito: “Ciência às partes acerca do Acórdão de f. 4426-39. <PERSON><PERSON><PERSON>, cumpram-se as determinações contidas na sentença de f. 4122-47. Às providências necessárias.\" Campo Grande, 13 de janeiro de 2025. CARLOS ALBERTO GARCETE Juiz de Direito em Substituição Legal - PUBLICACAO,2024-12-02 - <PERSON><PERSON><PERSON> <PERSON> Grande - 2ª Vara do Tribunal do Júri EMENTA - APELAÇÃO CRIMINAL - HOMICÍDIO QUALIFICADO - RECURSO INTERPOSTO PELO MINISTÉRIO PÚBLICO ESTADUAL - IMPRONÚNCIA MANTIDA - AUSÊNCIA DE INDÍCIOS MÍNIMOS E SUFICIENTES APTOS A CONVENCER DA MATERIALIDADE DO FATO E DA EXISTÊNCIA DE INDÍCIOS SUFICIENTES DA AUTORIA/PARTICIPAÇÃO - RECURSO DESPROVIDO. 1. <PERSON> bem sabe, o sistema processual penal brasileiro adota duas fases distintas para o processamento dos crimes dolosos contra a vida: a primeira, denominada de jus accusationis, tem objetivo voltado ao juízo de admissibilidade da acusação; já a segunda", "label": "Movimentações", "source": ["Escavador"], "is_deleted": false}, "advogado": [{"value": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 12345", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 54321", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}], "autor_pessoa": [{"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Autor", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "444.555.666-77", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Autor", "source": ["Escavador"], "is_deleted": false}], "reu_empresa": [{"value": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa ABC Ltda.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, {"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}], "juiz": [{"value": {"cpf": {"value": "555.666.777-88", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "Dr. <PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "999.888.777-66", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "Dr. <PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "<PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}], "representante_pessoa": [{"value": {"cpf": {"value": "999.888.777-66", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Representante", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Representante", "source": ["Escavador"], "is_deleted": false}]}, {"numero": {"value": "0007890-12.2022.8.26.0200", "label": "Número do Processo", "source": ["Escavador"], "is_deleted": false}, "detalhes": {"instancia": {"value": "2ª Instância", "label": "Instância", "source": ["Escavador"], "is_deleted": false}, "orgao": {"value": "Tribunal de Justiça de São Paulo", "label": "<PERSON><PERSON><PERSON>", "source": ["Escavador"], "is_deleted": false}, "data da remessa": {"value": "2022-11-20", "label": "<PERSON> da Remessa", "source": ["Escavador"], "is_deleted": false}, "data da instauracao": {"value": "2022-08-05", "label": "Data da Instauração", "source": ["Escavador"], "is_deleted": false}}, "movimentacoes": {"value": "20/11/2022 - Recurso recebido pelo Tribunal\n15/10/2022 - Recurso interposto pela parte autora\n30/09/2022 - Sentença proferida: procedente em parte\n15/09/2022 - Conclusos para sentença\n05/08/2022 - Distribuição do processo", "label": "Movimentações", "source": ["Escavador"], "is_deleted": false}, "advogado": [{"value": {"cpf": {"value": "222.333.444-55", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 78901", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "555.666.777-88", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}, "oab": {"value": "OAB/SP 54321", "label": "OAB", "source": ["Escavador"], "is_deleted": false}}, "label": "Advogado", "source": ["Escavador"], "is_deleted": false}], "recorrente_empresa": [{"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrente", "source": ["Escavador"], "is_deleted": false}, {"value": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["Escavador"], "is_deleted": false}, "razao social": {"value": "Empresa XYZ S.A.", "label": "Razão Social", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrente", "source": ["Escavador"], "is_deleted": false}], "recorrido_pessoa": [{"value": {"cpf": {"value": "444.555.666-77", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrido", "source": ["Escavador"], "is_deleted": false}, {"value": {"cpf": {"value": "111.222.333-44", "label": "CPF", "source": ["Escavador"], "is_deleted": false}, "full name": {"value": "<PERSON>", "label": "Nome", "source": ["Escavador"], "is_deleted": false}}, "label": "Recorrido", "source": ["Escavador"], "is_deleted": false}]}]}, {"title": "Recursos Públicos Recebidos", "subtitle": "Dados consultados em portais de Transparência estaduais e federal.", "source": ["PortalDaTransparenciaDeMinasGerais", "PortalDaTransparenciaDoAmazonas", "TransparenciaSC", "TransparenciaManausCPF", "TransparenciaDF"], "data_count": 7, "data": [{"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "detalhes": {"valor pago": {"value": "R$ 15.750,00", "label": "<PERSON><PERSON> (R$)", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor empenhado": {"value": "R$ 16.000,00", "label": "<PERSON><PERSON>", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor liquidado": {"value": "R$ 15.750,00", "label": "Valor Liquidado", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "detalhes": {"valor pago": {"value": "R$ 30.950,00", "label": "<PERSON><PERSON> (R$)", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor empenhado": {"value": "R$ 28.000,00", "label": "<PERSON><PERSON>", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}, "valor liquidado": {"value": "R$ 30.950,00", "label": "Valor Liquidado", "source": ["PortalDaTransparenciaDeMinasGerais"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "detalhes": {"nome do orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "codigo do orgao": {"value": "00123", "label": "Código do Orgão", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "pagamento execicio anterior": {"value": "R$ 0,00", "label": "Pagamento Execício <PERSON>", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero de ordem bancaria": {"value": "2023OB00123", "label": "Nº Ordem Bancária", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero da nota de lancamento": {"value": "2023NL00456", "label": "Nº Nota de Lançamento", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "numero do empenho": {"value": "2023EM00789", "label": "Nº Empenho", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "fonte do recurso": {"value": "Re<PERSON>rs<PERSON>", "label": "Fonte do Recurso", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}, "classificacao": {"value": "Educação", "label": "Classificação", "source": ["PortalDaTransparenciaDoAmazonas"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado da Saúde", "label": "Nome do Orgão", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "detalhes": {"nome do orgao": {"value": "Secretaria de Estado da Saúde", "label": "Nome do Orgão", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "data": {"value": "10/06/2023", "label": "Data Despesa", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "unidade gestora": {"value": "Fundo Estadual de Saúde", "label": "Unidade Gestora", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "status pagamento": {"value": "Pago", "label": "Status Pagamento", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "pagamento": {"value": "Integral", "label": "Pagamento", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "valor": {"value": "R$ 7.800,00", "label": "Valor", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "numero de ordem bancaria": {"value": "2023OB00456", "label": "Nº Ordem Bancária", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}, "historico": {"value": "Prestação de serviços de consultoria", "label": "Hist<PERSON><PERSON><PERSON>", "source": ["Portal da Transparência de Santa Catarina"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria Municipal de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "detalhes": {"numero": {"value": "2023NE00321", "label": "Número", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "favorecido": {"value": "<PERSON>", "label": "Favorecido", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "unidade gestora": {"value": "Secretaria Municipal de Educação", "label": "Unidade Gestora", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "data": {"value": "05/04/2023", "label": "Data", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "valor empenhado": {"value": "R$ 22.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "acrescimo": {"value": "R$ 0,00", "label": "Acréscimo (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "anulado": {"value": "R$ 0,00", "label": "Anulado (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "pago": {"value": "R$ 22.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}, "liquidado": {"value": "R$ 22.500,00", "label": "<PERSON>or <PERSON>ado (R$)", "source": ["Portal da Transparência de Manaus"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "detalhes": {"valor": {"value": "R$ 18.900,00", "label": "Valor (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "data": {"value": "12/07/2023", "label": "Data Despesa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor empenhado": {"value": "R$ 19.000,00", "label": "<PERSON><PERSON>", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor liquidado": {"value": "R$ 18.900,00", "label": "Valor Liquidado", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}}}, {"orgao": {"value": "Secretaria de Estado de Educação", "label": "Nome do Orgão", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "detalhes": {"unidade gestora": {"value": "Secretaria de Estado de Economia", "label": "Unidade Gestora", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "ano_mes": {"value": "2023/07", "label": "<PERSON><PERSON>/<PERSON>s", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "credor": {"value": "<PERSON>", "label": "C<PERSON>r", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "tipo de despesa": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tipo de Despesa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "id": {"value": "DF2023-0789", "label": "ID", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "esfera": {"value": "Fiscal", "label": "Esfera", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "programa de trabalho": {"value": "04.122.8203.8517.0051", "label": "Programa de Trabalho", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "funcao": {"value": "Administração", "label": "Função", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "subfuncao": {"value": "Administração Geral", "label": "Sub-Função", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "programa": {"value": "Gestão Pública", "label": "Programa", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "categoria economica": {"value": "Despesas Correntes", "label": "Categoria Econômica", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "grupo de natureza da despesa": {"value": "Outras Despesas Correntes", "label": "Grupo de Despesas", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "modalidade de aplicacao": {"value": "Aplicação Direta", "label": "Modalidade de Aplicação", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "elemento": {"value": "Diárias - Civil", "label": "Elemento", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "subelemento": {"value": "Diárias no País", "label": "Sub-Elemento", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "fonte de recurso": {"value": "Re<PERSON>rs<PERSON>", "label": "Fonte do Recurso", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "empenhado": {"value": "R$ 3.500,00", "label": "<PERSON><PERSON> (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "liquidado": {"value": "R$ 3.500,00", "label": "<PERSON>or <PERSON>ado (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago ex": {"value": "R$ 0,00", "label": "Pago Ex (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago rpp": {"value": "R$ 0,00", "label": "Pago RPP (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago rpnp": {"value": "R$ 0,00", "label": "Pago RPNP (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "pago ret": {"value": "R$ 0,00", "label": "Pago RET (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}, "valor": {"value": "R$ 3.500,00", "label": "Valor (R$)", "source": ["Portal da Transparência do Distrito Federal"], "is_deleted": false}}}]}, {"title": "Serviço Público", "subtitle": "Dados consultados em portais de Transparência estaduais e federal.", "source": ["TransparenciaPRNome"], "data_count": 2, "data": [{"servidor": {"value": "<PERSON>", "label": "Nome do Servidor", "source": ["Transparência PR"], "is_deleted": false}, "detalhes": {"carreira": {"value": "<PERSON><PERSON><PERSON> de Si<PERSON>mas", "label": "Função", "source": ["Transparência PR"], "is_deleted": false}, "instituicao": {"value": "Secretaria de Estado da Administração e da Previdência", "label": "Instituição", "source": ["Transparência PR"], "is_deleted": false}, "quadro funcional": {"value": "Quadro Próprio do Poder Executivo - QPPE", "label": "Quadro Funcional", "source": ["Transparência PR"], "is_deleted": false}, "municipio": {"value": "Curitiba", "label": "Cidade / PR", "source": ["Transparência PR"], "is_deleted": false}}}, {"servidor": {"value": "<PERSON>", "label": "Nome do Servidor", "source": ["Transparência PR"], "is_deleted": false}, "detalhes": {"carreira": {"value": "Professor", "label": "Função", "source": ["Transparência PR"], "is_deleted": false}, "instituicao": {"value": "Secretaria de Estado da Educação", "label": "Instituição", "source": ["Transparência PR"], "is_deleted": false}, "quadro funcional": {"value": "Quadro Próprio do Magistério - QPM", "label": "Quadro Funcional", "source": ["Transparência PR"], "is_deleted": false}, "municipio": {"value": "<PERSON><PERSON><PERSON>", "label": "Cidade / PR", "source": ["Transparência PR"], "is_deleted": false}}}]}, {"title": "Diários Oficiais - CPF", "subtitle": "Dados consultados na API SNAP através do CPF de entrada.", "source": ["EscavadorDOCPF", "QueridoDiarioCPF"], "data_count": 4, "data": [{"local": {"value": "<PERSON><PERSON><PERSON> União", "label": "Local", "source": ["EscavadorDOCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "15/05/2023", "label": "Data", "source": ["EscavadorDOCPF"], "is_deleted": false}, "texto correspondente": {"value": "Nomear JOÃO DA SILVA, CPF 123.456.789-00, para exercer o cargo de Analista Administrativo do quadro de pessoal do Ministério da Economia.", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}, "dados adicionais": {"value": "Se<PERSON> 2, <PERSON><PERSON><PERSON><PERSON> 45", "label": "<PERSON><PERSON>", "source": ["EscavadorDOCPF"], "is_deleted": false}, "link": {"value": "https://www.in.gov.br/web/dou/-/portaria-de-15-de-maio-de-2023", "label": "Link", "source": ["EscavadorDOCPF"], "is_deleted": false}}, "descricao": {"value": "Portaria de nomeação para cargo público", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}}, {"local": {"value": "Diário de Justiça do Estado de São Paulo", "label": "Local", "source": ["EscavadorDOCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "22/06/2023", "label": "Data", "source": ["EscavadorDOCPF"], "is_deleted": false}, "texto correspondente": {"value": "Processo nº 0001234-56.2023.8.26.0100. Autor: <PERSON>. Réu: Empresa XYZ Ltda. Juiz: Dr. <PERSON>. Decisão: <PERSON><PERSON> procedente o pedido...", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}, "dados adicionais": {"value": "Cade<PERSON> 4, <PERSON><PERSON><PERSON><PERSON> 123", "label": "<PERSON><PERSON>", "source": ["EscavadorDOCPF"], "is_deleted": false}, "link": {"value": "https://www.dje.tjsp.jus.br/cdje/consultaSimples.do?cdVolume=15&nuDiario=3528", "label": "Link", "source": ["EscavadorDOCPF"], "is_deleted": false}}, "descricao": {"value": "Decisão judicial em processo civil", "label": "Descrição", "source": ["EscavadorDOCPF"], "is_deleted": false}}, {"local": {"value": "São Paulo / SP", "label": "Cidade", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "10/07/2023", "label": "Data", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "edicao extra?": {"value": "Não", "label": "Edição Extra?", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "link": {"value": "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx", "label": "Link", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, "ocorrencia": {"value": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses.", "label": "Texto", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, {"local": {"value": "Rio de Janeiro / RJ", "label": "Cidade", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "detalhes": {"data": {"value": "05/08/2023", "label": "Data", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "edicao extra?": {"value": "<PERSON>m", "label": "Edição Extra?", "source": ["QueridoDiarioCPF"], "is_deleted": false}, "link": {"value": "https://doweb.rio.rj.gov.br/", "label": "Link", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, "ocorrencia": {"value": "EXTRATO DE CONTRATO Nº 123/2023. Partes: Município do Rio de Janeiro e João da Silva ME. Objeto: Prestação de serviços de manutenção predial. Valor: R$ 150.000,00. Prazo: 12 meses.", "label": "Texto", "source": ["QueridoDiarioCPF"], "is_deleted": false}}]}, {"title": "Diários Oficiais - NOME", "subtitle": "Dados consultados na API SNAP através do NOME da pessoa.", "source": ["EscavadorDONome", "QueridoDiarioNome"], "data_count": 4, "data": [{"local": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Local", "source": ["EscavadorDONome"], "is_deleted": false}, "detalhes": {"data": {"value": "2010-03-03", "label": "Data", "source": ["EscavadorDONome"], "is_deleted": false}, "dados adicionais": {"value": "Diário Oficial do Estado do Rio de Janeiro", "label": "<PERSON><PERSON>", "source": ["EscavadorDONome"], "is_deleted": false}, "link": {"value": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1", "label": "Link", "source": ["EscavadorDONome"], "is_deleted": false}}, "descricao": {"value": "...fevereiro de 2010, ELISETE...", "label": "Descrição", "source": ["EscavadorDONome"], "is_deleted": false}}, {"local": {"value": "Poder Executivo", "label": "Local", "source": ["EscavadorDONome"], "is_deleted": false}, "detalhes": {"data": {"value": "2015-08-12", "label": "Data", "source": ["EscavadorDONome"], "is_deleted": false}, "dados adicionais": {"value": "Diário Oficial do Estado de São Paulo", "label": "<PERSON><PERSON>", "source": ["EscavadorDONome"], "is_deleted": false}, "link": {"value": "https://www.escavador.com/diarios/123456/DOESP/poder-executivo/2015-08-12?page=5", "label": "Link", "source": ["EscavadorDONome"], "is_deleted": false}}, "descricao": {"value": "Portaria nº 123/2015 - Nomear MARIA SILVA para exercer o cargo...", "label": "Descrição", "source": ["EscavadorDONome"], "is_deleted": false}}, {"local": {"value": "Belo Horizonte / MG", "label": "Cidade", "source": ["QueridoDiarioNome"], "is_deleted": false}, "detalhes": {"data": {"value": "2022-05-20", "label": "Data", "source": ["QueridoDiarioNome"], "is_deleted": false}, "edicao extra?": {"value": "Não", "label": "Edição Extra?", "source": ["QueridoDiarioNome"], "is_deleted": false}, "link": {"value": "https://dom-web.pbh.gov.br/", "label": "Link", "source": ["QueridoDiarioNome"], "is_deleted": false}}, "ocorrencia": {"value": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses.", "label": "Texto", "source": ["QueridoDiarioCPF"], "is_deleted": false}}, {"local": {"value": "Fortaleza / CE", "label": "Cidade", "source": ["QueridoDiarioNome"], "is_deleted": false}, "detalhes": {"data": {"value": "2023-02-15", "label": "Data", "source": ["QueridoDiarioNome"], "is_deleted": false}, "edicao extra?": {"value": "<PERSON>m", "label": "Edição Extra?", "source": ["QueridoDiarioNome"], "is_deleted": false}, "link": {"value": "https://diariooficial.fortaleza.ce.gov.br/", "label": "Link", "source": ["QueridoDiarioNome"], "is_deleted": false}}, "ocorrencia": {"value": "EXTRATO DO CONTRATO Nº 025/2023 - CONTRATANTE: Município de Fortaleza, através da Secretaria Municipal de Educação. CONTRATADA: Maria Silva Consultoria Educacional LTDA, CNPJ 12.345.678/0001-90. OBJETO: Prestação de serviços de consultoria pedagógica. VALOR: R$ 85.000,00. VIGÊNCIA: 12 meses.", "label": "Texto", "source": ["QueridoDiarioNome"], "is_deleted": false}}]}, {"title": "Filiação Partidária", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEFiliacaoPartidaria"], "data_count": 3, "data": [{"sigla": {"value": "PT", "label": "Sig<PERSON>", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "detalhes": {"uf": {"value": "SP", "label": "Estado", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "data consulta": {"value": "15/05/2023", "label": "Data da Consulta", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}, "vinculos": [{"data registro": {"value": "10/03/2010", "label": "Data Registro", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "situacao": {"value": "Regular", "label": "Situação", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "tipo": {"value": "Filiação", "label": "Tipo", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}]}, {"sigla": {"value": "PSDB", "label": "Sig<PERSON>", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "detalhes": {"uf": {"value": "RJ", "label": "Estado", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "data consulta": {"value": "15/05/2023", "label": "Data da Consulta", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}, "vinculos": [{"data registro": {"value": "05/06/2005", "label": "Data Registro", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "situacao": {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "Situação", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "tipo": {"value": "Filiação", "label": "Tipo", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "data desfiliacao": {"value": "12/08/2015", "label": "Data Desfiliação", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}]}, {"sigla": {"value": "MDB", "label": "Sig<PERSON>", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "detalhes": {"uf": {"value": "MG", "label": "Estado", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "data consulta": {"value": "15/05/2023", "label": "Data da Consulta", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}, "vinculos": [{"data registro": {"value": "20/01/2018", "label": "Data Registro", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "situacao": {"value": "Cancelado", "label": "Situação", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "tipo": {"value": "Filiação", "label": "Tipo", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "data cancelamento": {"value": "15/03/2020", "label": "Data Cancelamento", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}, "motivo cancelamento": {"value": "Duplicidade de filiação", "label": "Motivo Cancelamento", "source": ["TSEFiliacaoPartidaria"], "is_deleted": false}}]}]}, {"title": "Possíveis <PERSON>", "subtitle": "Dados consultados na API SNAP.", "source": ["SNAP"], "data_count": 3, "data": [{"nome_completo": {"value": "AILTON RAMOS DA SILVA JUNIOR", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"mae": {"value": "ANTONIA MARIA O RAMOS SILVA", "label": "<PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "08936532766", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "data nascimento": {"value": "04/06/1982", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "outros_contatos", "label": "Tipo de Contato", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "M", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "552124669210", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}], "enderecos": [{"value": {"logradouro": {"value": "RMAGNO MARTINS", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "128", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "complemento": {"value": "CS 2105", "label": "Complemento", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "FREGUESIA", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "RJ", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "21911430", "label": "CEP", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}, {"nome_completo": {"value": "MARIA OLIVEIRA SANTOS", "label": "Nome", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"pai": {"value": "JOSE OLIVEIRA SANTOS", "label": "<PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "cpf": {"value": "12345678900", "label": "CPF", "source": ["SNAP"], "is_deleted": false}, "data nascimento": {"value": "15/08/1975", "label": "Data de Nascimento", "source": ["SNAP"], "is_deleted": false}, "titulo de eleitor": {"value": "123456789012", "label": "<PERSON><PERSON><PERSON><PERSON>", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "undefined", "label": "Tipo de Contato", "source": ["SNAP"], "is_deleted": false}, "sexo": {"value": "F", "label": "Sexo", "source": ["SNAP"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "5511987654321", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}], "enderecos": [{"value": {"logradouro": {"value": "RUA DAS FLORES", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "456", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "JARDIM PRIMAVERA", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "SÃO PAULO", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "SP", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "04567890", "label": "CEP", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}, {"razao_social": {"value": "EMPRESA XYZ LTDA", "label": "Razão Social", "source": ["SNAP"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12345678000190", "label": "CNPJ", "source": ["SNAP"], "is_deleted": false}, "label default key": {"value": "outros_contatos", "label": "Tipo de Contato", "source": ["SNAP"], "is_deleted": false}}, "telefones": [{"value": {"phone number": {"value": "551133334444", "label": "Número do Telefone", "source": ["SNAP"], "is_deleted": false}}, "label": "Telefone", "source": ["SNAP"], "is_deleted": false}], "enderecos": [{"value": {"logradouro": {"value": "AVENIDA PAULISTA", "label": "Logradouro", "source": ["SNAP"], "is_deleted": false}, "numero": {"value": "1000", "label": "Número", "source": ["SNAP"], "is_deleted": false}, "bairro": {"value": "BELA VISTA", "label": "Bairro", "source": ["SNAP"], "is_deleted": false}, "city": {"value": "SÃO PAULO", "label": "Cidade", "source": ["SNAP"], "is_deleted": false}, "area": {"value": "SP", "label": "Á<PERSON>", "source": ["SNAP"], "is_deleted": false}, "cep ou zipcode": {"value": "01310100", "label": "CEP", "source": ["SNAP"], "is_deleted": false}}, "label": "Endereço", "source": ["SNAP"], "is_deleted": false}]}]}, {"title": "Possíveis Contas em Sites", "subtitle": "Dados consultados na API SNAP.", "source": ["PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"], "data_count": 4, "data": [{"detalhes": [{"site": {"value": "casasbahia", "label": "Site", "source": ["PAIscpf"], "is_deleted": false}, "detalhes": {"entrada_cpf": {"value": "10748896732", "label": "Entrada CPF", "source": ["PAIscpf"], "is_deleted": false}, "found": {"value": "Não", "label": "Conta encontrada?", "source": ["PAIscpf"], "is_deleted": false}, "alerta": {"value": "Não", "label": "Alvo alertado?", "source": ["PAIscpf"], "is_deleted": false}, "tipo_alerta": {"value": "Não se Aplica", "label": "Tipo de Alerta", "source": ["PAIscpf"], "is_deleted": false}}}, {"site": {"value": "Estacionamento Digital", "label": "Site", "source": ["PAIscpf"], "is_deleted": false}, "detalhes": {"entrada_cpf": {"value": "10748896732", "label": "Entrada CPF", "source": ["PAIscpf"], "is_deleted": false}, "found": {"value": "<PERSON>m", "label": "Conta encontrada?", "source": ["PAIscpf"], "is_deleted": false}, "alerta": {"value": "Não", "label": "Alvo alertado?", "source": ["PAIscpf"], "is_deleted": false}, "tipo_alerta": {"value": "Não se Aplica", "label": "Tipo de Alerta", "source": ["PAIscpf"], "is_deleted": false}}}, {"site": {"value": "Facebook", "label": "Site", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "detalhes": {"entrada_cpf": {"value": "10748896732", "label": "Entrada CPF", "source": ["PAIscpf"], "is_deleted": false}, "found": {"value": "<PERSON>m", "label": "Conta encontrada?", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "alerta": {"value": "<PERSON>m", "label": "Alvo alertado?", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "tipo_alerta": {"value": "Notificação", "label": "Tipo de Alerta", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}}}, {"site": {"value": "Instagram", "label": "Site", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "detalhes": {"entrada_cpf": {"value": "10748896732", "label": "Entrada CPF", "source": ["PAIscpf"], "is_deleted": false}, "found": {"value": "<PERSON>m", "label": "Conta encontrada?", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "alerta": {"value": "Não", "label": "Alvo alertado?", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}, "tipo_alerta": {"value": "Não se Aplica", "label": "Tipo de Alerta", "source": ["ProvedorDeAplicacacaoDaInternetcpf"], "is_deleted": false}}}]}]}, {"title": "Doações Enviadas Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEDoacoes"], "data_count": 2, "data": [{"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}, "ano": {"value": "2022", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "cargo eleitoral": {"value": "Vereador", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "unidade eleitoral": {"value": "São Paulo / SP", "label": "Unidade Eleitoral", "source": ["TSEDoacoes"], "is_deleted": false}, "número do candidato": {"value": "12345", "label": "Número", "source": ["TSEDoacoes"], "is_deleted": false}, "partido eleitoral": {"value": "Partido ABC", "label": "Partido", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 1.000,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "10/05/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "123456789", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para campanha eleitoral", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa Física", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 500,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "15/08/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "987654321", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para material de campanha", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa Física", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}, "ano": {"value": "2022", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "cargo eleitoral": {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "unidade eleitoral": {"value": "Rio de Janeiro / RJ", "label": "Unidade Eleitoral", "source": ["TSEDoacoes"], "is_deleted": false}, "número do candidato": {"value": "54321", "label": "Número", "source": ["TSEDoacoes"], "is_deleted": false}, "partido eleitoral": {"value": "Partido XYZ", "label": "Partido", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 2.000,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "20/07/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "246813579", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para campanha eleitoral", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa Física", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}]}, {"title": "Doações Recebidas Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEDoacoes"], "data_count": 4, "data": [{"razao_social": {"value": "Empresa ABC Ltda", "label": "Razão Social", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["TSEDoacoes"], "is_deleted": false}, "cnae": {"value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "label": "CNAE", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 5.000,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "15/08/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "documento": {"value": "DOC123456", "label": "Documento", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "REC987654", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para campanha eleitoral", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos de pessoa jurídica", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 3.000,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "20/09/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "documento": {"value": "DOC654321", "label": "Documento", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "REC123456", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para material de campanha", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos de pessoa jurídica", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"razao_social": {"value": "XYZ Comércio e Serviços S.A.", "label": "Razão Social", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "98.765.432/0001-10", "label": "CNPJ", "source": ["TSEDoacoes"], "is_deleted": false}, "cnae": {"value": "4751-2/01 - Comércio varejista especializado de equipamentos e suprimentos de informática", "label": "CNAE", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 10.000,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "05/10/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "documento": {"value": "DOC789012", "label": "Documento", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "REC345678", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para campanha eleitoral", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos de pessoa jurídica", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 1.500,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "12/07/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "documento": {"value": "DOC456789", "label": "Documento", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "REC567890", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para campanha eleitoral", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa Física", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}, {"nome_completo": {"value": "<PERSON>", "label": "Nome <PERSON>to", "source": ["TSEDoacoes"], "is_deleted": false}, "detalhes": {"cpf": {"value": "987.654.321-00", "label": "CPF", "source": ["TSEDoacoes"], "is_deleted": false}}, "doacoes": [{"value": {"valor": {"value": "R$ 800,00", "label": "Valor", "source": ["TSEDoacoes"], "is_deleted": false}, "data": {"value": "25/08/2022", "label": "Data", "source": ["TSEDoacoes"], "is_deleted": false}, "documento": {"value": "DOC234567", "label": "Documento", "source": ["TSEDoacoes"], "is_deleted": false}, "recibo": {"value": "REC678901", "label": "Recibo", "source": ["TSEDoacoes"], "is_deleted": false}, "descricao": {"value": "Doação para material de campanha", "label": "Descrição", "source": ["TSEDoacoes"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEDoacoes"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEDoacoes"], "is_deleted": false}, "origem": {"value": "Pessoa Física", "label": "Origem", "source": ["TSEDoacoes"], "is_deleted": false}, "fonte": {"value": "Doação direta", "label": "Fonte", "source": ["TSEDoacoes"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEDoacoes"], "is_deleted": false}}, "label": "Doação", "source": ["TSEDoacoes"], "is_deleted": false}]}]}, {"title": "Fornecimentos Enviados Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEFornecimento"], "data_count": 1, "data": [{"razao_social": {"value": "Empresa XYZ Ltda", "label": "Razão Social", "source": ["TSEFornecimento"], "is_deleted": false}, "detalhes": {"cnpj": {"value": "12.345.678/0001-90", "label": "CNPJ", "source": ["TSEFornecimento"], "is_deleted": false}, "cnae": {"value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda", "label": "CNAE", "source": ["TSEFornecimento"], "is_deleted": false}}, "fornecimentos": [{"value": {"valor": {"value": "R$ 3.500,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "10/05/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC123456", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC987654", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de material gráfico para campanha", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 1.200,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "15/06/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC654321", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC123456", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de serviços de consultoria", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}]}]}, {"title": "Fornecimentos Recebidos Campanha", "subtitle": "Dados consultados na API SNAP.", "source": ["TSEFornecimento"], "data_count": 1, "data": [{"candidato": {"value": "<PERSON>", "label": "Candi<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "detalhes": {"cpf": {"value": "123.456.789-00", "label": "CPF", "source": ["TSEFornecimento"], "is_deleted": false}, "ano da eleição": {"value": "2022", "label": "Ano Da Eleição", "source": ["TSEFornecimento"], "is_deleted": false}, "cargo eleitoral": {"value": "Vereador", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "unidade eleitoral": {"value": "São Paulo / SP", "label": "Unidade Eleitoral", "source": ["TSEFornecimento"], "is_deleted": false}, "número do candidato": {"value": "12345", "label": "Número", "source": ["TSEFornecimento"], "is_deleted": false}, "partido eleitoral": {"value": "Partido ABC", "label": "Partido", "source": ["TSEFornecimento"], "is_deleted": false}}, "fornecimentos": [{"value": {"valor": {"value": "R$ 3.000,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "10/05/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC123456", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC987654", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de material gráfico para campanha", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Transferência eletrônica", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}, {"value": {"valor": {"value": "R$ 1.500,00", "label": "Valor", "source": ["TSEFornecimento"], "is_deleted": false}, "data": {"value": "15/06/2022", "label": "Data", "source": ["TSEFornecimento"], "is_deleted": false}, "documento": {"value": "DOC654321", "label": "Documento", "source": ["TSEFornecimento"], "is_deleted": false}, "recibo": {"value": "REC123456", "label": "Recibo", "source": ["TSEFornecimento"], "is_deleted": false}, "descricao": {"value": "Fornecimento de serviços de consultoria", "label": "Descrição", "source": ["TSEFornecimento"], "is_deleted": false}, "especie": {"value": "Depósito em espécie", "label": "Espécie", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza": {"value": "Recursos próprios", "label": "Natureza", "source": ["TSEFornecimento"], "is_deleted": false}, "natureza estimavel": {"value": "Não se aplica", "label": "<PERSON><PERSON>", "source": ["TSEFornecimento"], "is_deleted": false}, "origem": {"value": "Pessoa <PERSON>í<PERSON>", "label": "Origem", "source": ["TSEFornecimento"], "is_deleted": false}, "fonte": {"value": "Fornecimento direto", "label": "Fonte", "source": ["TSEFornecimento"], "is_deleted": false}, "Tipo": {"value": "Financeira", "label": "Tipo", "source": ["TSEFornecimento"], "is_deleted": false}}, "label": "Fornecimento", "source": ["TSEFornecimento"], "is_deleted": false}]}]}]}}