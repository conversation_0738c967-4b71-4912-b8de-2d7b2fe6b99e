#!/bin/bash

# Default timeout value (in seconds) if none is provided
TIMEOUT="${1:-1}"

should_use_kafka_container=$(echo "${SHOULD_USE_KAFKA_CONTAINER}" | tr '[:upper:]' '[:lower:]')

if [[ "$should_use_kafka_container" == "true" ]]; then
  kafka_uri=$KAFKA_CONTAINER_NAME
  kafka_port=$KAFKA_INTERNAL_PORT
else
  kafka_uri=$KAFKA_EXTERNAL_URI
  kafka_port=$KAFKA_EXTERNAL_PORT
fi


isAlive() {
  (exec 3<>/dev/tcp/"$kafka_uri"/"$kafka_port") >/dev/null 2>&1;
}

while ! isAlive; do
  echo "Waiting on kafka to start... on $kafka_uri at $kafka_port"
  sleep "$TIMEOUT"
done
