import { Request, Response, NextFunction, CookieOptions } from "express";
import jwt from "jsonwebtoken";
import { InternalServerError } from "../@types/errors";

const COOKIE_TOKEN_CONFIG: CookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  maxAge: 15 * 60 * 1000,
  path: "/",
};

const COOKIE_LOGIN_CONFIG: CookieOptions = {
  httpOnly: false,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  maxAge: 15 * 60 * 1000,
  path: "/",
};

const generateToken = (userData: any) => {
  if (!process.env.JWT_SECRET) {
    throw new InternalServerError("JWT secret is not configured");
  }

  return jwt.sign(userData, process.env.JWT_SECRET, {
    expiresIn: "24h",
  });
};

export const microsoftAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // In a real implementation, validate Microsoft OAuth token
    // 2. Get user info from Microsoft
    // 3. Create or update user in your database
    // if (!req.body.microsoftToken) {
    //   throw new ValidationError("Microsoft authentication token is required");
    // }

    // Mock user data - replace with real Microsoft auth
    const userData = {
      sub: "Hsuhda86454sb",
      name: "Andréia Ferreira de Souza",
      email: "<EMAIL>",
      image: "/profile_pic.jpg",
      report_type_credits: {
        cpf: 10,
        cnpj: 20,
        telefone: 8,
        email: 12,
      },
      total_credits: 40,
    };

    const token = generateToken(userData);

    res.cookie("token", token, COOKIE_TOKEN_CONFIG);
    res.cookie("isLoggedIn", "true", COOKIE_LOGIN_CONFIG);

    res.status(200).json({
      success: true,
      user: userData,
    });
  } catch (error) {
    next(error);
  }
};

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const userData = {
      sub: "Hsuhda86454sb",
      name: "Andréia Ferreira de Souza",
      email: "<EMAIL>",
      image: "/profile_pic.jpg",
      report_type_credits: {
        cpf: 10,
        cnpj: 20,
        telefone: 8,
        email: 12,
      },
      total_credits: 40,
    };

    const token = generateToken(userData);

    res.cookie("token", token, COOKIE_TOKEN_CONFIG);
    res.cookie("isLoggedIn", "true", COOKIE_LOGIN_CONFIG);

    res.status(200).json({
      success: true,
      user: userData,
    });
  } catch (error) {
    next(error);
  }
};

export const logout = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    res.clearCookie("token", { path: "/" });
    res.clearCookie("isLoggedIn", { path: "/" });

    res.status(200).json({
      success: true,
      message: "Logged out successfully",
    });
  } catch (error) {
    next(error);
  }
};
