#!/bin/bash
set -e

# Start MinIO temporarily to apply configurations
minio server /data --console-address ":9001" --quiet & echo $! > /tmp/minio.pid

/bin/bash "${WAIT_ON_MINIO_START}" 0.1

# Set up mc alias
mc alias set minio http://127.0.0.1:9000 "${MINIO_ROOT_USER}" "${MINIO_ROOT_PASSWORD}"

# Create buckets if not exist
for bucket in reports processed-reports; do
  if ! mc ls "minio/${bucket}" >/dev/null 2>&1; then
    mc mb "minio/${bucket}"
  fi
done

# Configure Kafka notifications
for config_name in REPORTS PROCESSED_REPORTS; do
  if ! mc admin config get minio "notify_kafka:${config_name}" >/dev/null 2>&1; then
    /bin/bash "${WAIT_ON_KAFKA}" 1
    case "${config_name}" in
      REPORTS)
        mc admin config set minio "notify_kafka:REPORTS" brokers="kafka:9092" topic="reports" queue_dir="/tmp/kafka-queue-reports" queue_limit="100000" || true
        ;;
      PROCESSED_REPORTS)
        mc admin config set minio "notify_kafka:PROCESSED_REPORTS" brokers="kafka:9092" topic="processed-reports" queue_dir="/tmp/kafka-queue-processed" queue_limit="100000" || true
        ;;
    esac
  fi
done

# Restart MinIO to load new notification configs
kill -s INT "$(cat /tmp/minio.pid)" && rm /tmp/minio.pid
/bin/bash "${WAIT_ON_MINIO_STOP}" 0.1
minio server /data --console-address ":9001" --quiet & echo $! > /tmp/minio.pid
/bin/bash "${WAIT_ON_MINIO_START}" 0.1

# Setup event triggers safely (remove old events first to avoid conflict)
for bucket in reports processed-reports; do
  mc event remove "minio/${bucket}" --force || true
done

mc event add minio/reports arn:minio:sqs::REPORTS:kafka --event put || true
mc event add minio/processed-reports arn:minio:sqs::PROCESSED_REPORTS:kafka --event put || true

# Final restart MinIO normally for production
kill -s INT "$(cat /tmp/minio.pid)" && rm /tmp/minio.pid
/bin/bash "${WAIT_ON_MINIO_STOP}" 0.1

# Start MinIO permanently
exec minio server /data --console-address ":9001"


#      /bin/sh -c '
#        # check if Minio is alive
#        isAlive() { curl -sf http://127.0.0.1:9000/minio/health/live; }
#        isKafkaReady() { (exec 3<>/dev/tcp/kafka/9092) >/dev/null 2>&1; }
#        # start Minio in the background
#        minio $0 "$@" --quiet & echo $! > /tmp/minio.pid
#        # wait until Minio is alive
#        while ! isAlive; do sleep 0.1; done
#        # setup Minio client
#        mc alias set minio http://127.0.0.1:9000 ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}
#        # Check if bucket exists before creating it
#        if ! mc ls minio/reports >/dev/null 2>&1; then
#          mc mb minio/reports
#        fi
#        # make the test bucket public
#        # mc anonymous set public minio/test-bucket
#
#        # Check if Kafka config exists before setting it
#        mc admin config get minio notify_kafka:REPORTS >/dev/null 2>&1
#        if [ $? -ne 0 ]; then
#          until isKafkaReady; do echo "Kafka not ready yet..."; sleep 2; done
#          mc admin config set minio/reports notify_kafka:REPORTS brokers="kafka:9092" topic="reports" queue_dir="/tmp/kafka-queue" queue_limit="100000" || true;
#          # restart Minio
#          kill -s INT $(cat /tmp/minio.pid) && rm /tmp/minio.pid
#          while isAlive; do sleep 0.1; done
#          minio $0 "$@" --quiet & echo $! > /tmp/minio.pid
#          while ! isAlive; do sleep 0.1; done
#        fi
#
#        if [ $(mc event list minio/reports | wc -l) -eq 0 ]; then
#          mc event add minio/reports arn:minio:sqs::REPORTS:kafka --event put || true;
#        fi
#
#        # stop Minio
#        kill -s INT $(cat /tmp/minio.pid) && rm /tmp/minio.pid
#        # wait until Minio is stopped
#        while isAlive; do sleep 0.1; done
#        # start minio for good
#        exec minio $0 "$@"'
#
#    command: server /data --console-address ":9001"