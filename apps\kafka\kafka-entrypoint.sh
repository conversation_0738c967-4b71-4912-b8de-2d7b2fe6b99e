
#!/bin/bash
set -e

/opt/bitnami/scripts/kafka/setup.sh

#echo "ZZZZZ Waiting for kafka....${KAFKA_KRAFT_CLUSTER_ID}"
#
#CLUSTER_ID=${KAFKA_KRAFT_CLUSTER_ID:-"my-cluster-id"}
#echo "ZZZZZ Waiting for kafka....${CLUSTER_ID}"
#KAFKA_STORAGE_DIR="/bitnami/kafka/data"
#KAFKA_CONFIG="/opt/bitnami/kafka/config/server.properties"
#
#if [ ! -f "$KAFKA_STORAGE_DIR/meta.properties" ]; then
#  echo "Formatting storage directory with cluster ID $CLUSTER_ID"
#  /opt/bitnami/kafka/bin/kafka-storage.sh format \
#    --ignore-formatted \
#    --cluster-id "$CLUSTER_ID" \
#    --config "$KAFKA_CONFIG"
#fi

/opt/bitnami/scripts/kafka/run.sh &

until kafka-topics.sh --bootstrap-server kafka:9092 --list &>/dev/null; do
  sleep 1
done

# echo "creating topic"
kafka-topics.sh --create --if-not-exists --topic reports --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1

# Create processed-reports topic
kafka-topics.sh --create --if-not-exists --topic processed-reports --bootstrap-server kafka:9092 --partitions 1 --replication-factor 1

echo "Kafka Started With topics: reports and processed-reports"


wait