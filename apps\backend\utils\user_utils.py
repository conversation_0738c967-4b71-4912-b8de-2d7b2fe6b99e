import logging
from sqlalchemy import text
from core.config import settings

from database.db import get_database_async_engine

logger = logging.getLogger(__name__)

async def get_user_data_dict(user_id: str):
    logger.info("[get_user_data_dict][user(%s)] Starting fetch for user_id.", user_id)
    engine = get_database_async_engine()

    query = 'SELECT * FROM public.users WHERE "user_id" = :user_id'
    logger.info("[get_user_data_dict][user(%s)] Query prepared: %s", user_id, query)

    try:
        async with engine.connect() as conn:
            logger.info("[get_user_data_dict][user(%s)] Connected to database.", user_id)
            result = await conn.execute(text(query), {"user_id": user_id})
            row = result.fetchone()

            if row:
                logger.info("[get_user_data_dict][user(%s)] User data found.", user_id)
                return dict(row._mapping)
            else:
                logger.warning("[get_user_data_dict][user(%s)] No user found for user_id: %s", user_id, user_id)
                return None
    except Exception as e:
        logger.error("[get_user_data_dict][user(%s)] Error fetching user data: %s", user_id, e)
        return None

