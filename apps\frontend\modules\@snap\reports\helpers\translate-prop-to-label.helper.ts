import {TRANSLATED_LABELS} from "../config/constants"

/**
 * Translates a property name to a user-friendly label using the translate_prop_to_label.json mapping.
 * The function performs case-insensitive matching by converting both the input and keys to lowercase.
 *
 * @param propName - The property name to translate
 * @returns The translated label if a match is found, otherwise returns the original string
 *
 * @example
 * // Returns "Nome Completo"
 * translatePropToLabel("full name");
 *
 * @example
 * // Returns "Nome Completo" (case-insensitive)
 * translatePropToLabel("FULL NAME");
 *
 * @example
 * // Returns "unknown_property" (no match found)
 * translatePropToLabel("unknown_property");
 */
export const translatePropToLabel = (propName: string): string => {
  if (!propName || typeof propName !== "string") return String(propName);

  const normalizedInput = propName.toLowerCase();

  // Create a lowercase map of keys for case-insensitive matching
  for (const key in TRANSLATED_LABELS) {
    if (key.toLowerCase() === normalizedInput) {
      return TRANSLATED_LABELS[key as keyof typeof TRANSLATED_LABELS];
    }
  }

  // Return original string if no match found
  return propName;
}