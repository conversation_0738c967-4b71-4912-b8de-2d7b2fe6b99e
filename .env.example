
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=password

MINIO_CONTAINER_NAME=minio
MINIO_S3_EXTERNAL_PORT=9000
MINIO_S3_INTERNAL_PORT=9000
MINIO_UI_EXTERNAL_PORT=9001
MINIO_UI_INTERNAL_PORT=9001

#dev only
MINIO_TEST_SETUP_CONTAINER_NAME=minio-test-setup

KAFKA_CONTAINER_NAME=kafka
KAFKA_EXTERNAL_PORT=9092
KAFKA_INTERNAL_PORT=9092

SPARK_VOLUMES_INIT_CONTAINER_NAME=spark-volumes-init
SPARK_APP_CONTAINER_NAME=spark-app
SPARK_MASTER_CONTAINER_NAME=spark-app
SPARK_WORKER_CONTAINER_NAME=spark-app


SNAP_API_CLIENT_SECRET=8454f11a72384868a374c761aff87b5f
CLIENT_ID_KEYCLOAK=myclient
REDIRECT_URI_KEYCLOAK=http://***************:8000/auth/callback
CAPTCHA_KEY=05b13752bd68c5fd06b78241e2abc2fc
# LOCAL_TOKEN=eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.
# PYTHONPATH=./apps/backend
# CLIENT_SECRET_GOOGLE=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
# CLIENT_ID_GOOGLE=409707573351-30funda9lsmaml5is48nsdh0f6vjht9d.apps.googleusercontent.com
# CLIENT_SECRET_MICROSOFT=22b45c21-d4dd-46a1-9bd3-f4c9e045a1a6
# CLIENT_ID_MICROSOFT=****************************************
KEYCLOAK_URL=http://***************:8080
REALM_NAME=SnapReportsRealm
FRONTEND_REDIRECT_URL=http://***************:3000/dashboard
BASE_API_URL=https://snap-api.azure-api.net
DB_HOST=postgres
DB_NAME=keycloak
DB_USER=keycloak
DB_PASS=keycloak
# REALM= myrealm
# NGROK_AUTHTOKEN=*************************************************