import React from "react";
import { Mandado } from "../../model/Mandados";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { renderSourceTooltip, renderValidArray, renderValidObject } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { translatePropToLabel } from "~/helpers";

export class RenderMandados implements RenderStrategy<Mandado> {

    validateKeys = (keys: Array<keyof Mandado>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the pena information for a mandado
     * @param mandado The Mandado object
     * @returns React element or null if no data
     */
    renderPena = (mandado?: Mandado): React.ReactElement | null => {
        if (!mandado?.pena) return null;

        return renderValidObject('pena', { pena: mandado.pena });
    };

    formatByKey: Record<
        string,
        (mandado?: Mandado) => React.ReactElement | null
    > = {
            numero: (mandado?: Mandado) => {
                if (!mandado?.numero) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(mandado.numero.label || "Número").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={String(mandado.numero.value || "")}
                                tooltip={renderSourceTooltip(mandado.numero.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (mandado?: Mandado) => {
                if (!mandado?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(mandado.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="">
                                <CustomReadOnlyInputField
                                    label={translatePropToLabel(value.label || key).toUpperCase()} 
                                    value={value.value}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            pena: (mandado?: Mandado) => {
                return renderValidArray('pena', mandado || {}, [], true /* hasLabel */, false /* hasTitle */);
            }
        }

    render = (mandado: Mandado): React.ReactElement[] => {
        const keys = Object.keys(mandado) as Array<keyof Mandado>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof Mandado> = [
            'numero',
            'detalhes',
            'pena',
            'pessoa'
        ];

        // Filter the keys to only include those that exist in the mandado object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(mandado);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}