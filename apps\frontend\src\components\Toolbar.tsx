import { Calendar, List, SortAsc, Search, X } from "lucide-react";
import { Input, Button, Select, Icon } from "@snap/design-system";
import { useState, useRef, useCallback, memo, useEffect } from "react";
import { useLocation } from "react-router";
import { useReportListActions } from "~/store/reportListStore";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";

function Toolbar() {
  const { setSearchFilter, clearSearchFilter } = useReportListActions();
  const { invalidateToInitialPage } = useReportCRUD();
  const [selectFilterType, setSelectFilterType] = useState<string>("1");
  const [inputValue, setInputValue] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const location = useLocation();

  const setFilter = useCallback(async (value: string) => {
    setSearchFilter(value);
  }, [setSearchFilter]);

  const handleSearch = useCallback(async (value: string, shouldInvalidate = true) => {
    if (value?.length >= 3) {
      await setFilter(value)
    } else {
      clearSearchFilter()
    }

    if (shouldInvalidate) {
      await invalidateToInitialPage()
    }
  }, [clearSearchFilter, invalidateToInitialPage, setFilter]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value === '') {
      clearSearchFilter();
      requestAnimationFrame(async () => {
        await invalidateToInitialPage();
      });
    }
  }, [clearSearchFilter, invalidateToInitialPage]);

  const clearInput = useCallback(() => {
    setInputValue("");

    if (inputRef.current) {
      inputRef.current.value = "";
      inputRef.current.focus();
    }

    // Clear the filter and invalidate reports
    clearSearchFilter();
    requestAnimationFrame(async () => {
      await invalidateToInitialPage();
    });
  }, [clearSearchFilter, invalidateToInitialPage]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const value = (e.target as HTMLInputElement).value;
      const shouldInvalidate = value.length >= 3 || value === '';
      if(!shouldInvalidate && value.length > 0){
        toast("Erro", {
          description: "A busca deve conter pelo menos 3 caracteres",
        });
      }
      handleSearch(value, shouldInvalidate);
    }
  }, [handleSearch]);

  const renderContent = () => {
    if (location.pathname !== "/") {
      return null;
    }

    return (
      <div className="flex items-center justify-between w-full">
        <div className=" flex items-center gap-8 flex-1/2">
          <div className="flex-3/4 mt-1.5">
            {/* receber componente de input se for passado */}
            <div className="relative w-full">
              <Input
                ref={inputRef}
                value={inputValue}
                placeholder="Procurar nos relatórios"
                onChange={handleChange}
                className="pr-10"
                onKeyDown={handleKeyDown}
              />
              <button
                type="button"
                onClick={inputValue ? clearInput : undefined}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground focus:outline-none"
                aria-label={inputValue ? "Limpar busca" : "Buscar"}
              >
                {inputValue ? (
                  <X size={18} />
                ) : (
                  <Search size={18} />
                )}
              </button>
            </div>
          </div>
          <div className="flex-1/4">
            {/* receber componente de select se for passado */}
            {/* <Select
              options={[
                { value: "1", label: "TODOS" },
                { value: "2", label: "RELATÓRIOS" },
                { value: "3", label: "PASTAS" },
              ]}
              value={selectFilterType}
              onChange={setSelectFilterType}
            /> */}
          </div>
        </div>

        <div className="flex items-center justify-end gap-2 flex-1/2">
          {/* receber os componentes de ícones se for passado */}
          {/* <Button variant="ghost">
            <Calendar size={32} />
          </Button>
          <Button variant="ghost">
            <List size={32} />
          </Button>
          <Button variant="ghost">
            <SortAsc size={32} />
          </Button> */}
        </div>
      </div>
    );
  };

  return renderContent();
}

export default memo(Toolbar);
