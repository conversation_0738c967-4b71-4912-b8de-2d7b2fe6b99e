import { AccordionGroup } from "~/types/global";

export class DataProcessingWorkerService {
  private worker: Worker | null = null;
  private isTerminated = false;
  // Singleton instance check
  private static instance: DataProcessingWorkerService | null = null;
  private readonly TIMEOUT_DURATION = 30000;

  private constructor() {
    // Private constructor for singleton pattern
    if (typeof window === 'undefined') {
      throw new Error('Workers can only be instantiated in browser environment');
    }
  }

  public static getInstance(): DataProcessingWorkerService {
    if (!DataProcessingWorkerService.instance) {
      DataProcessingWorkerService.instance = new DataProcessingWorkerService();
    }
    return DataProcessingWorkerService.instance;
  }

  private initWorker(): void {
    try {
      if (!this.worker && !this.isTerminated) {
        // Check browser support for Web Workers
        if (typeof Worker === 'undefined') {
          throw new Error('Web Workers are not supported in this environment');
        }

        // Create worker with error handling
        this.worker = new Worker(
          new URL("../../workers/dataProcessing.worker.ts", import.meta.url),
          { type: "module" }
        );

        // Add error handling
        this.worker.onerror = (error) => {
          console.error("Worker error:", error);
          this.terminateWorker();
        };

        // Add termination handling
        this.worker.onmessageerror = (error) => {
          console.error("Worker message error:", error);
          this.terminateWorker();
        };
      }
    } catch (error) {
      console.error("Failed to initialize worker:", error);
      throw new Error(`Worker initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async processReportData(
    reportData: unknown,
  ): Promise<AccordionGroup[]> {
    try {
      this.initWorker();

      if (!this.worker) {
        throw new Error("Worker failed to initialize");
      }

      if (!reportData) {
        throw new Error("Invalid input parameters");
      }

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log("Service sending reportData:", reportData);
      }

      return new Promise((resolve, reject) => {
        if (!this.worker) {
          reject(new Error("Worker is not initialized"));
          return;
        }

        // Timeout handling
        const timeoutId = setTimeout(() => {
          this.terminateWorker();
          reject(new Error("Worker operation timed out"));
        }, this.TIMEOUT_DURATION);

        const handleMessage = (event: MessageEvent) => {
          clearTimeout(timeoutId);

          if (process.env.NODE_ENV === 'development') {
            console.log("Service received response:", event.data);
          }

          if (event.data.error) {
            reject(new Error(event.data.error));
          } else {
            resolve(event.data.data);
          }

          this.worker?.removeEventListener("message", handleMessage);
        };

        this.worker.addEventListener("message", handleMessage);

        // Add try-catch for postMessage
        try {
          this.worker.postMessage({ reportData });
        } catch (error) {
          clearTimeout(timeoutId);
          this.worker.removeEventListener("message", handleMessage);
          reject(new Error(`Failed to send message to worker: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      });
    } catch (error) {
      this.terminateWorker();
      throw error;
    }
  }

  private terminateWorker(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
      this.isTerminated = true;
    }
  }

  // Cleanup method for component unmounting
  public dispose(): void {
    this.terminateWorker();
    DataProcessingWorkerService.instance = null;
  }
}

// Singleton instance with proper typing
export const dataProcessingWorker = DataProcessingWorkerService.getInstance();