// Web Worker é responsável por executar as operações de criptografia e descriptografia
// fora da thread principal para evitar bloqueios na UI.
// [ADAPTAR] Dependendo do tamanho dos dados, pode ser necessário otimizar a comunicação.
self.addEventListener("message", async (event) => {
  const { cmd, id, payload } = event.data;

  // Convert ArrayBuffer to a Base64 string
  function arrayBufferToBase64(buffer: ArrayBuffer): string {
    let binary = "";
    const bytes = new Uint8Array(buffer);
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  // Convert Base64 string back to ArrayBuffer
  function base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }

  if (cmd === "encrypt") {
    try {
      // Importa a chave bruta enviada como ArrayBuffer
      const key = await crypto.subtle.importKey(
        "raw",
        payload.key,
        { name: "AES-GCM" },
        false,
        ["encrypt"]
      );
      // Gera um IV aleatório de 12 bytes
      const iv = crypto.getRandomValues(new Uint8Array(12));
      // Codifica os dados (em JSON) para ArrayBuffer
      const encoded = new TextEncoder().encode(JSON.stringify(payload.data));
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: "AES-GCM", iv },
        key,
        encoded
      );
      // Convert to Base64 strings
      const encryptedBase64 = arrayBufferToBase64(encryptedBuffer);
      const ivBase64 = arrayBufferToBase64(iv.buffer);

      self.postMessage({
        cmd: "encrypt",
        id,
        result: { encrypted: encryptedBase64, iv: ivBase64 },
        success: true
      });
    } catch (err: any) {
      self.postMessage({ cmd: "error", id, error: err.message });
    }
  } else if (cmd === "decrypt") {
    try {
      // Importa a chave para descriptografia
      const key = await crypto.subtle.importKey(
        "raw",
        payload.key,
        { name: "AES-GCM" },
        true,
        ["decrypt"]
      );
      
      // Convert Base64 strings back to ArrayBuffers
      const encryptedBuffer = base64ToArrayBuffer(payload.data.encrypted);
      const ivBuffer = base64ToArrayBuffer(payload.data.iv);
      const iv = new Uint8Array(ivBuffer);

      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: "AES-GCM", iv },
        key,
        encryptedBuffer
      );
      const decryptedText = new TextDecoder().decode(decryptedBuffer);
      const data = JSON.parse(decryptedText);
      self.postMessage({ cmd: "decrypt", id, result: data, success: true });
    } catch (err: any) {
      self.postMessage({ cmd: "error", id, error: err.message });
    }
  }
});
// Export as ES module rather than a global script.
// This helps TypeScript enforce module scoping,
// ensuring that variables declared in the file do not pollute the global namespace.
export {};
