import fs from "fs/promises";
import path from "path";
import crypto from "crypto";
import { ConflictError, InternalServerError } from "../@types/errors";

interface UserSalt {
  sub: string;
  salt: string;
}

interface UserVerifier {
  sub: string;
  encrypted: string;
  iv: string;
}

export class UserSaltService {
  private readonly mocksDir = path.join(__dirname, "..", "..", "mocks");

  private getFilePath(sub: string): string {
    return path.join(this.mocksDir, `${sub}.json`);
  }

  private getVerifierPath(sub: string): string {
    return path.join(this.mocksDir, `${sub}_verifier.json`);
  }

  async saveUserSalt(sub: string, salt: string): Promise<void> {
    try {
      await fs.mkdir(this.mocksDir, { recursive: true });
      const userData: UserSalt = { sub, salt };
      await fs.writeFile(
        this.getFilePath(sub),
        JSON.stringify(userData, null, 2)
      );
    } catch (error) {
      console.error("Error saving user salt:", error);
      throw new InternalServerError("Failed to save user salt");
    }
  }

  async getUserSalt(sub: string): Promise<string | null> {
    try {
      const filePath = this.getFilePath(sub);
      const fileContent = await fs.readFile(filePath, "utf-8");
      const userData: UserSalt = JSON.parse(fileContent);
      return userData.salt;
    } catch (error) {
      return null;
    }
  }

  async checkSaltExists(sub: string): Promise<boolean> {
    try {
      const salt = await this.getUserSalt(sub);
      return salt !== null;
    } catch (error) {
      return false;
    }
  }

  generateSalt(): string {
    return crypto.randomBytes(16).toString("hex");
  }

  async initializeUserSalt(sub: string): Promise<string> {
    try {
      const exists = await this.checkSaltExists(sub);
      if (exists) {
        throw new ConflictError("Salt already exists for this user");
      }

      const newSalt = this.generateSalt();
      await this.saveUserSalt(sub, newSalt);
      return newSalt;
    } catch (error) {
      if (error instanceof ConflictError) {
        throw error;
      }
      console.error("Error initializing user salt:", error);
      throw new InternalServerError("Failed to initialize user salt");
    }
  }

  async saveUserVerifier(verifier: UserVerifier): Promise<void> {
    try {
      await fs.mkdir(this.mocksDir, { recursive: true });
      const userData: UserVerifier = verifier;
      await fs.writeFile(
        this.getVerifierPath(verifier.sub),
        JSON.stringify(userData, null, 2)
      );
    } catch (error) {
      console.error("Error saving user verifier:", error);
      throw new InternalServerError("Failed to save user verifier");
    }
  }

  async getUserVerifier(sub: string): Promise<UserVerifier | null> {
    try {
      const filePath = this.getFilePath(`${sub}_verifier.json`);
      const fileContent = await fs.readFile(filePath, "utf-8");
      const userVerifierData: UserVerifier = JSON.parse(fileContent);
      return userVerifierData;
    } catch (error) {
      return null;
    }
  }

  async checkVerifierExists(sub: string): Promise<boolean> {
    try {
      const verifier = await this.getUserVerifier(sub);
      const hasVerifier = verifier !== null;
      return hasVerifier;
    } catch (error) {
      return false;
    }
  }

  async postUserVerifier(verifier: UserVerifier): Promise<void> {
    try {
      await this.saveUserVerifier(verifier);
    } catch (error) {
      if (error instanceof ConflictError) {
        throw error;
      }
      throw new InternalServerError("Failed to post user verifier");
    }
  }
}
