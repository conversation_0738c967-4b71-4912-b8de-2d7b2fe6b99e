# Fluxo autenticação / Criação e validação da senha

## Página de Login

- O usuário acessa a aplicação por meio de um navegador.
- O usuário clica no botão de autenticação para fazer login usando Google ou Microsoft.

### Chamada de API para Autenticação

- O Keycloak (através da Reports API) gerencia a autenticação.
- Após a autenticação bem-sucedida, o cliente recebe os dados do usuário (por exemplo, id, nome, email, imagem, créditos de relatório, créditos totais).
- A Reports API armazena um token de acesso HTTP-only e um refresh token (o refresh token não contém informações do usuário) nos cookies.

- **Redirecionamento para a Página Inicial:**
  - O usuário é redirecionado para a página inicial (`/`).

## Diálogo da Senha do Usuário

- Um diálogo solicita ao usuário que insira uma senha (esta senha fica apenas na memória do sistema).

### Caso o usuário ainda não tenha uma chave secreta (primeira vez)

1. **Verificar se existe um verificador para o usuário**
   - Isto é feito a partir dos dados do usuário já carregados na página inicial.
   - Se o verificador não existir, as mensagens exibidas no diálogo indicam que o usuário está criando uma nova senha.

2. **Derivar senha e criptografar verificador para armazenamento:**
   - A senha inserida pelo usuário é derivada em uma chave de criptografia usando Argon2.
   - Um verificador (frase fixa) é criptografado com a chave derivada.

3. **Salvar verificador no banco de dados:**
   - O verificador é enviado para o endpoint /verifier e adicionado ao banco de dados jusnto aos dados do usuário.
   - A verificação do usuário é feita através do token, do lado da api.

### Caso o usuário já tenha uma chave secreta

1. **Derivar senha e decriptografar verificador para validação:**
   - A senha inserida pelo usuário é derivada em uma chave de criptografia usando Argon2.
   - O verificador armazenado é decriptografado com a chave derivada.
   - Se a descriptografia for bem-sucedida, a senha é considerada válida.

## Dados vinculados ao usuário:

- Qualquer informação sensível dos relatórios associados ao usuário precisa ser criptografada com a chave derivada da senha do usuário antes de ser armazenada no banco de dados.
- O mesmo se aplica ao buscar os dados do banco: é necessário descriptografar os dados antes de exibi-los para o usuário.
