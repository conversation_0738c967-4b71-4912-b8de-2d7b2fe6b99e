import { create } from "zustand";
import { describe, expect, test, beforeEach } from "vitest";
import type { ReportDetails, ReportListItem } from "~/types/global";
import createReportSlice from "~/store/slices/createReportSlice";

// A stub factory for ReportListItem
const makeReportStub = (
  overrides: Partial<ReportListItem> = {}
): ReportListItem => ({
  id: "fake-id-" + Math.random().toString(36).slice(2, 11),
  reportName: "Default Report",
  reportStatus: "Complete",
  reportType: "cpf",
  creationDate: new Date("2024-01-02").toISOString(),
  lastModified: new Date("2024-01-02").toISOString(),
  requestDate: new Date("2024-01-02").toISOString(),
  creditsUsed: 10,
  searchArgs: { cpf: ["12345678900"] },
  ...overrides,
});

// Similarly, a stub for ReportDetails
const makeReportDetailsStub = (
  overrides: Partial<ReportDetails> = {}
): ReportDetails => ({
  result: {
    creationDate: new Date("2024-01-02").toISOString(),
    creditsUsed: 10,
    data: { key: "value" },
    lastModified: new Date("2024-01-02").toISOString(),
    omittedNodes: [],
    reportName: "Detailed Report",
    reportStatus: "Complete",
    reportType: "cpf",
    requestDate: new Date("2024-01-02").toISOString(),
    searchArgs: { cpf: ["12345678900"] },
  },
  ...overrides,
});

// Utility to create a fresh store for each test (similar to the makeSUT pattern)
const createTestStore = () => {
  return create<ReturnType<typeof createReportSlice>>()((...args) => ({
    ...createReportSlice(...args),
  }));
};

describe("createReportSlice", () => {
  let store: ReturnType<typeof createTestStore>;

  beforeEach(() => {
    store = createTestStore();
  });

  test("setReportList replaces the entire report list", () => {
    // Arrange: Create a stub report list using the factory
    const stubReports = [
      makeReportStub({ id: "report-1", reportName: "CPF Report" }),
    ];
    // Act: Set the report list using the slice method
    store.getState().setReportList(stubReports);
    // Assert: The slice state now has the stub list
    expect(store.getState().reports.list).toEqual(stubReports);
  });

  test("addReport adds a report to the list", () => {
    // Arrange: Create a stub report with specific overrides
    const report = makeReportStub({
      id: "report-2",
      reportName: "Telefone Report",
      reportType: "telefone",
      creditsUsed: 20,
      searchArgs: { telefone: ["21999891661"] },
    });
    // Act: Call addReport with the stub report
    store.getState().addReport(report);
    // Assert: The report should now be in the list
    expect(store.getState().reports.list).toContainEqual(report);
  });

  test("removeReport removes the report with the specified id", () => {
    // Arrange: Create two stub reports
    const report1 = makeReportStub({
      id: "report-1",
      reportName: "CPF Report",
    });
    const report2 = makeReportStub({
      id: "report-2",
      reportName: "Telefone Report",
      reportType: "telefone",
      creditsUsed: 20,
      searchArgs: { telefone: ["21999891661"] },
    });
    store.getState().setReportList([report1, report2]);
    // Act: Remove the report with id "report-1"
    store.getState().removeReport("report-1");
    // Assert: Only report2 remains in the list
    expect(store.getState().reports.list).toEqual([report2]);
  });

  test("setReportDetails updates the report details", () => {
    // Arrange: Create a stub details object
    const details = makeReportDetailsStub();
    // Act: Update details in the slice
    store.getState().setReportDetails(details);
    // Assert: The details state matches the stub
    expect(store.getState().reports.details).toEqual(details);
  });

  test("setReportType updates the report type", () => {
    // Act: Change the reportType to 'email'
    store.getState().setReportType("email");
    // Assert: The state reflects the new type
    expect(store.getState().reports.reportType).toBe("email");
  });

  test("setInputValue updates the input value", () => {
    // Act: Update the input value in the slice
    store.getState().setInputValue("test-input");
    // Assert: The state's inputValue now equals 'test-input'
    expect(store.getState().reports.inputValue).toBe("test-input");
  });
});
