# REGRAS RELATÓRIO CPF


## Seções existentes para o tipo de relatório "CPF" e suas respectivas fontes de dados:

1. <PERSON><PERSON>
    - <PERSON><PERSON>s de dados: "SNAP"

2. Mandados de Prisão
    - Fontes de dados: "BancoNacionalDeMonitoramentoDePrisoes"

3. Telefones
    - Fontes de dados: "SNAP", "SintegraMA"

4. Emails
    - Fontes de dados: "SNAP"

5. Endereços
    - <PERSON><PERSON>s de dados: "SNAP", "SintegraMTCPF", "SintegraPBCPF", "SintegraSECPF", "SintegraMA", "SintegraPRCPF"

6. Parentes
    - Fontes de dados: "SNAP", "BancoNacionalDeMonitoramentoDePrisoes"

7. Sociedades
    - Fontes de dados: "SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"

8. <PERSON><PERSON><PERSON>s
    - <PERSON><PERSON>s de dados: "SNAP", "CadastroNacionalPJ"

9. <PERSON><PERSON><PERSON><PERSON>
    - <PERSON><PERSON>s de dados: "SNAP"

10. Processos
    - <PERSON><PERSON>s de dados: "Escavador"

11. Recursos Públicos Recebidos
    - Fontes de dados: "PortalDaTransparenciaDeMinasGerais", "PortalDaTransparenciaDoAmazonas", "TransparenciaSC", "TransparenciaManausCPF", "TransparenciaDF"

12. Serviço Público
    - Fontes de dados: "TransparenciaPRNome"

13. Diários Oficiais - CPF
    - Fontes de dados: "EscavadorDOCPF", "QueridoDiarioCPF"

14. Diários Oficiais - NOME
    - Fontes de dados: "EscavadorDONome", "QueridoDiarioNome"

15. Filiação Partidária
    - Fontes de dados: "TSEFiliacaoPartidaria"

16. Possíveis Contatos
    - Fontes de dados: "SNAP"

17. Possíveis Contas em Sites
    - Fontes de dados: "PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"

18. Doações Enviadas Campanha
    - Fontes de dados: "TSEDoacoes"

19. Doações Recebidas Campanha
    - Fontes de dados: "TSEDoacoes"

20. Fornecimentos Enviados Campanha
    - Fontes de dados: "TSEFornecimento"

21. Fornecimentos Recebidos Campanha
    - Fontes de dados: "TSEFornecimento"

## SEÇÃO: "Dados Pessoais"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.

- Adicionar as propriedades (chave e valor) deste objeto na seção "Dados Pessoais", sem listas e objetos, apenas os valores primitivos. Existem algumas propriedades que não devem ser adicionadas nesse processo. São elas:
  - "bookmark"
  - "first names"
  - "surname"
  - "pessoa" (já que se trata de uma lista)

- No objeto com propriedade **"bookmark": 4**, buscar na lista com chave **"pessoa"** um objeto com a propriedade **"label default key"** com valor que contenha o termo **"mae"**.
  - Adicionar a propriedade **"full name"** deste objeto à seção "Dados Pessoais", porém trocando a chave para **"nome_da_mae"**. Este passo é necessário pois o nome da mãe pode ser determinante para identificar e diferenciar as pessoas de interesse.

- Modificar valores de propriedades específicas:
  - "idade" retorna apenas texto com um número por padrão, então é necessário adicionar "ANOS" ao retorno (ex.: "34" --> "34 ANOS")
  - "sexo" retorna apenas a inicial, sendo necesário atribuir a palavra completa (ex.: "F" --> "Feminino", "M" --> "Masculino")

**Regras para a formatação da seção:**

- Ordenar as propriedades inseridas na seção "Dados Pessoais" colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "full name"
  - "nome_da_mae" (inserido no passo anterior)
  - "idade"
  - "sexo"
  - "nacionalidade" OU "pais do passaporte"
  - "data nascimento" OU "dt_nascimento"
  - "cpf"
  - "identidade"
  - restante das propriedades...

- O objeto que constitui a seção **"Dados Pessoais"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Dados Pessoais"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP"]
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": { // objeto com os dados
            "full name": {
                "value": "ANDREIA FERREIRA DE SOUZA",
                "label": "Nome Completo",
                "source": ["SNAP"],
                "is_deleted": false
            }
            //outros objetos da seção
        }
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cpf": "110.452.316-73",
                    "data de admissao": "18/12/2019",
                    "data nascimento": "02/12/1990",
                    "escolaridade": "EDUCACAO SUPERIOR COMPLETA",
                    "first names": "ANDREIA",
                    "full name": "ANDREIA FERREIRA DE SOUZA",
                    "idade": "34",
                    "info restricao": "* NADA CONSTA *",
                    "pais do passaporte": "BRASIL",
                    "pessoa": [
                        {
                            "full name": "SANDRA FERREIRA DE CARVALHO",
                            "label default key": "parente MAE"
                        },
                        {
                            "full name": "JESUS ALVES DE SOUZA",
                            "label default key": "parente PAI"
                        }
                    ],
                    "pis/pasep": "13055665111",
                    "procon": "(NAO TEM)",
                    "renda estimada": "2917",
                    "sexo": "F",
                    "signo": "SAGITARIO",
                    "status receita": "REGULAR",
                    "surname": "FERREIRA DE SOUZA"
                }
                //outros objetos
            ]
            //outras propriedades
        }
    ]
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Dados Pessoais",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": 7,
        "data": [
            {
             "detalhes": {
                    "full name": {
                        "value": "ANDREIA FERREIRA DE SOUZA",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "nome_da_mae": {
                        "value": "SANDRA FERREIRA DE CARVALHO",
                        "label": "Nome da Mãe",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "idade": {
                        "value": "34 ANOS",
                        "label": "Idade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "sexo": {
                        "value": "Feminino",
                        "label": "Sexo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "pais do passaporte": {
                        "value": "BRASIL",
                        "label": "País do Passaporte",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "data nascimento": {
                        "value": "02/12/1990",
                        "label": "Data de Nascimento",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cpf": {
                        "value": "110.452.316-73",
                        "label": "CPF",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                    //outros objetos da seção
                }
            }
        ]
    }
    //outras seções
]
```
</div>
</div>

## SEÇÃO: "Mandados de Prisão"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"BancoNacionalDeMonitoramentoDePrisoes"** a lista com chave **"mandado"**.

- Buscar Dentro de cada objeto da chave **"mandado"**, uma outra lista com chave **"pessoa"**, e copiar as propriedades **"pena imposta"** e **"recaptura"** para uma nova lista que precisa estar na raiz deste objeto, utilizando o valor **"Pena"** como chave.

- Adicionar todos os objetos da lista **"mandado"** no **"data"** da seção **"Mandados de Prisão"**.
-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
     - "pessoa"

**Regras para a formatação da seção:**

- Ordenar as propriedades de cada objeto inserido na seção "Mandados de Prisão", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "numero do mandado de prisao"
  - "mandado"
  - "situacao"
  - "numero do processo"
  - restante das propriedades...

- O objeto que constitui a seção **"Mandados de Prisão"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Mandados de Prisão"
  - **"subtitle":** "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP)."
  - **"source"**: ["BancoNacionalDeMonitoramentoDePrisoes"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção.
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "numero"
- "detalhes"
- "pena"

```json
  "data": [
    {
        "numero": {
            "value": "0001234-56.2023.8.19.0001.01.0001-01",
            "label": "Número do Mandado",
            "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
            "is_deleted": false
        },
        "detalhes": {
            "tipo de mandado": {
                "value": "Preventiva",
                "label": "Tipo de Mandado",
                "source": ["BancoNacionalDeMonitoramentoDePrisoes"]
            },
            //outros...
        },
        "pena": {
            "value": {
                "pena imposta": {
                    "value": "8 anos e 6 meses de reclusão em regime fechado",
                    "label": "Pena Imposta",
                    "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                    "is_deleted": false
                },
                "recaptura": {
                    "value": "Não",
                    "label": "Recaptura",
                    "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                    "is_deleted": false
                },
            },
            "label": "Pena",
            "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
            "is_deleted": false
        }
    }
]
```

- A nova lista criada em um dos passos anteriores, **"Pena"**, também é adicionada como um objeto pertencente ao seu respectivo mandado.


### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
   "BancoNacionalDeMonitoramentoDePrisoes": [
        {
            "endpoint": "BancoNacionalDeMonitoramentoDePrisoes",
            "mandado": [
                {
                    "data de expedicao": "12/07/2022",
                    "data de validade": "16/11/2061",
                    "especie de prisao": "Preventiva",
                    "magistrado": "ALUIZIO PEREIRA DOS SANTOS",
                    "mandado": "Mandado de Prisão",
                    "municipio": "Campo Grande",
                    "numero do mandado de prisao": "0014717-72.2021",
                    "numero do processo": "0014717-72.2021.8.12.0001",
                    "orgao expedidor": "CAMPO GRANDE - 2a VARA DO TRIBUNAL DO JURI",
                    "pessoa": [
                        {
                            "alcunha": "Nao Informado",
                            "data nascimento": "24/12/1976",
                            "full name": "Juanil Miranda Lima",
                            "label default key": "Pessoa Objeto do Mandado de Prisao",
                            "naturalidade": "Campo Grande",
                            "nome mae": "Maria Aparecida de Miranda Rosa",
                            "nome pai": "Juvenciano Rosa de Lima",
                            "pena imposta": "0 ano(s) 0 mes(es) 0 dia(s).",
                            "recaptura": "Nao",
                            "sexo": "Masculino"
                        }
                    ],
                    "situacao": "Pendente de Cumprimento",
                    "tipificacoes penais": "1, 121, § 2º, IV; ; 1, 121, § 2º, I;"
                }
                //outros objetos
            ]
        }
    ]
    //outros retornos
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Mandados de Prisão",
        "subtitle": "Dados consultados no Banco Nacional de Monitoramento de Prisões (BNMP).",
        "source": ["BancoNacionalDeMonitoramentoDePrisoes"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
            {
                "numero": {
                    "value": "0001234-56.2023.8.19.0001.01.0001-01",
                    "label": "Número do Mandado",
                    "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "tipo de mandado": {
                        "value": "Preventiva",
                        "label": "Tipo de Mandado",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "situacao": {
                        "value": "Pendente de Cumprimento",
                        "label": "Situação",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "numero do processo": {
                        "value": "0001234-56.2023.8.19.0001",
                        "label": "Número do Processo",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "tipificacoes penais": {
                        "value": "Art. 157, § 2º, I e II do Código Penal - Roubo qualificado",
                        "label": "Tipificações Penais",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "data de expedicao": {
                        "value": "15/03/2023",
                        "label": "Data de Expedição",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "data de validade": {
                        "value": "15/03/2043",
                        "label": "Data de Validade",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "especie de prisao": {
                        "value": "Prisão Preventiva",
                        "label": "Espécie de Prisão",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "municipio": {
                        "value": "Rio de Janeiro",
                        "label": "Município",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "orgao expedidor": {
                        "value": "Vara Criminal da Comarca do Rio de Janeiro",
                        "label": "Órgão Expedidor",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    },
                    "magistrado": {
                        "value": "Dr. João Silva",
                        "label": "Magistrado",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                        "is_deleted": false
                    }
                },
               "pena": {
                    "value": {
                        "pena imposta": {
                            "value": "8 anos e 6 meses de reclusão em regime fechado",
                            "label": "Pena Imposta",
                            "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                            "is_deleted": false
                        },
                        "recaptura": {
                            "value": "Não",
                            "label": "Recaptura",
                            "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                            "is_deleted": false
                        },
                    },
                    "label": "Pena",
                    "source": ["BancoNacionalDeMonitoramentoDePrisoes"],
                    "is_deleted": false
                }
            },
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Telefones":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"phonenumber"**.

- Buscar no resultado da chave **""SintegraMA""** a lista com chave **"phonenumber"**.

- Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
    - "operadora",
    - "whatsapp",
    - "bookmark",

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Telefones"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Telefones"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP", "SintegraMA"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [ // lista com os telefones
            {
                "value": {
                    "phone number": {
                        "value": "553125860228",
                        "label": "Número do Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "label": "Telefone",
                "source": ["SNAP"],
                "is_deleted": false
            }
            //outros objetos da seção
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "phonenumber":[
                {
                    "operadora": "CLARO",
                    "phone number": "553125860228"
                },
                {
                    "operadora": "CLARO",
                    "phone number": "5531983462818",
                    "whatsapp": "Nao"
                },
            ]
            //outras propriedades
        }
    ],
    "SintegraMA": [
        {
            "endpoint": "SintegraMA",
            "phonenumber":[
                {
                    "operadora": "CLARO",
                    "phone number": "5531988684136"
                },
            ]
            //outras propriedades
        }
    ]
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Telefones",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "SintegraMA"],
        "data_count": 3,
        "data": [
            {
                "detalhes": [
                    {
                        "value": {
                            "phone number": {
                                "value": "553125860228",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5531983462818",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    {
                        "value": {
                            "phone number": {
                                "value": "5531988684136",
                                "label": "Número do Telefone",
                                "source": ["SintegraMA"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SintegraMA"],
                        "is_deleted": false
                    }
                    //outros objetos da seção
                ]
            }
        ]
    }
    //outras seções
]
```
</div>
</div>

## SEÇÃO: "Emails":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"emailaddress"**.

- Adicionar todos os objetos destas listas no **"data"** da seção **"Emails"** dentro de um objeto com chave **"detalhes"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Emails"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Emails"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [
            {
                "value": {
                    "email address": {
                        "value": "<EMAIL>",
                        "label": "Endereço de Email",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                },
                "label": "Email",
                "source": ["SNAP"],
                "is_deleted": false
            }
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "emailaddress":[
                {
                    "email address": "<EMAIL>"
                },
                {
                    "email address": "<EMAIL>"
                },
            ]
            //outras propriedades
        }
    ]
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Emails",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": 2,
        "data": {
            "detalhes": [
                {
                    "value": {
                        "email address": {
                            "value": "<EMAIL>",
                            "label": "Endereço de Email",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                    },
                    "label": "Email",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                {
                    "value": {
                        "email address": {
                            "value": "<EMAIL>",
                            "label": "Endereço de Email",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                    },
                    "label": "Email",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
            ]
        }
    }
    //outras seções
]
```
</div>
</div>

## SEÇÃO: "Endereços"

**Regras para processamento de dados:**

- No resultado da chave **"SNAP"**, buscar a lista com a chave **"location"**.
- No resultado da chave **"SintegraMTCPF"**, buscar a lista com a chave **"endereco"**.
- No resultado da chave **"SintegraPBCPF"**, buscar a lista com a chave **"endereco"**.
- No resultado da chave **"SintegraSECPF"**, buscar a lista com a chave **"endereco"**.
- No resultado da chave **"SintegraMA"**, buscar a lista com a chave **"endereco"**.
- No resultado da chave **"SintegraPRCPF"**, buscar a lista com a chave **"endereco"**.

- Adicionar todos os objetos provenientes dessas listas no **"data"** da seção **"Endereços"**.

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "label default key"


**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Endereços"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Endereços"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "SintegraMTCPF", "SintegraPBCPF", "SintegraSECPF", "SintegraMA", "SintegraPRCPF"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [ // lista com os endereços
            {
                "value": {
                    "logradouro": {
                        "value": "R TEOBALDO JOAQUIM DOS SANTOS",
                        "label": "Logradouro",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                    // outras propriedades
                },
                "label": "Endereço 1",
                "source": ["SNAP"],
                "is_deleted": false
            },
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
         {
            "endpoint": "SNAP",
            "location": [
                {
                    "area": "MG",
                    "bairro": "TRES BARRAS",
                    "cep ou zipcode": "32041100",
                    "city": "CONTAGEM",
                    "complemento": "C",
                    "label default key": "5531991701966",
                    "logradouro": "R TEOBALDO JOAQUIM DOS SANTOS"
                },
                {
                    "area": "MG",
                    "bairro": "TRES BARRAS",
                    "cep ou zipcode": "32041160",
                    "city": "CONTAGEM",
                    "complemento": "A",
                    "label default key": "5531991701966",
                    "logradouro": "R AMÉLIA ZEFERINA DE FREITAS"
                }
            ]
        }
    ]
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Endereços",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "SintegraMTCPF", "SintegraPBCPF", "SintegraSECPF", "SintegraMA", "SintegraPRCPF"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
           {
            "detalhes": [
                {
                    "value": {
                        "logradouro": {
                            "value": "R TEOBALDO JOAQUIM DOS SANTOS",
                            "label": "Logradouro",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "complemento": {
                            "value": "C",
                            "label": "Complemento",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "city": {
                            "value": "CONTAGEM",
                            "label": "Cidade",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "cep ou zipcode": {
                            "value": "32041100",
                            "label": "CEP",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "bairro": {
                            "value": "TRES BARRAS",
                            "label": "Bairro",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "area": {
                            "value": "MG",
                            "label": "Estado",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                    },
                    "label": "Endereço 1",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                {
                    "value": {
                        "logradouro": {
                            "value": "R AMÉLIA ZEFERINA DE FREITAS",
                            "label": "Logradouro",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "complemento": {
                            "value": "A",
                            "label": "Complemento",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "city": {
                            "value": "CONTAGEM",
                            "label": "Cidade",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "cep ou zipcode": {
                            "value": "32041160",
                            "label": "CEP",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "bairro": {
                            "value": "TRES BARRAS",
                            "label": "Bairro",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                        "area": {
                            "value": "MG",
                            "label": "Estado",
                            "source": ["SNAP"],
                            "is_deleted": false
                        },
                    },
                    "label": "Endereço 2",
                    "source": ["SNAP"],
                    "is_deleted": false
                }
            ]
           }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Parentes"

**Regras para processamento de dados:**

- No resultado da chave **"SNAP"**, buscar a lista com a chave **"pessoa"** (SNAP[0].pessoa).

- Buscar no resultado da chave **"SNAP"** a lista com chave **"pessoa"**, e achar o objeto desta lista que possui a propriedade **"bookmark"** com valor igual a **4**. Esse objeto traz as propriedades referentes a pessoa de interesse associada ao número do CPF buscado.
  - Buscar a lista com chave **"pessoa"** deste objeto (SNAP[0].pessoa["pessoa com bookmark 4"].pessoa).

- Adicionar os objetos provenientes dessas listas no **"data"** da seção **"Parentes"** SE o objeto se enquadrar em alguma das regras abaixo:
  - A propriedade **"label default key"** OU **"grau de parentesco"** CONTEM algum dos seguintes termos: ["MAE", "IRMA", "PAI", "TIO", "AVOS", "FILHO", "CONJUGE"]
    - Alterar em cada objeto que cair nessa regra, a propriedade **"label default key"** OU **"grau de parentesco"** para **"parentesco"**

- No resultado da chave **"BancoNacionalDeMonitoramentoDePrisoes"** buscar a lista com chave **"mandado"**.
  - Em cada objeto da lista **"mandado"** buscar a lista com chave **"pessoa"**.
    - Caso um objeto contenha a propriedade **"nome pai"**, adicionar o valor dessa propriedade como um novo objeto no **"data"** da seção **"Parentes"**, pois trata-se provavelmente do nome do pai da pessoa de interesse, e adicionar a propriedade **"parentesco"**  com valor igual a **"parente PAI"**.

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "bookmark"
   - "surname"
   - "first names"
   - "credilink label"
   - "pessoa"
   - "location"
   - "phonenumber"

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Parentes"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Parentes"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "BancoNacionalDeMonitoramentoDePrisoes"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "parentesco"
- "detalhes"
- "telefones"
- "enderecos"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "bookmark": 4,
                    "cbo": "716610",
                    "cpf": "942.367.041-53",
                    "ctps": "00070",
                    "data de admissao": "01/11/2017",
                    "data nascimento": "24/12/1976",
                    "descricao cbo": "PINTOR DE OBRAS",
                    "escolaridade": "ENSINO MEDIO COMPLETO",
                    "first names": "JUANIL",
                    "full name": "JUANIL MIRANDA LIMA",
                    "grau de instrucao": "6º AO 9º ANO DO ENSINO FUNDAMENTAL INCOMPLETO",
                    "idade": "47",
                    "info restricao": "* NADA CONSTA *",
                    "nacionalidade": "BRASILEIRO",
                    "pais do passaporte": "BRASIL",
                    "pessoa": [
                        {
                            "full name": "MARIA APARECIDA DE MIRANDA ROSA",
                            "label default key": "parente MAE" // pessoa parente
                        }
                    ],
                    "pis/pasep": "20019012750",
                    "procon": "(NAO TEM)",
                    "renda estimada": "2089,92",
                    "rg": "923465",
                    "sexo": "M",
                    "signo": "CAPRICORNIO",
                    "status receita": "REGULAR",
                    "surname": "MIRANDA LIMA",
                    "titulo de eleitor": "013947121953",
                    "uf de nascimento": "MS"
                },
                {
                    "cpf": "32113815893",
                    "credilink label": "parente IRMAO(A)",
                    "data nascimento": "06/02/1983",
                    "first names": "ADRIANA",
                    "full name": "ADRIANA APARECIDA ROSA BIANCHIN",
                    "label default key": "parente IRMAO(A)", // pessoa parente
                    "location": [
                        {
                            "area": "SP",
                            "bairro": "PRQ RESIDENCIAL JUNDIAI",
                            "cep ou zipcode": "13212510",
                            "city": "JUNDIAI",
                            "label default key": "11911533317",
                            "logradouro": "R NEUSA FARIA BINI",
                            "numero": "106"
                        }
                    ],
                    "pessoa": [
                        {
                            "full name": "MARIA APARECIDA MIRANDA ROSA",
                            "label default key": "parente MAE"
                        }
                    ],
                    "phonenumber": [
                        {
                            "operadora": "CLARO",
                            "phone number": "+5511911533317"
                        }
                    ],
                    "procon": "(NAO TEM)",
                    "sexo": "F",
                    "surname": "APARECIDA ROSA BIANCHIN",
                    "titulo de eleitor": "307837410167"
                }
            ]
        }
    ],
    "BancoNacionalDeMonitoramentoDePrisoes": [
        {
            "endpoint": "BancoNacionalDeMonitoramentoDePrisoes",
            "mandado": [
                {
                    "data de expedicao": "12/07/2022",
                    "data de validade": "16/11/2061",
                    "especie de prisao": "Preventiva",
                    "magistrado": "ALUIZIO PEREIRA DOS SANTOS",
                    "mandado": "Mandado de Prisão",
                    "municipio": "Campo Grande",
                    "numero do mandado de prisao": "0014717-72.2021.8.12.0001.01.0001-09",
                    "numero do processo": "0014717-72.2021.8.12.0001",
                    "orgao expedidor": "CAMPO GRANDE - 2a VARA DO TRIBUNAL DO JURI",
                    "pessoa": [
                        {
                            "alcunha": "Nao Informado",
                            "data nascimento": "24/12/1976",
                            "full name": "Juanil Miranda Lima",
                            "label default key": "Pessoa Objeto do Mandado de Prisao",
                            "naturalidade": "Campo Grande",
                            "nome mae": "Maria Aparecida de Miranda Rosa",
                            "nome pai": "Juvenciano Rosa de Lima", // Buscar parente pai
                            "pena imposta": "0 ano(s) 0 mes(es) 0 dia(s).",
                            "recaptura": "Nao",
                            "sexo": "Masculino"
                        }
                    ],
                    "situacao": "Pendente de Cumprimento",
                    "tipificacoes penais": "1, 121, § 2º, IV; ; 1, 121, § 2º, I;"
                },
                {
                    "data de expedicao": "27/11/2019",
                    "data de validade": "08/04/2039",
                    "especie de prisao": "Preventiva",
                    "magistrado": "Aluizio Pereira dos Santos",
                    "mandado": "Mandado de Prisão",
                    "municipio": "Campo Grande",
                    "numero do mandado de prisao": "0045032-54.2019.8.12.0001.01.0005-24",
                    "numero do processo": "0045032-54.2019.8.12.0001",
                    "orgao expedidor": "CAMPO GRANDE - 2a VARA DO TRIBUNAL DO JURI",
                    "pessoa": [
                        {
                            "alcunha": "Nao Informado",
                            "data nascimento": "24/12/1976",
                            "full name": "Juanil Miranda Lima",
                            "label default key": "Pessoa Objeto do Mandado de Prisao",
                            "naturalidade": "Campo Grande",
                            "nome mae": "Maria Aparecida de Miranda Rosa",
                            "nome pai": "Juvenciano Rosa de Lima",
                            "pena imposta": "0 ano(s) 0 mes(es) 0 dia(s).",
                            "recaptura": "Nao",
                            "sexo": "Masculino"
                        }
                    ],
                    "situacao": "Pendente de Cumprimento",
                    "tipificacoes penais": "1, 121, § 2º, IV;"
                }
            ]
        }
    ]
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Parentes",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP", "BancoNacionalDeMonitoramentoDePrisoes"], // listar todas as chaves usadas como fonte
        "data_count": 3,
        "data": [
            {
                "parentesco": {
                    "value": "parente MAE",
                    "label": "Parentesco",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "full name": {
                        "value": "MARIA APARECIDA DE MIRANDA ROSA",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                }
            },
            {
                "parentesco": {
                    "value": "parente PAI",
                    "label": "Parentesco",
                    "source": ["BancoNacionalDeMonitoramentoDePrisoes"]
                },
                "detalhes": {
                    "full name": {
                        "value": "JUVENCIANO ROSA DE LIMA",
                        "label": "Nome Completo",
                        "source": ["BancoNacionalDeMonitoramentoDePrisoes"]
                    },
                }
            },
            {
                "parentesco": {
                    "value": "parente IRMAO(A)",
                    "label": "Parentesco",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "full name": {
                        "value": "ADRIANA APARECIDA ROSA BIANCHIN",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                }
            }
        ]
    }
]
```
</div>
</div>

## SEÇÃO: "Sociedades"

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"empresa"**.
  - Nesta lista, buscar os resultados em que a chave **"label default key"**  ou  **"credilink label"** CONTEM o termo **"socio"**
- Buscar no resultado da chave **"CadastroNacionalPJ"** a lista com chave **"pessoa"** e no objeto buscar a lista **"empresa"** (CadastroNacionalPJ[0].pessoa[0].empresa).
- Buscar no resultado da chave **"SintegraMTCPF"** a lista com chave **"empresa"**.
- Buscar no resultado da chave **"SintegraMA"** a lista com chave **"empresa"**.
- Buscar no resultado da chave **"SintegraMGCPF"** a lista com chave **"empresa"**.

- Adicionar todos os objetos das lista acima no **"data"** da seção **"Sociedades"**.

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
     - "label default key"
     - "credilink label"

**Regras para a formatação da seção:**

- Ordenar as propriedades de cada objeto inserido na seção "Sociedades", colocando primeiro as propriedades que trazem informações mais relevantes para o processo investigativo:
  - "razao social"
  - "cnpj"
  - "inscricao estadual"
  - "tipo de ie"
  - "situacao inscricao estadual"
  - "cnae"
  - "data inicio de atividade"
  - "matriz"
  - "natureza juridica"
  - "entradasociedade"
  - "participacaosociedade",
  - "qualificação societária"
  - "situacao atual"
  - ...restante das propriedades...
  - "phonenumber"
  - "location"

- O objeto que constitui a seção **"Sociedades"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Sociedades"
  - **"subtitle":** "Dados consultados na API SNAP, SINTEGRA e Dados Abertos do Governo Federal."
  - **"source"**: ["SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "razao_social",
- "detalhes",
- "enderecos",
- "telefones",
- "emails"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
   "SNAP": [
        {
            "endpoint": "SNAP",
            "empresa": [
               {
                    "cargosociedade": "49",
                    "cnpj": "52643023000118",
                    "credilink label": "socio",
                    "desccargosociedade": "SOCIO ADMINISTRADOR",
                    "entradasociedade": "24/10/2023",
                    "label default key": "socio",
                    "participacaosociedade": "0",
                    "location": [
                        {
                            "area": "MG",
                            "bairro": "TRES BARRAS",
                            "cep ou zipcode": "32041100",
                            "city": "CONTAGEM",
                            "complemento": "C",
                            "label default key": "5531991701966",
                            "logradouro": "R TEOBALDO JOAQUIM DOS SANTOS"
                        }
                    ],
                    "phonenumber": [
                        {
                            "operadora": "CLARO",
                            "phone number": "5521992126794",
                            "whatsapp": "Nao"
                        }
                    ],
                    "razao social": "JBG AVERSA TRATAMENTO DE DADOS LTDA"
                }
                //outros objetos
            ]
        }
    ]
    //outros retornos
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Sociedades",
        "subtitle": "Dados consultados na API SNAP, SINTEGRA e Dados Abertos do Governo Federal.",
        "source": ["SNAP", "CadastroNacionalPJ", "SintegraMTCPF", "SintegraMA", "SintegraMGCPF"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
            {
                "razao_social": "JBG AVERSA TRATAMENTO DE DADOS LTDA",
                "detalhes": {
                    "cnpj": {
                        "value": "52643023000118",
                        "label": "CNPJ",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cargosociedade": {
                        "value": "49",
                        "label": "Cargo na Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "desccargosociedade": {
                        "value": "SOCIO ADMINISTRADOR",
                        "label": "Descrição do Cargo na Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                    // restante das propriedades
                },
                "enderecos": [
                    {
                        "value": {
                            "logradouro": {
                                "value": "R TEOBALDO JOAQUIM DOS SANTOS",
                                "label": "Logradouro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "complemento": {
                                "value": "C",
                                "label": "Complemento",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "city": {
                                "value": "CONTAGEM",
                                "label": "Cidade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "32041100",
                                "label": "CEP",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "TRES BARRAS",
                                "label": "Bairro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "area": {
                                "value": "MG",
                                "label": "Estado",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Endereço",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                ],
                "telefones": [
                    {
                        "value": {
                            "phonenumber": {
                                "value": "5521992126794",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                ]
            }
        ]
    }
]
```
</div>
</div>

## SEÇÃO: "Sócios"

**Regras para processamento de dados:**
 
- No resultado da chave **"SNAP"** -> lista com a chave **"pessoa"** (SNAP[0].pessoa.empresa).

**Regras para a formatação da seção:**

- O objeto que constituirá a seção **"Sócios"** e que será usado para a renderização dinâmica na interface deve conter as seguintes propriedades:
  - **"title":** "Sócios"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source":** ["SNAP", "CadastroNacionalPJ"] - Uma lista contendo as chaves utilizadas para obter os dados.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "detalhes"

```json
  "data": [
    {
        "detalhes": [
            {
                "value": {
                    "full name": {
                        "value": "GIOVANI THIBAU CHRISTOFARO",
                        "label": "Nome Completo",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cargo em sociedade": {
                        "value": "SOCIO ADMINISTRADOR",
                        "label": "Cargo em Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cpf": {
                        "value": "68000367653",
                        "label": "CPF",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "entradasociedade": {
                        "value": "04/09/2006",
                        "label": "Entrada em Sociedade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "label": "Sócio",
                "source": ["SNAP"],
                "is_deleted": false
            },
            //outros sócios
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
        {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "cargo em sociedade": "SOCIO ADMINISTRADOR",
                    "cpf": "68000367653",
                    "credilink_label": "socio",
                    "dtiniciosocio": "20060904",
                    "entradasociedade": "04/09/2006",
                    "first names": "GIOVANI",
                    "full name": "GIOVANI THIBAU CHRISTOFARO",
                    "label default key": "socio",
                    "participacaosociedade": "49",
                    "surname": "THIBAU CHRISTOFARO"
                }
            ]
        }
    ]
}
```
</div> 
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Sócios",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"], // listar todas as chaves usadas como fonte
        "data_count": 1,
        "data": [
           {
                "detalhes": [
                    {
                        "value": {
                            "full name": {
                                "value": "GIOVANI THIBAU CHRISTOFARO",
                                "label": "Nome Completo",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cargo em sociedade": {
                                "value": "SOCIO ADMINISTRADOR",
                                "label": "Cargo em Sociedade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cpf": {
                                "value": "68000367653",
                                "label": "CPF",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "entradasociedade": {
                                "value": "04/09/2006",
                                "label": "Entrada em Sociedade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Sócio",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    //outros sócios
                ]
            }
        ]
    }
     //outras seções
]
```
</div> 
</div>


## SEÇÃO: "Vínculos Empregatícios":

**Regras para processamento de dados:**

- Buscar no resultado da chave **"SNAP"** a lista com chave **"remuneracao"**.

- Buscar Dentro de cada objeto da chave **"remuneracao"**, uma outra lista com chave **"empresa"**, e copiar a propriedades **"cnpj** para a raiz deste objeto.

- Adicionar todos os objetos das lista acima no **"data"** da seção **"Sociedades"**.

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Vínculos Empregatícios"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Vínculos Empregatícios"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "empresa_pagadora"
- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
       {
            "endpoint": "SNAP",
            "remuneracao": [
                {
                    "data admissao": "05/08/2014",
                    "empresa": [
                        {
                            "cnpj": "26389171000148",
                            "razao social": "MAXCOR INDUSTRIA DE ETIQUETAS LTDA"
                        }
                    ],
                    "empresa pagadora": "MAXCOR INDUSTRIA DE ETIQUETAS LTDA",
                    "valor": "R$ 1414,41"
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Vínculos Empregatícios",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": 1,
        "data": [
            {
                "empresa_pagadora": {
                    "value": "MAXCOR INDUSTRIA DE ETIQUETAS LTDA",
                    "label": "Empresa Pagadora",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "26389171000148",
                        "label": "CNPJ",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "data admissao": {
                        "value": "05/08/2014",
                        "label": "Admissão",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "valor": {
                        "value": "R$ 1414,41",
                        "label": "Remuneração (R$)",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "razao social": {
                        "value": "MAXCOR INDUSTRIA DE ETIQUETAS LTDA",
                        "label": "Razão Social",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Processos":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"Escavador"** -> lista **"processos"** (Escavador[0].processos)

**Importante:** Na estrutura de cada processo, é necessário renderizar a propriedade "numero do processo" separadamente, no mesmo nível que "detalhes" (que contém as propriedades do processo que não são listas) e "movimentacoes". Além disso, deve-se criar listas para cada tipo de participante com base no valor da propriedade "label default key" dos objetos em "pessoa" e "empresa".

As listas de participantes devem seguir a ordem abaixo e usar o formato snake_case para as chaves. Em cada processo, esses valores são buscados dentro da lista pessoa ou empresa, e dependendo da origem, leva o valor da propriedade "label default key" e o sufixo da origem para que a interface possa identificar:

**Lista de todas as chaves possíveis para participantes (em ordem):**
- advogado
- autor_pessoa
- autor_empresa
- reu_pessoa
- reu_empresa
- requerido_pessoa
- requerido_empresa
- executado_pessoa
- executado_empresa
- juizo_deprecante_pessoa
- juizo_deprecante_empresa
- deprecado_pessoa
- deprecado_empresa
- recorrente_pessoa
- recorrente_empresa
- outro_pessoa
- outro_empresa
- impetrante_pessoa
- impetrante_empresa
- paciente_pessoa
- paciente_empresa
- correu_pessoa
- correu_empresa
- juiz
- impetrado_pessoa
- impetrado_empresa
- interessado_pessoa
- interessado_empresa
- apelante_pessoa
- apelante_empresa
- apelado_pessoa
- apelado_empresa
- agravado_pessoa
- agravado_empresa
- agravante_pessoa
- agravante_empresa
- requerente_pessoa
- requerente_empresa
- recorrido_pessoa
- recorrido_empresa
- vitima_pessoa
- vitima_empresa
- assistente_pessoa
- assistente_empresa
- representante_pessoa
- representante_empresa
- ministerio_publico_empresa
- litisconsorte_empresa

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Processos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Processos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["Escavador"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

```json
  "data": [
    {
        "numero": { // objeto com o número do processo
            "value": "02116589820188130024",
            "label": "Número do Processo",
            "source": ["Escavador"],
            "is_deleted": false
        },
        "detalhes": { // objeto com os detalhes do processo
            "instancia": {
                "value": "Primeiro Grau",
                "label": "Instância",
                "source": ["Escavador"],
                "is_deleted": false
            },
            "orgao": {
                "value": "Diario de Justica do Estado de Minas Gerais",
                "label": "Órgão",
                "source": ["Escavador"],
                "is_deleted": false
            },
            "data da remessa": {
                "value": "2018-08-23",
                "label": "Data da Remessa",
                "source": ["Escavador"],
                "is_deleted": false
            },
            "data da instauracao": {
                "value": "2018-06-19",
                "label": "Data da Instauração",
                "source": ["Escavador"],
                "is_deleted": false
            }
        },
        "movimentacoes": { // objeto com as movimentações do processo
            "value": "Data - Conteudo - Tipo,2018-09-25 - REMETIDOS OS AUTOS...",
            "label": "Movimentações",
            "source": ["Escavador"],
            "is_deleted": false
        },
        "advogado": [ // lista com os advogados do processo
            {
                "value": {
                    "cpf": {
                        "value": "711.082.896-49",
                        "label": "CPF",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "full name": {
                        "value": "FERNANDO DINIZ PACHECO",
                        "label": "Nome",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "oab": {
                        "value": "123566/MG",
                        "label": "OAB",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                },
                "label": "Advogado",
                "source": ["Escavador"],
                "is_deleted": false
            }
        ],
        "recorrente_pessoa": [ // lista com os recorrentes do processo
            {
                "value": {
                    "full name": {
                        "value": "ANDREIA FERREIRA DE SOUZA",
                        "label": "Nome",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                },
                "label": "Recorrente",
                "source": ["Escavador"],
                "is_deleted": false
            }
        ],

    }
    //outros objetos de processos
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "Escavador": [
       {
            "endpoint": "Escavador",
            "processos": [
                {
                    "advogado": [
                        {
                            "cpf": "71108289649",
                            "full name": "FERNANDO DINIZ PACHECO",
                            "label default key": "Advogado",
                            "oab": "123566/MG"
                        }
                    ],
                    "data da remessa": "2018-08-23",
                    "data instauracao": "2018-06-19",
                    "empresa": [
                        {
                            "cnpj": "01109184000195",
                            "label default key": "Recorrido",
                            "razao social": "UNIVERSO ONLINE S.A"
                        },
                    ],
                    "instancia": "Primeiro Grau",
                    "movimentacoes": "Data - Conteudo - Tipo,2018-09-25 - REMETIDOS OS AUTOS...",
                    "numero do processo": "02116589820188130024",
                    "orgao": "Diario de Justica do Estado de Minas Gerais",
                    "pessoa": [
                        {
                            "full name": "ANDREIA FERREIRA DE SOUZA",
                            "label default key": "Recorrente"
                        },
                        {
                            "cpf": "56528221634",
                            "full name": "SILVEMAR JOSE HENRIQUES SALGADO",
                            "label default key": "Juiz"
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Processos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["Escavador"],
        "data_count": 1,
        "data": [
            {
                "numero": {
                    "value": "02116589820188130024",
                    "label": "Número do Processo",
                    "source": ["Escavador"],
                    "is_deleted": false
                },
                "detalhes": {
                    "instancia": {
                        "value": "Primeiro Grau",
                        "label": "Instância",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "orgao": {
                        "value": "Diario de Justica do Estado de Minas Gerais",
                        "label": "Órgão",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "data da remessa": {
                        "value": "2018-08-23",
                        "label": "Data da Remessa",
                        "source": ["Escavador"],
                        "is_deleted": false
                    },
                    "data da instauracao": {
                        "value": "2018-06-19",
                        "label": "Data da Instauração",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                },
                "movimentacoes": {
                    "value": "Data - Conteudo - Tipo,2018-09-25 - REMETIDOS OS AUTOS...",
                    "label": "Movimentações",
                    "source": ["Escavador"],
                    "is_deleted": false
                },
                "advogado": [
                    {
                        "value": {
                            "cpf": {
                                "value": "711.082.896-49",
                                "label": "CPF",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "full name": {
                                "value": "FERNANDO DINIZ PACHECO",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "oab": {
                                "value": "123566/MG",
                                "label": "OAB",
                                "source": ["Escavador"],
                                "is_deleted": false
                            }
                        },
                        "label": "Advogado",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "recorrente_pessoa": [
                    {
                        "value": {
                            "full name": {
                                "value": "ANDREIA FERREIRA DE SOUZA",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Recorrente",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "juiz": [
                    {
                        "value": {
                            "cpf": {
                                "value": "565.282.216-34",
                                "label": "CPF",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "full name": {
                                "value": "SILVEMAR JOSE HENRIQUES SALGADO",
                                "label": "Nome",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Juiz",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ],
                "recorrido_empresa": [
                    {
                        "value": {
                            "cnpj": {
                                "value": "01.109.184/0001-95",
                                "label": "CNPJ",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                            "razao social": {
                                "value": "UNIVERSO ONLINE S.A",
                                "label": "Razão Social",
                                "source": ["Escavador"],
                                "is_deleted": false
                            },
                        },
                        "label": "Recorrido",
                        "source": ["Escavador"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Recursos Públicos Recebidos":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"PortalDaTransparenciaDeMinasGerais"** -> lista **"pessoa"** (PortalDaTransparenciaDeMinasGerais[0].pessoa)
  - **"PortalDaTransparenciaDeMinasGerais"** -> lista **"despesas publicas recursos recebidos"** (PortalDaTransparenciaDeMinasGerais[0]["despesas publicas recursos recebidos"])
  - **"PortalDaTransparenciaDoAmazonas"** -> lista **"despesas publicas recursos recebidos"** (PortalDaTransparenciaDoAmazonas[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaSC"** -> lista **"despesas publicas recursos recebidos"** (TransparenciaSC[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaManausCPF"** -> lista **"empenho"** (TransparenciaManausCPF[0].empenho)
  - **"TransparenciaDF"** -> lista **"despesas publicas recursos recebidos"** (TransparenciaDF[0]["despesas publicas recursos recebidos"])
  - **"TransparenciaDF"** -> lista **"viagem"** (TransparenciaDF[0].viagem)

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Recursos Públicos Recebidos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Recursos Públicos Recebidos"
  - **"subtitle":** "Dados consultados em portais de Transparência estaduais e federal."
  - **"source"**: ["PortalDaTransparenciaDeMinasGerais", "PortalDaTransparenciaDoAmazonas", "TransparenciaSC", "TransparenciaManausCPF", "TransparenciaDF"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "orgao" (Caso não tenha propriedade com o nome do orgão, criar um objeto com a propriedade "value" com o valor "Não Informado")
- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json

```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Recursos Públicos Recebidos",
        "subtitle": "Dados consultados em portais de Transparência estaduais e federal.",
        "source": ["PortalDaTransparenciaDeMinasGerais", "PortalDaTransparenciaDoAmazonas", "TransparenciaSC", "TransparenciaManausCPF", "TransparenciaDF"],
        "data_count": 0,
        "data": [
            {
                "orgao": {
                    "value": "Prefeitura de Manaus",
                    "label": "Órgão",
                    "source": ["TransparenciaManausCPF"],
                    "is_deleted": false
                },
                "detalhes": {
                    "ano": {
                        "value": "2023",
                        "label": "Ano",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    },
                    "mes": {
                        "value": "07",
                        "label": "Mês",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    },
                    "valor": {
                        "value": "R$ 1.000,00",
                        "label": "Valor (R$)",
                        "source": ["TransparenciaManausCPF"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Serviço Público":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TransparenciaPRNome"** -> lista **"servidor publico"** (TransparenciaPRNome[0]["servidor publico"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Serviço Público"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Serviço Público"
  - **"subtitle":** "Dados consultados em portais de Transparência estaduais e federal."
  - **"source"**: ["TransparenciaPRNome"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] **Segue abaixo o exemplo do formato**

### Chaves esperadas dentro do objeto da lista **"data"**:

- "servidor"
- "detalhes"

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json

```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Serviço Público",
        "subtitle": "Dados consultados em portais de Transparência estaduais e federal.",
        "source": ["TransparenciaPRNome"],
        "data_count": 0,
        "data": [
            {
                "servidor": {
                    "value": "João Silva Santos",
                    "label": "Nome do Servidor",
                    "source": ["TransparenciaPRNome"],
                    "is_deleted": false
                },
                "detalhes": {
                    "carreira": {
                        "value": "Analista de Sistemas",
                        "label": "Função",
                        "source": ["TransparenciaPRNome"],
                        "is_deleted": false
                    },
                    "instituicao": {
                        "value": "Secretaria de Estado da Administração e da Previdência",
                        "label": "Instituição",
                        "source": ["TransparenciaPRNome"],
                        "is_deleted": false
                    },
                    "quadro funcional": {
                        "value": "Quadro Próprio do Poder Executivo - QPPE",
                        "label": "Quadro Funcional",
                        "source": ["TransparenciaPRNome"],
                        "is_deleted": false
                    },
                    "municipio": {
                        "value": "Curitiba",
                        "label": "Cidade / PR",
                        "source": ["TransparenciaPRNome"],
                        "is_deleted": false
                    }
                }
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Diários Oficiais - CPF":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"EscavadorDOCPF"** -> lista **"diario oficial"** (EscavadorDOCPF[0]["diario oficial"])
  - **"QueridoDiarioCPF"** -> lista **"diario oficial"** (QueridoDiarioCPF[0]["diario oficial"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Diários Oficiais - CPF"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Diários Oficiais - CPF"
  - **"subtitle":** "Dados consultados na API SNAP através do CPF de entrada.",
  - **"source"**: ["EscavadorDOCPF", "QueridoDiarioCPF"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "local"
- "detalhes"
- "descricao" (apenas para EscavadorDOCPF)
- "ocorrencia" (apenas para QueridoDiarioCPF)

```json
  "data": [
    {
        "local": {
            "value": "Poder Executivo (Suplemento B)",
            "label": "Local",
            "source": ["EscavadorDOCPF"],
            "is_deleted": false
        },
        "detalhes": {
            "data": {
                "value": "2013-09-16",
                "label": "Data",
                "source": ["EscavadorDOCPF"],
                "is_deleted": false
            },

            "dados adicionais": {
                "value": "Diario Oficial do Estado do Rio de Janeiro",
                "label": "Dados Adicionais",
                "source": ["EscavadorDOCPF"],
                "is_deleted": false
            },
            "link": {
                "value": "https://www.escavador.com/diarios/814413/DOERJ/poder-executivo-(suplemento-b)/2013-09-16?page=169",
                "label": "Link",
                "source": ["EscavadorDOCPF"],
                "is_deleted": false
            }
        },
        "descricao": {
            "value": "...609.34 0 617,1 70,91 108,21..",
            "label": "Descrição",
            "source": ["EscavadorDOCPF"],
            "is_deleted": false
        },
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "EscavadorDOCPF": [
       {
            "endpoint": "EscavadorDOCPF",
            "diario oficial": [
                {
                    "dados adicionais": "Diario Oficial do Estado do Rio de Janeiro",
                    "data": "2013-09-16",
                    "descricao": "...609.34 0 617,1 70,91 108,21..",
                    "link": "https://www.escavador.com/diarios/814413/DOERJ/poder-executivo-(suplemento-b)/2013-09-16?page=169",
                    "local": "Poder Executivo (Suplemento B)"
                }
            ]
       }
    ],
    "QueridoDiarioCPF": [
       {
            "endpoint": "QueridoDiarioCPF",
            "diario oficial": [
                {
                    "data": "2023-07-10",
                    "edicao extra?": "Não",
                    "link": "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx",
                    "local": "São Paulo",
                    "uf": "SP",
                    "frase": [
                        {
                            "texto": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo."
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Diários Oficiais - CPF",
        "subtitle": "Dados consultados na API SNAP através do CPF de entrada.",
        "source": ["EscavadorDOCPF", "QueridoDiarioCPF"],
        "data_count": 2,
        "data": [
            {
                "local": {
                    "value": "Poder Executivo (Suplemento B)",
                    "label": "Local",
                    "source": ["EscavadorDOCPF"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "2013-09-16",
                        "label": "Data",
                        "source": ["EscavadorDOCPF"],
                        "is_deleted": false
                    },
                    "dados adicionais": {
                        "value": "Diario Oficial do Estado do Rio de Janeiro",
                        "label": "Dados Adicionais",
                        "source": ["EscavadorDOCPF"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://www.escavador.com/diarios/814413/DOERJ/poder-executivo-(suplemento-b)/2013-09-16?page=169",
                        "label": "Link",
                        "source": ["EscavadorDOCPF"],
                        "is_deleted": false
                    }
                },
                "descricao": {
                    "value": "...609.34 0 617,1 70,91 108,21..",
                    "label": "Descrição",
                    "source": ["EscavadorDOCPF"],
                    "is_deleted": false
                },
            },
            {
                "local": {
                    "value": "São Paulo / SP",
                    "label": "Cidade",
                    "source": ["QueridoDiarioCPF"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "2023-07-10",
                        "label": "Data",
                        "source": ["QueridoDiarioCPF"],
                        "is_deleted": false
                    },
                    "edicao extra?": {
                        "value": "Não",
                        "label": "Edição Extra?",
                        "source": ["QueridoDiarioCPF"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://www.imprensaoficial.com.br/DO/BuscaDO2001Documento_11_4.aspx",
                        "label": "Link",
                        "source": ["QueridoDiarioCPF"],
                        "is_deleted": false
                    }
                },
                "ocorrencia": {
                    "value": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo.",
                    "label": "Texto",
                    "source": ["QueridoDiarioCPF"],
                    "is_deleted": false
                }
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Diários Oficiais - NOME":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"EscavadorDONome"** -> lista **"diario oficial"** (EscavadorDONome[0]["diario oficial"])
  - **"QueridoDiarioNome"** -> lista **"diario oficial"** (QueridoDiarioNome[0]["diario oficial"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Diários Oficiais - NOME"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Diários Oficiais - NOME"
  - **"subtitle":** "Dados consultados na API SNAP através do NOME da pessoa."
  - **"source"**: ["EscavadorDONome", "QueridoDiarioNome"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "local"
- "detalhes"
- "descricao" (apenas para EscavadorDONome)
- "ocorrencia" (apenas para QueridoDiarioNome)

```json
  "data": [
    {
        "local": {
            "value": "Ministerio Publico",
            "label": "Local",
            "source": ["EscavadorDONome"],
            "is_deleted": false
        },
        "detalhes": {
            "data": {
                "value": "2010-03-03",
                "label": "Data",
                "source": ["EscavadorDONome"],
                "is_deleted": false
            },
            "dados adicionais": {
                "value": "Diario Oficial do Estado do Rio de Janeiro",
                "label": "Dados Adicionais",
                "source": ["EscavadorDONome"],
                "is_deleted": false
            },
            "link": {
                "value": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1",
                "label": "Link",
                "source": ["EscavadorDONome"],
                "is_deleted": false
            }
        },
        "descricao": {
            "value": "...fevereiro de 2010, ELISETE...",
            "label": "Descrição",
            "source": ["EscavadorDONome"],
            "is_deleted": false
        },
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "EscavadorDONome": [
       {
            "endpoint": "EscavadorDONome",
            "diario oficial": [
                {
                    "dados adicionais": "Diario Oficial do Estado do Rio de Janeiro",
                    "data": "2010-03-03",
                    "descricao": "...fevereiro de 2010, ELISETE...",
                    "link": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1",
                    "local": "Ministerio Publico"
                }
            ]
       }
    ],
    "QueridoDiarioNome": [
       {
            "endpoint": "QueridoDiarioNome",
            "diario oficial": [
                {
                    "data": "2022-05-20",
                    "edicao extra?": "Não",
                    "link": "https://dom-web.pbh.gov.br/",
                    "local": "Belo Horizonte",
                    "uf": "MG",
                    "frase": [
                        {
                            "texto": "PORTARIA SMASAC Nº 055/2022 - A Secretária Municipal de Assistência Social, Segurança Alimentar e Cidadania, no uso de suas atribuições legais, resolve nomear MARIA SILVA, matrícula 123.456-7, para a função de Coordenadora do Centro de Referência de Assistência Social - CRAS Norte."
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Diários Oficiais - NOME",
        "subtitle": "Dados consultados na API SNAP através do NOME da pessoa.",
        "source": ["EscavadorDONome", "QueridoDiarioNome"],
        "data_count": 2,
        "data": [
            {
                "local": {
                    "value": "Ministerio Publico",
                    "label": "Local",
                    "source": ["EscavadorDONome"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "2010-03-03",
                        "label": "Data",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    },
                    "dados adicionais": {
                        "value": "Diario Oficial do Estado do Rio de Janeiro",
                        "label": "Dados Adicionais",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://www.escavador.com/diarios/829767/DOERJ/ministerio-publico/2010-03-03?page=1",
                        "label": "Link",
                        "source": ["EscavadorDONome"],
                        "is_deleted": false
                    }
                },
                "descricao": {
                    "value": "...fevereiro de 2010, ELISETE...",
                    "label": "Descrição",
                    "source": ["EscavadorDONome"],
                    "is_deleted": false
                }
            },
            {
                "local": {
                    "value": "Belo Horizonte / MG",
                    "label": "Cidade",
                    "source": ["QueridoDiarioNome"],
                    "is_deleted": false
                },
                "detalhes": {
                    "data": {
                        "value": "2022-05-20",
                        "label": "Data",
                        "source": ["QueridoDiarioNome"],
                        "is_deleted": false
                    },
                    "edicao extra?": {
                        "value": "Não",
                        "label": "Edição Extra?",
                        "source": ["QueridoDiarioNome"],
                        "is_deleted": false
                    },
                    "link": {
                        "value": "https://dom-web.pbh.gov.br/",
                        "label": "Link",
                        "source": ["QueridoDiarioNome"],
                        "is_deleted": false
                    }
                },
                "ocorrencias":
                    {
                        "value": "O Secretário Municipal de Administração resolve nomear JOÃO DA SILVA, RG 12.345.678-9, para o cargo de Assistente Administrativo, referência QPA-13, pelo prazo de 12 meses.",
                        "label": "Texto",
                        "source":  ["QueridoDiarioNome"],
                        "is_deleted": false
                    }

            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Filiação Partidária":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEFiliacaoPartidaria"** -> lista **"partido politico"** (TSEFiliacaoPartidaria[0]["partido politico"])

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Filiação Partidária"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Filiação Partidária"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEFiliacaoPartidaria"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "sigla"
- "detalhes"
- "vinculos" (opcional)

```json
  "data": [
    {
        "sigla": {
            "value": "PT",
            "label": "Sigla",
            "source": ["TSEFiliacaoPartidaria"],
            "is_deleted": false
        },
        "detalhes": {
            "uf": {
                "value": "SP",
                "label": "Estado",
                "source": ["TSEFiliacaoPartidaria"],
                "is_deleted": false
            },
            "data consulta": {
                "value": "15/05/2023",
                "label": "Data da Consulta",
                "source": ["TSEFiliacaoPartidaria"],
                "is_deleted": false
            }
        },
        "vinculos": [
            {
                "data registro": {
                    "value": "10/03/2010",
                    "label": "Data Registro",
                    "source": ["TSEFiliacaoPartidaria"],
                    "is_deleted": false
                },
                "situacao": {
                    "value": "Regular",
                    "label": "Situação",
                    "source": ["TSEFiliacaoPartidaria"],
                    "is_deleted": false
                },
                "tipo": {
                    "value": "Filiação",
                    "label": "Tipo",
                    "source": ["TSEFiliacaoPartidaria"],
                    "is_deleted": false
                }
            }
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEFiliacaoPartidaria": [
       {
            "endpoint": "TSEFiliacaoPartidaria",
            "partido politico": [
                {
                    "sigla": "PT",
                    "uf": "SP",
                    "data consulta": "15/05/2023",
                    "vinculo": [
                        {
                            "data registro": "10/03/2010",
                            "situacao": "Regular",
                            "tipo": "Filiação"
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Filiação Partidária",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEFiliacaoPartidaria"],
        "data_count": 1,
        "data": [
            {
                "sigla": {
                    "value": "PT",
                    "label": "Sigla",
                    "source": ["TSEFiliacaoPartidaria"],
                    "is_deleted": false
                },
                "detalhes": {
                    "uf": {
                        "value": "SP",
                        "label": "Estado",
                        "source": ["TSEFiliacaoPartidaria"],
                        "is_deleted": false
                    },
                    "data consulta": {
                        "value": "15/05/2023",
                        "label": "Data da Consulta",
                        "source": ["TSEFiliacaoPartidaria"],
                        "is_deleted": false
                    }
                },
                "vinculos": [
                    {
                        "data registro": {
                            "value": "10/03/2010",
                            "label": "Data Registro",
                            "source": ["TSEFiliacaoPartidaria"],
                            "is_deleted": false
                        },
                        "situacao": {
                            "value": "Regular",
                            "label": "Situação",
                            "source": ["TSEFiliacaoPartidaria"],
                            "is_deleted": false
                        },
                        "tipo": {
                            "value": "Filiação",
                            "label": "Tipo",
                            "source": ["TSEFiliacaoPartidaria"],
                            "is_deleted": false
                        }
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Possíveis Contatos":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"SNAP"** -> lista **"pessoa"** (SNAP[0]["pessoa"])
    - SE o a propriedade **"label default key"** OU **"grau de parentesco"** NÃO CONTEM nenhum dos seguintes termos: ["MAE", "IRMA", "PAI", "TIO", "AVOS", "FILHO", "CONJUGE"] E NÃO for o objeto com **"bookmark"** igual a **4**.

  - **"SNAP"** -> lista **"empresa"** (SNAP[0]["empresa"])
    - SE o a propriedade **"label default key"** for igual a **"outros_contatos"**

-  Existem algumas propriedades do objeto que não devem ser adicionadas nesse processo. São elas:
   - "bookmark"
   - "surname"
   - "first names"
   - "credilink label"
   - "label default key"
   - "pessoa"

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contatos"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contatos"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["SNAP"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Chaves esperadas dentro do objeto da lista **"data"**:

- "nome_completo"
- "detalhes"
- "telefones"
- "enderecos"

```json
  "data": [
    {
        "nome_completo": {
            "value": "AILTON RAMOS DA SILVA JUNIOR",
            "label": "Nome",
            "source": ["SNAP"],
            "is_deleted": false
        },
        "mae": {
            "value": "ANTONIA MARIA O RAMOS SILVA",
            "label": "Mãe",
            "source": ["SNAP"],
            "is_deleted": false
        },
        "detalhes": {
            "cpf": {
                "value": "08936532766",
                "label": "CPF",
                "source": ["SNAP"],
                "is_deleted": false
            },
            "data nascimento": {
                "value": "04/06/1982",
                "label": "Data de Nascimento",
                "source": ["SNAP"],
                "is_deleted": false
            },
            "label default key": {
                "value": "outros_contatos",
                "label": "Tipo de Contato",
                "source": ["SNAP"],
                "is_deleted": false
            }
        },
        "telefones": [
            {
                "value": {
                    "phone number": "552124669210",
                    "operadora": "OI"
                },
                "label": "Telefone",
                "source": ["SNAP"],
                "is_deleted": false
            }
        ],
        "enderecos": [
            {
                "value": {
                    "endereco": {
                        "value": "RMAGNO MARTINS",
                        "label": "Logradouro",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "numero": {
                        "value": "128",
                        "label": "Número",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "complemento": {
                        "value": "CS 2105",
                        "label": "Complemento",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "bairro": {
                        "value": "FREGUESIA",
                        "label": "Bairro",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "city": {
                        "value": "RIO DE JANEIRO",
                        "label": "Cidade",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "area": {
                        "value": "RJ",
                        "label": "Estado",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "cep ou zipcode": {
                        "value": "21911430",
                        "label": "CEP",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "label": "Endereço",
                "source": ["SNAP"],
                "is_deleted": false
            }
        ]
    }
]
```

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "SNAP": [
       {
            "endpoint": "SNAP",
            "pessoa": [
                {
                    "cpf": "08936532766",
                    "data nascimento": "04/06/1982",
                    "dt_istalacao": "05/02/2019",
                    "first names": "AILTON",
                    "full name": "AILTON RAMOS DA SILVA JUNIOR",
                    "label default key": "outros_contatos",
                    "location": [
                        {
                            "area": "RJ",
                            "bairro": "FREGUESIA",
                            "cep ou zipcode": "21911430",
                            "city": "RIO DE JANEIRO",
                            "complemento": "CS 2105",
                            "endereco": "RMAGNO MARTINS",
                            "nome": "RMAGNO MARTINS",
                            "numero": "128"
                        }
                    ],
                    "pessoa": [
                        {
                            "full name": "ANTONIA MARIA O RAMOS SILVA",
                            "label default key": "parente MAE"
                        }
                    ],
                    "phonenumber": [
                        {
                            "operadora": "OI",
                            "phone number": "552124669210"
                        }
                    ]
                }
            ]
       },
       {
            "endpoint": "SNAP",
            "empresa": [
                {
                    "razao social": "EMPRESA XYZ LTDA",
                    "cnpj": "12345678000190",
                    "label default key": "outros_contatos",
                    "phonenumber": [
                        {
                            "operadora": "CLARO",
                            "phone number": "551133334444"
                        }
                    ],
                    "location": [
                        {
                            "endereco": "AVENIDA PAULISTA",
                            "numero": "1000",
                            "bairro": "BELA VISTA",
                            "city": "SÃO PAULO",
                            "area": "SP",
                            "cep ou zipcode": "01310100"
                        }
                    ]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contatos",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["SNAP"],
        "data_count": 2,
        "data": [
            {
                "nome": {
                    "value": "AILTON RAMOS DA SILVA JUNIOR",
                    "label": "Nome",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "nome_da_mae": {
                    "value": "ANTONIA MARIA O RAMOS SILVA",
                    "label": "Mãe",
                    "source": ["SNAP"],
                    "is_deleted": false
                    },
                    "cpf": {
                        "value": "08936532766",
                        "label": "CPF",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "data nascimento": {
                        "value": "04/06/1982",
                        "label": "Data de Nascimento",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "dt_istalacao": {
                        "value": "05/02/2019",
                        "label": "Data de Instalação",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "label default key": {
                        "value": "outros_contatos",
                        "label": "Tipo de Contato",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "telefones": [
                    {
                        "value": {
                            "phone number": {
                                "value": "552124669210",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                ],
                "enderecos": [
                    {
                        "value": {
                            "endereco": {
                                "value": "RMAGNO MARTINS",
                                "label": "Logradouro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "numero": {
                                "value": "128",
                                "label": "Número",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "complemento": {
                                "value": "CS 2105",
                                "label": "Complemento",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "FREGUESIA",
                                "label": "Bairro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "city": {
                                "value": "RIO DE JANEIRO",
                                "label": "Cidade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "area": {
                                "value": "RJ",
                                "label": "Estado",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "21911430",
                                "label": "CEP",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Endereço",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                ]
            },
            {
                "nome": {
                    "value": "EMPRESA XYZ LTDA",
                    "label": "Razão Social",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "12345678000190",
                        "label": "CNPJ",
                        "source": ["SNAP"],
                        "is_deleted": false
                    },
                    "label default key": {
                        "value": "outros_contatos",
                        "label": "Tipo de Contato",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                },
                "telefones": [
                    {
                        "value": {
                            "phone number": {
                                "value": "551133334444",
                                "label": "Número do Telefone",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                        },
                        "label": "Telefone",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                ],
                "enderecos": [
                    {
                        "value": {
                            "endereco": {
                                "value": "AVENIDA PAULISTA",
                                "label": "Logradouro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "numero": {
                                "value": "1000",
                                "label": "Número",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "bairro": {
                                "value": "BELA VISTA",
                                "label": "Bairro",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "city": {
                                "value": "SÃO PAULO",
                                "label": "Cidade",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "area": {
                                "value": "SP",
                                "label": "Estado",
                                "source": ["SNAP"],
                                "is_deleted": false
                            },
                            "cep ou zipcode": {
                                "value": "01310100",
                                "label": "CEP",
                                "source": ["SNAP"],
                                "is_deleted": false
                            }
                        },
                        "label": "Endereço",
                        "source": ["SNAP"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Possíveis Contas em Sites":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"PAIscpf"** -> lista **"PAIs"** (PAIscpf[0]["PAIs"])
  - **"ProvedorDeAplicacacaoDaInternetcpf"** -> lista **"provedor de aplicacoes de internet"** (ProvedorDeAplicacacaoDaInternetcpf[0]["provedor de aplicacoes de internet"])

  - Adicionar propriedade em cada objeto com o valor do CPF de entrada e com a chave "Entrada CPF". Ex.: {"entrada cpf": "11045231673"}

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Possíveis Contas em Sites"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Possíveis Contas em Sites"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- O objeto principal contém uma propriedade `detalhes` que é um array de itens
- Cada item contém:
  - `site`: Informação sobre o site (aplicação)
  - `detalhes`: Objeto com informações detalhadas (found, entrada_cpf, alerta, tipo_alerta)

### Renderização:
- Os sites são renderizados em uma grade de duas colunas
- Cada site é exibido com um número sequencial (Site 1, Site 2, etc.)
- Um ícone verde (check) é exibido quando a conta foi encontrada, vermelho (error) quando não
- Os detalhes são exibidos abaixo do nome do site

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/PossiveisContas.ts`
- Mock: `apps/frontend/domain/mocks/possiveis_contas.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderPossiveisContas.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "data": {
        "cpf": {
            "PAIscpf":
            [
                {
                    "endpoint": "PAIscpf",
                    "PAIs": [
                        {
                            "alerta": "Não",
                            "aplicacao": "casasbahia",
                            "found": "Não",
                            "tipo_alerta": "Não se Aplica"
                        },
                        {
                            "alerta": "Não",
                            "aplicacao": "Estacionamento Digital",
                            "found": "Sim",
                            "tipo_alerta": "Não se Aplica"
                        }
                    ]
                }
            ],
            //outros resultados}
        }
    },
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Possíveis Contas em Sites",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["PAIscpf", "ProvedorDeAplicacacaoDaInternetcpf"],
        "data_count": 2,
        "data":  [
                    {
                        "site": {
                            "value": "casasbahia",
                            "label": "Site",
                            "source": ["PAIscpf"],
                            "is_deleted": false
                        },
                        "detalhes": {
                            "entrada_cpf": {
                                "value": "10748896732",
                                "label": "Entrada CPF",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "found": {
                                "value": "Não",
                                "label": "Conta encontrada?",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "alerta": {
                                "value": "Não",
                                "label": "Alvo alertado?",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "tipo_alerta": {
                                "value": "Não se Aplica",
                                "label": "Tipo de Alerta",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            }
                        }
                    },
                    {
                        "site": {
                            "value": "Estacionamento Digital",
                            "label": "Site",
                            "source": ["PAIscpf"],
                            "is_deleted": false
                        },
                        "detalhes": {
                            "entrada_cpf": {
                                "value": "10748896732",
                                "label": "Entrada CPF",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "found": {
                                "value": "Sim",
                                "label": "Conta encontrada?",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "alerta": {
                                "value": "Não",
                                "label": "Alvo alertado?",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            },
                            "tipo_alerta": {
                                "value": "Não se Aplica",
                                "label": "Tipo de Alerta",
                                "source": ["PAIscpf"],
                                "is_deleted": false
                            }
                        }
                    }
            ]
        
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Doações Enviadas Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEDoacoes"** -> lista **"pessoa"** -> lista **"candidato"** (TSEDoacoes[0].pessoa[0].candidato)

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Doações Enviadas Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Doações Enviadas Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEDoacoes"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de doação enviada contém:
  - `candidato`: Informação sobre o candidato (nome completo)
  - `detalhes`: Objeto com informações detalhadas do candidato (cpf, ano, cargo eleitoral, etc.)
  - `doacoes`: Array de objetos com informações sobre as doações realizadas

### Renderização:
- O nome do candidato é exibido em destaque
- Os detalhes do candidato são exibidos em uma grade de duas colunas
- As doações são renderizadas usando a função `renderValidArray` que exibe cada doação com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/DoacoesEnviadas.ts`
- Mock: `apps/frontend/domain/mocks/doacoes_enviadas.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderDoacoesEnviadas.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEDoacoes": [
       {
            "endpoint": "TSEDoacoes",
            "pessoa": [{
                "candidato": [/* lista de dados */]
            }]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Doações Enviadas Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEDoacoes"],
        "data_count": 2,
        "data": [
            {
                "candidato": {
                    "value": "João da Silva",
                    "label": "Candidato",
                    "source": ["TSEDoacoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "123.456.789-00",
                        "label": "CPF",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "ano": {
                        "value": "2022",
                        "label": "Ano",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "cargo eleitoral": {
                        "value": "Vereador",
                        "label": "Cargo Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "unidade eleitoral": {
                        "value": "São Paulo / SP",
                        "label": "Unidade Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "número do candidato": {
                        "value": "12345",
                        "label": "Número",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "partido eleitoral": {
                        "value": "Partido ABC",
                        "label": "Partido",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                },
                "doacoes": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 1.000,00",
                                "label": "Valor",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "10/05/2022",
                                "label": "Data",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "123456789",
                                "label": "Recibo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Doação para campanha eleitoral",
                                "label": "Descrição",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Física",
                                "label": "Origem",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Doação direta",
                                "label": "Fonte",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            }
                        },
                        "label": "Doação",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                ]
            },
            {
                "candidato": {
                    "value": "Maria Oliveira",
                    "label": "Candidato",
                    "source": ["TSEDoacoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "987.654.321-00",
                        "label": "CPF",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "ano": {
                        "value": "2022",
                        "label": "Ano",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "cargo eleitoral": {
                        "value": "Deputado Estadual",
                        "label": "Cargo Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "unidade eleitoral": {
                        "value": "Rio de Janeiro / RJ",
                        "label": "Unidade Eleitoral",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "número do candidato": {
                        "value": "54321",
                        "label": "Número",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "partido eleitoral": {
                        "value": "Partido XYZ",
                        "label": "Partido",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                },
                "doacoes": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 2.000,00",
                                "label": "Valor",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "20/07/2022",
                                "label": "Data",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "246813579",
                                "label": "Recibo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Doação para campanha eleitoral",
                                "label": "Descrição",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Física",
                                "label": "Origem",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Doação direta",
                                "label": "Fonte",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            }
                        },
                        "label": "Doação",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                ]
            }
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Doações Recebidas Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEDoacoes"** -> lista **"candidato"** -> lista **"empresa"** (TSEDoacoes[0].candidato[n].empresa)
  - **"TSEDoacoes"** -> lista **"candidato"** -> lista **"pessoa"** (TSEDoacoes[0].candidato[n].pessoa)

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Doações Recebidas Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Doações Recebidas Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEDoacoes"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Existem dois tipos de doações recebidas:
  1. **Doações de Empresas**:
     - `razao_social`: Informação sobre a empresa doadora
     - `detalhes`: Objeto com informações detalhadas da empresa (cnpj, cnae)
     - `doacoes`: Array de objetos com informações sobre as doações realizadas

  2. **Doações de Pessoas Físicas**:
     - `nome_completo`: Informação sobre a pessoa doadora
     - `detalhes`: Objeto com informações detalhadas da pessoa (cpf)
     - `doacoes`: Array de objetos com informações sobre as doações realizadas

### Renderização:
- O nome da empresa (razão social) ou da pessoa (nome completo) é exibido em destaque
- Os detalhes do doador são exibidos em uma grade de duas colunas
- As doações são renderizadas usando a função `renderValidArray` que exibe cada doação com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/DoacoesRecebidas.ts`
- Mock: `apps/frontend/domain/mocks/doacoes_recebidas.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderDoacoesRecebidas.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEDoacoes": [
       {
            "endpoint": "TSEDoacoes",
            "candidato": [
                {
                    "pessoa": [/* lista de dados */],
                    "empresa": [/* lista de dados */],
                },
                {
                    "pessoa": [/* lista de dados */],
                    "empresa": [/* lista de dados */],
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Doações Recebidas Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "data_count": 2,
        "source": ["TSEDoacoes"],
        "data": [
            {
                "razao_social": {
                    "value": "Empresa ABC Ltda",
                    "label": "Razão Social",
                    "source": ["TSEDoacoes"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "12.345.678/0001-90",
                        "label": "CNPJ",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    },
                    "cnae": {
                        "value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
                        "label": "CNAE",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                },
                "doacoes": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 5.000,00",
                                "label": "Valor",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "15/08/2022",
                                "label": "Data",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Doação para campanha eleitoral",
                                "label": "Descrição",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos de pessoa jurídica",
                                "label": "Natureza",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Doação direta",
                                "label": "Fonte",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEDoacoes"],
                                "is_deleted": false
                            }
                        },
                        "label": "Doação",
                        "source": ["TSEDoacoes"],
                        "is_deleted": false
                    }
                ]
            },
            // Outros doadores
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Fornecimentos Enviados Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEFornecimento"** -> lista **"candidado"** -> lista **"empresa"** (TSEFornecimento[0].candidado[n].empresa)

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Fornecimentos Enviados Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Fornecimentos Enviados Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEFornecimento"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de fornecimento enviado contém:
  - `razao_social`: Informação sobre a empresa (razão social)
  - `detalhes`: Objeto com informações detalhadas da empresa (cnpj, cnae)
  - `fornecimentos`: Array de objetos com informações sobre os fornecimentos realizados

### Renderização:
- A razão social da empresa é exibida em destaque
- Os detalhes da empresa são exibidos em uma grade de duas colunas
- Os fornecimentos são renderizados usando a função `renderValidArray` que exibe cada fornecimento com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/FornecimentosEnviados.ts`
- Mock: `apps/frontend/domain/mocks/fornecimentos_enviados.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderFornecimentosEnviados.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEFornecimento": [
       {
            "endpoint": "TSEFornecimento",
            "candidato": [
                {
                    "empresa": [/* lista de dados */]
                },
                {
                    "empresa": [/* lista de dados */]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Fornecimentos Enviados Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEFornecimento"],
        "data_count": 2,
        "data": [
            {
                "razao_social": {
                    "value": "Empresa XYZ Ltda",
                    "label": "Razão Social",
                    "source": ["TSEFornecimento"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cnpj": {
                        "value": "12.345.678/0001-90",
                        "label": "CNPJ",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "cnae": {
                        "value": "6201-5/01 - Desenvolvimento de programas de computador sob encomenda",
                        "label": "CNAE",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                },
                "fornecimentos": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 3.500,00",
                                "label": "Valor",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "10/05/2022",
                                "label": "Data",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Fornecimento de material gráfico para campanha",
                                "label": "Descrição",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Fornecimento direto",
                                "label": "Fonte",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            }
                        },
                        "label": "Fornecimento",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                ]
            },
           // Outros fornecedores
        ]
    }
     //outras seções
]
```
</div>
</div>

## SEÇÃO: "Fornecimentos Recebidos Campanha":

**Regras para processamento de dados:**

- Chaves para buscar:
  - **"TSEFornecimento"** -> lista **"pessoa"** -> lista **"candidato"** (TSEFornecimento[0].pessoa[0].candidato)

**Regras para a formatação da seção:**

- O objeto que constitui a seção **"Fornecimentos Recebidos Campanha"** e que será utilizado para a construção de forma dinâmica da sua interface, precisa conter as seguintes propriedades e valores:
  - **"title":** "Fornecimentos Recebidos Campanha"
  - **"subtitle":** "Dados consultados na API SNAP."
  - **"source"**: ["TSEFornecimento"] - uma lista com todos as chaves utilizadas para obter os dados desta seção.
  - **"data_count"**: **7** - quantidade de dados inseridos nesta seção - contagem de objetos dentro do **"data":**
  - **"data":** [...] Esta seria a lista com as propriedades adicionadas a seção.

### Estrutura do objeto:
- Cada objeto de fornecimento recebido contém:
  - `candidato`: Informação sobre o candidato (nome completo)
  - `detalhes`: Objeto com informações detalhadas do candidato (cpf, ano da eleição, cargo eleitoral, etc.)
  - `fornecimentos`: Array de objetos com informações sobre os fornecimentos recebidos

### Renderização:
- O nome do candidato é exibido em destaque
- Os detalhes do candidato são exibidos em uma grade de duas colunas
- Os fornecimentos são renderizados usando a função `renderValidArray` que exibe cada fornecimento com um número sequencial

**Arquivos relacionados:**
- Interface: `apps/frontend/domain/models/FornecimentosRecebidos.ts`
- Mock: `apps/frontend/domain/mocks/fornecimentos_recebidos.mock.ts`
- Estratégia de renderização: `apps/frontend/domain/models/strategy/renderFornecimentosRecebidos.strategy.tsx`

### **Exemplo de dados antes e depois do processamento:**

<div style="display: flex; gap: 1em;">
  <div style="flex: 1;">

- Retorno antes do processamento:

```json
{
    "TSEFornecimento": [
       {
            "endpoint": "TSEFornecimento",
            "pessoa": [
                {
                    "candidato": [/* lista de dados */]
                }
            ]
       }
    ],
    //outros resultados
}
```
</div>
<div style="flex: 1;">

- Retorno após o processamento:

```json
[
    {
        "title": "Fornecimentos Recebidos Campanha",
        "subtitle": "Dados consultados na API SNAP.",
        "source": ["TSEFornecimento"],
        "data_count": 2,
        "data": [
            {
                "candidato": {
                    "value": "João da Silva",
                    "label": "Candidato",
                    "source": ["TSEFornecimento"],
                    "is_deleted": false
                },
                "detalhes": {
                    "cpf": {
                        "value": "123.456.789-00",
                        "label": "CPF",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "ano da eleição": {
                        "value": "2022",
                        "label": "Ano Da Eleição",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "cargo eleitoral": {
                        "value": "Vereador",
                        "label": "Cargo Eleitoral",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "unidade eleitoral": {
                        "value": "São Paulo / SP",
                        "label": "Unidade Eleitoral",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "número do candidato": {
                        "value": "12345",
                        "label": "Número",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    },
                    "partido eleitoral": {
                        "value": "Partido ABC",
                        "label": "Partido",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                },
                "fornecimentos": [
                    {
                        "value": {
                            "valor": {
                                "value": "R$ 3.000,00",
                                "label": "Valor",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "data": {
                                "value": "10/05/2022",
                                "label": "Data",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "documento": {
                                "value": "DOC123456",
                                "label": "Documento",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "recibo": {
                                "value": "REC987654",
                                "label": "Recibo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "descricao": {
                                "value": "Fornecimento de material gráfico para campanha",
                                "label": "Descrição",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "especie": {
                                "value": "Transferência eletrônica",
                                "label": "Espécie",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza": {
                                "value": "Recursos próprios",
                                "label": "Natureza",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "natureza estimavel": {
                                "value": "Não se aplica",
                                "label": "Natureza Estimável",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "origem": {
                                "value": "Pessoa Jurídica",
                                "label": "Origem",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "fonte": {
                                "value": "Fornecimento direto",
                                "label": "Fonte",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            },
                            "Tipo": {
                                "value": "Financeira",
                                "label": "Tipo",
                                "source": ["TSEFornecimento"],
                                "is_deleted": false
                            }
                        },
                        "label": "Fornecimento",
                        "source": ["TSEFornecimento"],
                        "is_deleted": false
                    }
                ]
            },
            // Outros fornecimentos recebidos
        ]
    }
     //outras seções
]
```
</div>
</div>