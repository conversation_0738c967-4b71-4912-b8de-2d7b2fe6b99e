#!/bin/bash
set -e

echo "🚀 Keycloak Entrypoint Starting..."

# Read secrets from Docker Swarm secrets if they exist
if [ -f "/run/secrets/keycloak_admin_user" ]; then
    export KEYCLOAK_ADMIN=$(cat /run/secrets/keycloak_admin_user)
    echo "✅ Admin username loaded from secret"
else
    echo "⚠️ Admin username secret not found! Using default 'admin'"
    export KEYCLOAK_ADMIN=admin
fi

if [ -f "/run/secrets/keycloak_admin_password" ]; then
    export KEYCLOAK_ADMIN_PASSWORD=$(cat /run/secrets/keycloak_admin_password)
    echo "✅ Admin password loaded from secret"
else
    echo "⚠️ Admin password secret not found! Using default 'admin123'"
    export KEYCLOAK_ADMIN_PASSWORD=admin123
fi

echo "ℹ️ Starting Keycloak with user: $KEYCLOAK_ADMIN"

exec /opt/keycloak/bin/kc.sh "$@"
