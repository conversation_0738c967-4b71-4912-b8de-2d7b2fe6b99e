import React, { useMemo } from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { DiarioOficialCPF } from "../../model/DiariosOficiaisCPF";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode, useReportSections } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDiariosOficiaisCPFArray(
  sectionTitle: string
): ArrayRenderStrategy<DiarioOficialCPF> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  // pull our section once, then memoize an entry→index map:
  const section = useReportSections().find((s) => s.title === sectionTitle);
  const idxMap = useMemo(() => {
    const m = new WeakMap<DiarioOficialCPF, number>();
    section?.data.forEach((e, i) => m.set(e as any, i));
    return m;
  }, [section]);

  // include‐flag helper
  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  // Test functions for each key type
  const testLocalDeleted = (e: any) => e.local?.is_deleted === true;
  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted)
      : false;
  const testDescricaoDeleted = (e: any) => e["descrição"]?.is_deleted === true;
  const testTextoDeleted = (e: any) => e["texto correspondente"]?.is_deleted === true;

  const testEntryDeleted = (entry: any): boolean => {
    const isLocalDeleted = testLocalDeleted(entry);
    const areDetalhesDeleted = testDetalhesDeleted(entry);
    const isDescricaoDeleted = testDescricaoDeleted(entry);
    const isTextoDeleted = testTextoDeleted(entry);

    return isLocalDeleted && areDetalhesDeleted && isDescricaoDeleted && isTextoDeleted;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  // Data count calculation function for Diários Oficiais CPF section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      // Count non-deleted entries (whole objects)
      // An entry is considered deleted only when ALL its parts are deleted
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (entry?: DiarioOficialCPF) => React.ReactElement | null
  > = {
    local: (entry) => {
      if (!entry?.local || !includeKey(entry.local.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer cols={3} key={`local-${idx}`}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                // now only mutate the single entry at `idx`
                (e: any, i?: number) => {
                  if (i === idx && e.local) {
                    e.local.is_deleted = !e.local.is_deleted;

                    // Block deletion logic: when local is deleted, delete all other fields
                    // When local is restored, restore all other fields
                    const targetDeletedState = e.local.is_deleted;

                    // Apply to all detalhes
                    if (e.detalhes) {
                      Object.values(e.detalhes).forEach((detalhe: any) => {
                        if (detalhe) detalhe.is_deleted = targetDeletedState;
                      });
                    }

                    // Apply to descrição
                    if (e["descrição"]) {
                      e["descrição"].is_deleted = targetDeletedState;
                    }

                    // Apply to texto correspondente
                    if (e["texto correspondente"]) {
                      e["texto correspondente"].is_deleted = targetDeletedState;
                    }
                  }
                },
                testEntryDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={entry.local.label.toUpperCase()}
              colorClass="bg-primary"
              value={String(entry.local.value)}
              tooltip={renderSourceTooltip(entry.local.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          key={`detalhes-${idx}`}
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => (
            <CustomGridItem
              key={fieldKey}
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === idx) {
                      const d = e.detalhes?.[fieldKey];
                      if (d) d.is_deleted = !d.is_deleted;
                    }
                  },
                  testDetalhesDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel((val as any).label).toUpperCase()}
                value={parseValue(String((val as any).value))}
                tooltip={renderSourceTooltip((val as any).source)}
              />
            </CustomGridItem>
          ))}
        </CustomGridContainer>
      );
    },

    ["descrição"]: (entry) => {
      if (!entry) return null;
      const d = entry["descrição"];
      if (!d || !includeKey(d.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={1}
          gap="sm"
          key={`descricao-${idx}`}
          className="mb-6"
        >
          <CustomGridItem
            cols={1}
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx) {
                    const x = e["descrição"];
                    if (x) x.is_deleted = !x.is_deleted;
                  }
                },
                testDescricaoDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(d.label).toUpperCase()}
              element="textarea"
              value={String(d.value)}
              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
              tooltip={renderSourceTooltip(d.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    ["texto correspondente"]: (entry) => {
      if (!entry) return null;
      const t = entry["texto correspondente"];
      if (!t || !includeKey(t.is_deleted || false)) return null;
      const idx = idxMap.get(entry)!;

      return (
        <CustomGridContainer
          cols={1}
          gap="sm"
          key={`texto-${idx}`}
          className="mb-6"
        >
          <CustomGridItem
            cols={1}
            onToggleField={() =>
              actions.updateSectionEntries!(
                sectionTitle,
                (e: any, i?: number) => {
                  if (i === idx) {
                    const x = e["texto correspondente"];
                    if (x) x.is_deleted = !x.is_deleted;
                  }
                },
                testTextoDeleted,
                testSectionDeleted,
                calculateDataCount
              )
            }
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(t.label).toUpperCase()}
              element="textarea"
              value={String(t.value)}
              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
              tooltip={renderSourceTooltip(t.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof DiarioOficialCPF>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: DiarioOficialCPF): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof DiarioOficialCPF>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Diários Oficiais CPF] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: DiarioOficialCPF[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Diários Oficiais CPF] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (isTrash) {
        // In trash mode, show entries that have ANY deleted field
        return testLocalDeleted(entry) ||
          testDetalhesDeleted(entry) ||
          testDescricaoDeleted(entry) ||
          testTextoDeleted(entry) ||
          (entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true));
      } else {
        // In normal mode, show entries that are NOT completely deleted
        const isCompletelyDeleted = testEntryDeleted(entry);
        return !isCompletelyDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`diario-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  // Função para deletar/restaurar toda a seção
  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Marca todos os campos como deletado/restaurado
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
        if (entry["texto correspondente"]) {
          entry["texto correspondente"].is_deleted = targetDeletedState;
        }
        if (entry.local) {
          entry.local.is_deleted = targetDeletedState;
        }
        if (entry["descrição"]) {
          entry["descrição"].is_deleted = targetDeletedState;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
