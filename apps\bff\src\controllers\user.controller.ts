import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { UserSaltService } from "../services/user.service";
import { AuthenticationError, ValidationError } from "../@types/errors";

const userService = new UserSaltService();

export const addUserSalt = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sub } = req.params;
    const { salt } = req.body;
    const token = req.cookies.token;

    if (!sub || !salt) {
      throw new ValidationError("Sub and salt are required");
    }

    if (!token) {
      throw new AuthenticationError("No authentication token provided");
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!);
      // [TODO] - adicionar validações do token
    } catch (jwtError) {
      throw new AuthenticationError("Invalid or expired token");
    }

    await userService.saveUserSalt(sub, salt);

    res.status(200).json({
      success: true,
      message: "Salt saved successfully",
    });
  } catch (error) {
    next(error);
  }
};

export const getUserSalt = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sub } = req.params;
    const token = req.cookies.token;

    if (!sub) {
      throw new ValidationError("Sub parameter is required");
    }

    if (!token) {
      throw new AuthenticationError("No authentication token provided");
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!);
      // [TODO] - adicionar validações do token
    } catch (jwtError) {
      throw new AuthenticationError("Invalid or expired token");
    }

    const salt = await userService.getUserSalt(sub);

    if (!salt) {
      res.status(404).json({
        success: false,
        message: "Salt not found for this user",
      });
      return;
    }

    res.status(200).json({
      success: true,
      salt,
    });
  } catch (error) {
    next(error);
  }
};

export const getUserVerifier = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sub } = req.params;
    const token = req.cookies.token;

    if (!sub) {
      throw new ValidationError("Sub parameter is required");
    }

    if (!token) {
      throw new AuthenticationError("No authentication token provided");
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!);
      // [TODO] - adicionar validações do token
    } catch (jwtError) {
      throw new AuthenticationError("Invalid or expired token");
    }

    const salt = await userService.getUserVerifier(sub);

    if (!salt) {
      res.status(404).json({
        success: false,
        message: "Verifier not found for this user",
      });
      return;
    }

    res.status(200).json({
      success: true,
      salt,
    });
  } catch (error) {
    next(error);
  }
};

export const initializeUserSalt = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sub } = req.params;
    const token = req.cookies.token;

    if (!sub) {
      throw new ValidationError("Sub parameter is required");
    }

    if (!token) {
      throw new AuthenticationError("No authentication token provided");
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!);
      // [TODO] - adicionar validações do token
    } catch (jwtError) {
      throw new AuthenticationError("Invalid or expired token");
    }

    const newSalt = await userService.initializeUserSalt(sub);
    res.status(201).json({
      success: true,
      salt: newSalt,
      message: "Salt initialized successfully",
    });
  } catch (error) {
    next(error);
  }
};
