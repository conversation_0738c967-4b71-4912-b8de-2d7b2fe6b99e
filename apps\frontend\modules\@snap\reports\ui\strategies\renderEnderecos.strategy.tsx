import React from "react";
import { Endereco } from "../../model/Enderecos";
import { RenderStrategy } from "./RenderStrategy";
import { renderValidArray } from "./helpers.strategy";

export class RenderEnderecos implements RenderStrategy<Endereco> {
  validateKeys = (keys: Array<keyof Endereco>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (enderecos?: Endereco) => React.ReactElement | null
  > = {
      endereco: (enderecos?: Endereco) => {
        if (!enderecos?.detalhes?.length) return null;

        return renderValidArray('detalhes', enderecos || {}, [], true /* hasLabel */, false /* hasTitle */);
      },
    };

  render = (data: Endereco): React.ReactElement[] => {
    const elementos: React.ReactElement[] = [];

    // Iterate through all keys in the dictionary
    Object.keys(this.formatByKey).forEach((chave) => {
      const elemento = this.formatByKey[chave](data);
      if (elemento) {
        elementos.push(
          <React.Fragment key={`fragment-${chave}`}>{elemento}</React.Fragment>
        );
      }
    });

    return elementos;
  };
}
