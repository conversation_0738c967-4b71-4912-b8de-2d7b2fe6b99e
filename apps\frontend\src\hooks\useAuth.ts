import { useMutation } from "@tanstack/react-query";
import { postLogout, LogoutResponse } from "~/services/gateways/auth.gateway";
import { useNavigate } from "react-router";


export const useAuth = () => {
  const navigate = useNavigate();

  const logoutMutation = useMutation<LogoutResponse, Error>({
    mutationFn: postLogout,
    onSuccess: () => {
      navigate("/login", { replace: true, state: { fromLogout: true } });
    },
    onError: (error) => {
      console.error("Logout failed:", error);
      navigate("/login", { replace: true, state: { fromLogout: true } });
    },
  });

  return {
    logoutMutation,
  };
};
