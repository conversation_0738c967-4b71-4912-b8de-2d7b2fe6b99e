import { ServicoPublico } from "../model/ServicoPublico";

// Mock for Transparência PR
const servicoPublicoPR1: ServicoPublico = {
    servidor: {
        value: "<PERSON>",
        label: "Nome do Servidor",
        source: ["Transparência PR"],
        is_deleted: false
    },
    detalhes: {
        carreira: {
            value: "Analista de Sistemas",
            label: "Função",
            source: ["Transparência PR"],
            is_deleted: false
        },
        instituicao: {
            value: "Secretaria de Estado da Administração e da Previdência",
            label: "Instituição",
            source: ["Transparência PR"],
            is_deleted: false
        },
        "quadro funcional": {
            value: "Quadro Próprio do Poder Executivo - QPPE",
            label: "Quadro Funcional",
            source: ["Transparência PR"],
            is_deleted: false
        },
        municipio: {
            value: "Curitiba",
            label: "Cidade / PR",
            source: ["Transparência PR"],
            is_deleted: false
        }
    }
};

// Mock for Transparência PR - Another example
const servicoPublicoPR2: ServicoPublico = {
    servidor: {
        value: "Maria Oliveira Lima",
        label: "Nome do Servidor",
        source: ["Transparência PR"],
        is_deleted: false
    },
    detalhes: {
        carreira: {
            value: "Professor",
            label: "Função",
            source: ["Transparência PR"],
            is_deleted: false
        },
        instituicao: {
            value: "Secretaria de Estado da Educação",
            label: "Instituição",
            source: ["Transparência PR"],
            is_deleted: false
        },
        "quadro funcional": {
            value: "Quadro Próprio do Magistério - QPM",
            label: "Quadro Funcional",
            source: ["Transparência PR"],
            is_deleted: false
        },
        municipio: {
            value: "Londrina",
            label: "Cidade / PR",
            source: ["Transparência PR"],
            is_deleted: false
        }
    }
};

// Export the complete mock
export const servicosPublicosMock: ServicoPublico[] = [
    servicoPublicoPR1,
    servicoPublicoPR2
];
