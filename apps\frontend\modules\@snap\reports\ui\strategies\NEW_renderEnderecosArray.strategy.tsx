import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { Endereco } from "../../model/Enderecos";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { GridItem, CustomLabel } from "@snap/design-system";
import { translatePropToLabel } from "../../helpers";
import { getFieldLabel, getFieldValue, renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export function useRenderEnderecosArray(sectionTitle: string): ArrayRenderStrategy<Endereco> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const shouldIncludeBlock = (detalhe: any) => {
    const vals = Object.values(detalhe.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const onToggleField = (blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value?.[fieldKey]) {
          detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;
        }
      },
      (entry) =>
        entry.detalhes?.every((d: any) =>
          Object.values(d.value).every((v: any) => v.is_deleted === true)
        ) ?? false,
      (section) =>
        Array.isArray(section.data) &&
        section.data.every((e) =>
          e.detalhes?.every((d: any) =>
            Object.values(d.value).every((v: any) => v.is_deleted === true)
          )
        ),
      calculateDataCount
    );
  };

  const onToggleBlock = (blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value) {
          // Determina o novo estado baseado no modo atual
          const targetDeletedState = isTrash ? false : true;

          // Define o is_deleted do bloco principal
          detalhe.is_deleted = targetDeletedState;

          // Define o is_deleted de todos os campos dentro do bloco
          Object.values(detalhe.value).forEach((campo: any) => {
            if (campo) {
              campo.is_deleted = targetDeletedState;
            }
          });
        }
      },
      (entry) =>
        entry.detalhes?.every((d: any) =>
          Object.values(d.value).every((v: any) => v.is_deleted === true)
        ) ?? false,
      (section) =>
        Array.isArray(section.data) &&
        section.data.every((e) =>
          e.detalhes?.every((d: any) =>
            Object.values(d.value).every((v: any) => v.is_deleted === true)
          )
        ),
      calculateDataCount
    );
  };

  const formatByKey: Record<
    string,
    (endereco?: Endereco) => React.ReactElement | null
  > = {
    detalhes: (endereco) => {
      if (!endereco?.detalhes?.length) return null;

      const blocks = endereco.detalhes
        .map((d, idx) => ({ bloco: d, idx }))
        .filter(({ bloco }) => shouldIncludeBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={1} className="mb-5">
          <GridItem fullWidth>
            <CustomGridContainer cols={2}>
              {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
                <GridItem key={`end-${origIdx}`} cols={1}>
                  <CustomGridItem
                    cols={1}
                    className="py-2"
                    onToggleField={() => onToggleBlock(origIdx)}
                  >
                    <CustomLabel
                      label={`ENDEREÇO ${!isTrash ? blockRenderIdx + 1 : ""}`}
                      colorClass="bg-primary"
                      icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                    />
                  </CustomGridItem>
                  <div className="pl-5">
                    {Object.entries(bloco.value)
                      .filter(([_, v]: any) =>
                        isTrash ? v.is_deleted : !v.is_deleted
                      )
                      .map(([fieldKey, fieldValue]: any) => (
                        <CustomGridItem
                          key={`end-${origIdx}-${fieldKey}`}
                          cols={1}
                          className="py-2"
                          onToggleField={() => onToggleField(origIdx, fieldKey)}
                        >
                          <CustomReadOnlyInputField
                            label={translatePropToLabel(
                              getFieldLabel(fieldKey, fieldValue)
                            ).toUpperCase()}
                            colorClass="bg-border"
                            icon={
                              <MdOutlineSubdirectoryArrowRight size={16} />
                            }
                            value={String(getFieldValue(fieldValue) || "")}
                            tooltip={renderSourceTooltip(fieldValue.source)}
                          />
                        </CustomGridItem>
                      ))}
                  </div>
                </GridItem>
              ))}
            </CustomGridContainer>
          </GridItem>
        </CustomGridContainer>
      );
    },
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) =>
      Object.values(d.value).every((v: any) => v.is_deleted === true)
    ) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  // Data count calculation function for Endereços section
  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted blocks in detalhes array
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  };

  const validateKeys = (keys: Array<keyof Endereco>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (endereco: Endereco): React.ReactElement[] => {
    const keys = Object.keys(endereco) as Array<keyof Endereco>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Endereços] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(endereco))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Endereco[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Endereços] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((d: any) =>
          Object.values(d.value).some((v: any) => v.is_deleted === true)
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((endereco, index) => {
      const elements = renderSingleItem(endereco);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`endereco-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  // Função para deletar/restaurar toda a seção
  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca como deletado/restaurado
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            // Define o is_deleted do bloco principal
            detalhe.is_deleted = targetDeletedState;

            if (detalhe.value) {
              Object.values(detalhe.value).forEach((campo: any) => {
                if (campo) campo.is_deleted = targetDeletedState;
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
