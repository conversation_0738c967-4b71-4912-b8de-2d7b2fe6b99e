import { DadosEmpresa } from "../model/DadosEmpresa";

export const dadosEmpresaMock: DadosEmpresa = {
    detalhes: {
        "razao social": {
            value: "TECHBIZ FORENSE DIGITAL LTDA",
            label: "Razão Social",
            source: ["SNAP"],
            is_deleted: false
        },
        cnpj: {
            value: "05.757.597/0002-18",
            label: "CNPJ",
            source: ["SNAP"],
            is_deleted: false
        },
        procon: {
            value: "(NAO TEM)",
            label: "PROCON",
            source: ["SNAP"],
            is_deleted: false
        },
        "info restricao": {
            value: "* NADA CONSTA *",
            label: "Info Restrição",
            source: ["SNAP"],
            is_deleted: false
        },
        "status receita": {
            value: "ATIVA",
            label: "Status na Receita",
            source: ["SNAP"],
            is_deleted: false
        },
        "data de fundacao": {
            value: "24/06/2009",
            label: "Data de Fundação",
            source: ["SNAP"],
            is_deleted: false
        },
        data_abertura: {
            value: "24/06/2009",
            label: "Data de Abertura",
            source: ["SNAP"],
            is_deleted: false
        },
        sequencial: {
            value: "1",
            label: "Sequencial",
            source: ["SNAP"],
            is_deleted: false
        },
        porte: {
            value: "DEMAIS EMPRESA",
            label: "Porte",
            source: ["SNAP"],
            is_deleted: false
        },
        "tipo de imposto": {
            value: "LUCRO REAL",
            label: "Tipo de Imposto",
            source: ["SNAP"],
            is_deleted: false
        },
        "total de funcionarios": {
            value: "35",
            label: "Total de Funcionários",
            source: ["SNAP"],
            is_deleted: false
        },
        "quantidade de funcionarios acima de 5 salarios": {
            value: "10",
            label: "Funcionários acima de 5 salários",
            source: ["SNAP"],
            is_deleted: false
        },
        "quantidade de funcionarios abaixo de 5 salarios": {
            value: "25",
            label: "Funcionários abaixo de 5 salários",
            source: ["SNAP"],
            is_deleted: false
        }
    },
};
