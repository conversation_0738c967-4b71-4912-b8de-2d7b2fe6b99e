import React from "react";
import { Email } from "../../model/Emails";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { ReadOnlyInputField, Text } from "@snap/design-system";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export class RenderEmails implements RenderStrategy<Email> {
  validateKeys = (keys: Array<keyof Email>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (emails?: Email) => React.ReactElement | null
  > = {
      detalhes: (emails?: Email) => {
        if (!emails?.detalhes?.length) return null;

        return (
          <CustomGridContainer cols={1} className="">
            <GridItem fullWidth>
              <CustomGridContainer cols={2}>
                {emails.detalhes.map((detalhe, index) => (
                  <GridItem key={`email-detalhe-column-${index}`} cols={1}>

                    <div className="">
                      {Object.entries(detalhe.value).map(
                        ([key, valueObj]) =>
                          key !== "label default key" && (
                            <GridItem
                              key={`detalhe-${index}-${key}`}
                              cols={1}
                              className="py-2 group"
                            >
                              <CustomReadOnlyInputField
                                label={`${(detalhe.label || key).toUpperCase()} ${index + 1}`}
                                value={String(
                                  typeof valueObj === 'object' && valueObj?.value
                                    ? valueObj.value
                                    : valueObj
                                )}
                                tooltip={renderSourceTooltip(valueObj.source)}
                              />
                            </GridItem>
                          )
                      )}
                    </div>

                  </GridItem>
                ))}
              </CustomGridContainer>
            </GridItem>
          </CustomGridContainer>
        );
      },
    };

  render = (data: Email): React.ReactElement[] => {
    const elements: React.ReactElement[] = [];

    Object.keys(this.formatByKey).forEach((key) => {
      const element = this.formatByKey[key](data);
      if (element) {
        elements.push(
          <React.Fragment key={`fragment-${key}`}>{element}</React.Fragment>
        );
      }
    });

    return elements;
  };
}
