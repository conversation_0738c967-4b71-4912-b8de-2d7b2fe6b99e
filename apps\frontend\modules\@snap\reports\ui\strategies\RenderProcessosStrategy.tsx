import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    Accordion,
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from '../../ui/components/accordion';
import { Badge } from '../../ui/components/badge';
import { Processo } from '../../model/Processo';
import { RenderProcessos } from './renderProcessos.strategy';
import {
    useModalControl,
    List,
    ListItem,
    Separator,
    Text,
    CustomLabel,
    StandardList,
    Button,
    ModalClose
} from "@snap/design-system";
import { X } from 'lucide-react';
import { translatePropToLabel } from '../../helpers';
import { useReportMode } from '../../context/ReportContext';

interface ProcessosSectionProps {
    rootSection: {
        data: Record<string, any>[];
    };
    subSections: {
        subsection: string;
        data: Record<string, any>[];
    }[];
}

const ProcessosSection: React.FC<ProcessosSectionProps> = ({ rootSection, subSections }) => {
    const mode = useReportMode();
    const isTrashMode = mode === 'trash';
    const { open } = useModalControl();
    const renderer = useMemo(() => new RenderProcessos(), []);

    const openDetails = useCallback((proc: Record<string, any>) => {
        const fullProcessoData = rootSection.data.find(r => r.numero.value === proc.numero.value);
        if (!fullProcessoData) return;

        open({
            modal: () => ({
                title: `Processo`,
                icon: (
                    <ModalClose>
                        <X className="cursor-pointer" />
                    </ModalClose>
                ),
                content: renderer.render(fullProcessoData as Processo).map((el, idx) => (
                    <div key={idx} className="mb-4">
                        {el}
                    </div>
                )),
                footer: null,
            }),
            config: {
                content: { className: "max-w-5xl" },
                contentWrapper: {
                    className: "max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable]"
                }
            },
        });
    }, [open, renderer, rootSection.data]);

    return (
        <>
            <Accordion type="multiple">
                {subSections.map((sec, i) => (
                    <AccordionItem key={`${sec.subsection}-${i}`} value={sec.subsection} className='border-b-neutral-100'>
                        <AccordionTrigger className="px-0 py-0 mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer">
                            <div className="flex items-center gap-4">
                                <h3 className="font-mono text-lg uppercase">{translatePropToLabel(sec.subsection)}</h3>
                                {
                                    !isTrashMode && (
                                        <Badge variant="secondary" className="rounded-2xl px-4 py-0.5 bg-gray-400">
                                            {sec.data.length}
                                        </Badge>
                                    )
                                }
                            </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-8 py-4 border-t">
                            <StandardList withSeparator>
                                {sec.data.map(proc => (
                                    <ListItem key={proc.numero.value} className={`w-full flex justify-between gap-2 ${isTrashMode ? 'flex-col items-start' : ''}`}>
                                        <CustomLabel
                                            label={`Número: ${proc.numero.value}`}
                                            colorClass="bg-primary"
                                        />
                                        <Button onClick={() => openDetails(proc)}>
                                            Ver detalhes
                                        </Button>
                                    </ListItem>
                                ))}
                            </StandardList>
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        </>
    );
};

export default ProcessosSection;