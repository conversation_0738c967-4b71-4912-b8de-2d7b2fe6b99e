export const getInitials = (name: string | undefined): string => {
  if (!name) return "N/A";

  const nameParts = name.trim().split(" ").filter(Boolean);

  if (nameParts.length === 1) {
    return nameParts[0][0].toUpperCase();
  } else if (nameParts.length > 1) {
    const firstInitial = nameParts[0][0];
    const lastInitial = nameParts[nameParts.length - 1][0];
    return `${firstInitial}${lastInitial}`.toUpperCase();
  }

  return "N/A";
};
