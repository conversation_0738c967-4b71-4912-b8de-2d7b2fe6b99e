import { ValueWithSource } from "./ValueWithSource";

export interface _Fornecimento {
    valor: ValueWithSource;
    data: ValueWithSource;
    documento: ValueWithSource;
    recibo: ValueWithSource;
    descricao: ValueWithSource;
    especie: ValueWithSource;
    natureza: ValueWithSource;
    "natureza estimavel": ValueWithSource;
    origem: ValueWithSource;
    fonte: ValueWithSource;
    Tipo: ValueWithSource;
}

export interface _Candidato {
    "full name": string;
    cpf: string;
    "ano da eleição": string;
    "cargo eleitoral": string;
    "unidade eleitoral": string;
    "número do candidato": string;
    "partido eleitoral": string;
}

export interface FornecimentoRecebido {
    candidato: ValueWithSource<_Candidato["full name"]>;
    detalhes: {
        cpf: ValueWithSource<string>;
        "ano da eleição": ValueWithSource<string>;
        "cargo eleitoral": ValueWithSource<string>;
        "unidade eleitoral": ValueWithSource<string>;
        "número do candidato": ValueWithSource<string>;
        "partido eleitoral": ValueWithSource<string>;
    };
    fornecimentos: Array<ValueWithSource<_Fornecimento>>;
}
