import React from "react";
import { Processo } from "../../model/Processo";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel, GridContainer } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { renderSourceTooltip, renderValidArray } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderProcessos implements RenderStrategy<Processo> {

  /**
   * Checks if a value is an array of objects with the expected participant format
   * @param value The value to check
   * @returns True if the value is an array of participant objects
   */
  isParticipantArray = (value: any): boolean => {
    if (!Array.isArray(value) || value.length === 0) {
      return false;
    }

    // Check if the first item has the expected structure
    const firstItem = value[0];
    return (
      typeof firstItem === 'object' &&
      firstItem !== null &&
      'value' in firstItem &&
      'source' in firstItem &&
      typeof firstItem.value === 'object'
    );
  };

  validateKeys = (keys: Array<keyof Processo>): boolean => {
    return keys.some((campo) => {
      // Check if the key is in formatByKey
      return campo in this.formatByKey;
    });
  };

  formatByKey: Record<
    string,
    (processo?: Processo) => React.ReactElement | null
  > = {
      numero: (processo?: Processo) => {
        if (!processo?.numero) return null;

        return (
          <GridContainer cols={3}>
            <GridItem cols={1} className="mb-6 group">
              <CustomReadOnlyInputField
                label={(processo.numero.label || "Número do Processo").toUpperCase()}
                colorClass="bg-primary"
                labelTextClass="text-accent"
                value={String(processo.numero.value || "")}
                tooltip={renderSourceTooltip(processo.numero.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },

      detalhes: (processo?: Processo) => {
        if (!processo?.detalhes) return null;

        return (
          <GridContainer cols={2} className="">
            {Object.entries(processo.detalhes).map(([key, value]) => (
              <GridItem key={key} cols={1} className="group">
                <CustomReadOnlyInputField
                  label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                  value={String(value.value)}
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  tooltip={renderSourceTooltip(value.source)}
                />
              </GridItem>
            ))}
          </GridContainer>
        );
      },

      movimentações: (processo?: Processo) => {
        if (!processo?.movimentações) return null;

        return (
          <GridContainer cols={1} gap="sm" className="mb-6">
            <GridItem fullWidth className="group">
              <CustomReadOnlyInputField
                label={`${(translatePropToLabel(processo.movimentações.label || "Movimentações")).toUpperCase()}`}
                value={String(processo.movimentações.value)}
                element="textarea"
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip(processo.movimentações?.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },

    }

  render = (processo: Processo): React.ReactElement[] => {
    const keys = Object.keys(processo) as Array<keyof Processo>;

    if (!this.validateKeys(keys)) {
      console.log(keys);
      //throw new Error("Chaves inválidas");
    }

    const orderedKeys: Array<keyof Processo> = [
      'numero',
      'detalhes',
      'movimentações',
    ];

    // First, process the ordered keys that exist in the processo object
    const orderedElements = orderedKeys
      .filter(key => keys.includes(key))
      .map((chave) => {
        const validKey = chave in this.formatByKey;
        if (validKey) {
          return this.formatByKey[chave]?.(processo);
        }

        return null;
      });

    // Then, process any remaining keys that weren't in the orderedKeys list
    const remainingElements = keys
      .filter(key => !orderedKeys.includes(key))
      .map(chave => renderValidArray(chave as string, processo));

    // Combine both arrays and filter out null elements
    return [...orderedElements, ...remainingElements]
      .filter((el): el is React.ReactElement => el !== null);
  };
}
