# Keycloak Configuration

This document provides an overview of the Keycloak configuration used in this project. Keycloak is an open-source Identity and Access Management solution for modern applications and services.

## Table of Contents

- [Overview](#overview)
- [Directory Structure](#directory-structure)
- [Keycloak Setup](#keycloak-setup)
- [Realm Configuration](#realm-configuration)
- [Docker Setup](#docker-setup)
- [Entrypoint Script](#entrypoint-script)
- [Running Keycloak](#running-keycloak)

## Overview

The Keycloak configuration in this project is used to manage authentication and authorization. It provides features such as single sign-on, identity brokering, and user federation.

## Directory Structure

- `realm.json.template`: A template for the Keycloak realm configuration.
- `realm.json`: The actual realm configuration file.
- `Dockerfile`: Dockerfile for building the Keycloak image.
- `entrypoint.sh`: Entrypoint script for initializing Keycloak.

## Keycloak Setup

To set up Keycloak, you need to configure a realm, clients, and users. The `realm.json.template` file provides a template for these configurations.

## Realm Configuration

The `realm.json` file contains the configuration for the Keycloak realm. It includes settings for clients, roles, and users. You can customize this file to suit your application's needs.

## Docker Setup

The `Dockerfile` is used to build a Docker image for Keycloak. It includes the necessary configurations and scripts to run Keycloak in a containerized environment.

## Entrypoint Script

The `entrypoint.sh` script is used to initialize Keycloak with the specified realm configuration. It is executed when the Docker container starts.

## Running Keycloak

To run Keycloak, you can use Docker to build and start the container. Ensure that you have Docker installed and configured on your system.

```bash
docker build -t keycloak .
docker run -p 8080:8080 keycloak
```

This will start Keycloak on port 8080. You can access the Keycloak admin console at `http://localhost:8080/auth`.
The keycloak on vm *************** is running on the address https://sturgeon-big-tapir.ngrok-free.app/ (was necessary to use ngrok because google/microfost auth do not work on ip's, was necessary an url. To run locally is possible to not use ngrok, but need to be setup)

---
