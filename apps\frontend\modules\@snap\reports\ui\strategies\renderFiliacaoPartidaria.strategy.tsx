import React from "react";
import { FiliacaoPartidaria, Vinculo } from "../../model/FiliacaoPartidaria";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { renderSourceTooltip, renderValidArray } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderFiliacaoPartidaria implements RenderStrategy<FiliacaoPartidaria> {

    validateKeys = (keys: Array<keyof FiliacaoPartidaria>): boolean => {
        return keys.some((campo) => {
            return campo in this.formatByKey;
        });
    };

    renderVinculos = (filiacaoPartidaria?: FiliacaoPartidaria): React.ReactElement | null => {
        return renderValidArray('vinculos', filiacaoPartidaria || {});
    };

    formatByKey: Record<
        string,
        (filiacaoPartidaria?: FiliacaoPartidaria) => React.ReactElement | null
    > = {
            sigla: (filiacaoPartidaria?: FiliacaoPartidaria) => {
                if (!filiacaoPartidaria?.sigla) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(filiacaoPartidaria.sigla.label || "Sigla").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={String(filiacaoPartidaria.sigla.value || "")}
                                tooltip={renderSourceTooltip(filiacaoPartidaria.sigla.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (filiacaoPartidaria?: FiliacaoPartidaria) => {
                if (!filiacaoPartidaria?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(filiacaoPartidaria.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={String(value.value)}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            vinculos: (filiacaoPartidaria?: FiliacaoPartidaria) => {
                return this.renderVinculos(filiacaoPartidaria);
            }
        }

    render = (filiacaoPartidaria?: FiliacaoPartidaria): React.ReactElement[] => {
        if (!filiacaoPartidaria) return [];

        const keys = Object.keys(filiacaoPartidaria) as Array<keyof FiliacaoPartidaria>;

        if (!this.validateKeys(keys)) {
             console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        const orderedKeys: Array<keyof FiliacaoPartidaria> = [
            'sigla',
            'detalhes',
            'vinculos'
        ];
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(filiacaoPartidaria);
                }

                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}
