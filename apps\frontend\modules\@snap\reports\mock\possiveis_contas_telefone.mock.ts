import { PossivelConta } from "../model/PossiveisContas";

const possivelContaPAIs1: PossivelConta = {
    site: {
        value: "AmazonPhone",
        label: "Site",
        source: ["ProvedorDeAplicacacaoDaInternettelefone"],
        is_deleted: false
    },
    detalhes: {
        "entrada_telefone": {
            "value": "5521999891661",
            "label": "Entrada TELEFONE",
            "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "found": {
            value: "Sim",
            label: "Conta encontrada?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        }
    }
};

const possivelContaPAIs2: PossivelConta = {
    site: {
        value: "FacebookPhone",
        label: "Site",
        source: ["ProvedorDeAplicacacaoDaInternettelefone"],
        is_deleted: false
    },
    detalhes: {
        "entrada_telefone": {
            "value": "5521999891661",
            "label": "Entrada TELEFONE",
            "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "found": {
            value: "Não",
            label: "Conta encontrada?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        }
    }
};

const possivelContaPAIs3: PossivelConta = {
    site: {
        value: "TwitterPhone",
        label: "Site",
        source: ["ProvedorDeAplicacacaoDaInternettelefone"],
        is_deleted: false
    },
    detalhes: {
        "entrada_telefone": {
            "value": "5521999891661",
            "label": "Entrada TELEFONE",
            "source": ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "found": {
            value: "Não",
            label: "Conta encontrada?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "alerta": {
            value: "Não",
            label: "Alvo alertado?",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        },
        "tipo_alerta": {
            value: "Não se Aplica",
            label: "Tipo de Alerta",
            source: ["ProvedorDeAplicacacaoDaInternettelefone"],
            is_deleted: false
        }
    }
};


export const possiveisContasMockTelefone: PossivelConta[] = [
    {
        "site": {
            "value": "BrasilBitcoin",
            "label": "Site",
            "source": [
                "Provedor de Aplicacação da Internet"
            ],
            "is_deleted": false
        },
        "detalhes": {
            "existe": {
                "value": "false",
                "label": "existe",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            },
            "termo procurado": {
                "value": "110.452.316-73",
                "label": "termo procurado",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            }
        }
    },
    {
        "site": {
            "value": "CasasBahia",
            "label": "Site",
            "source": [
                "Provedor de Aplicacação da Internet"
            ],
            "is_deleted": false
        },
        "detalhes": {
            "existe": {
                "value": "Erro: A aplicação bloqueou a requisicao.",
                "label": "existe",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            },
            "termo procurado": {
                "value": "110.452.316-73",
                "label": "termo procurado",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            }
        }
    },
    {
        "site": {
            "value": "Digipare",
            "label": "Site",
            "source": [
                "Provedor de Aplicacação da Internet"
            ],
            "is_deleted": false
        },
        "detalhes": {
            "existe": {
                "value": "false",
                "label": "existe",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            },
            "termo procurado": {
                "value": "110.452.316-73",
                "label": "termo procurado",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            }
        }
    },
    {
        "site": {
            "value": "Extra",
            "label": "Site",
            "source": [
                "Provedor de Aplicacação da Internet"
            ],
            "is_deleted": false
        },
        "detalhes": {
            "existe": {
                "value": "true",
                "label": "existe",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            },
            "termo procurado": {
                "value": "110.452.316-73",
                "label": "termo procurado",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            }
        }
    },
    {
        "site": {
            "value": "PontoFrio",
            "label": "Site",
            "source": [
                "Provedor de Aplicacação da Internet"
            ],
            "is_deleted": false
        },
        "detalhes": {
            "existe": {
                "value": "false",
                "label": "existe",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            },
            "termo procurado": {
                "value": "110.452.316-73",
                "label": "termo procurado",
                "source": [
                    "Provedor de Aplicacação da Internet"
                ],
                "is_deleted": false
            }
        }
    }
]