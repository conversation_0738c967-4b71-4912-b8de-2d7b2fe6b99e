// Função que deriva a chave de criptografia utilizando Argon2.
// A chave é derivada a partir da senha do usuário e do salt.
// [ADAPTAR] A lógica de derivação pode ser ajustada para produção conforme as políticas de segurança.
import argon2 from "argon2-browser/dist/argon2-bundled.min.js";
import { CryptoResult, EncryptedPayload } from "~/hooks/useEncryption";
import { ReportData, NewReportResponse } from "~/types/global";
import { REPORT_CONSTANTS, NON_ENCRYPTED_KEYS } from '~/helpers/constants';
import { extractNgrams } from ".";

export async function deriveEncryptionKeyArgon2(
  password: string | null | undefined,
  salt: string | null | undefined
): Promise<CryptoKey> {
  if (!password) {
    throw new Error("Password is required");
  }

  if (!salt) {
    throw new Error("Salt is required");
  }

  const hash = await argon2.hash({
    pass: password,
    salt: salt,
    time: 2, // Ajuste conforme necessidade
    mem: 1024, // Ajuste conforme necessidade
    hashLen: 32, // 32 bytes = chave de 256 bits
    type: argon2.ArgonType.Argon2id,
  });

  // Cria uma cópia do buffer para garantir que é um ArrayBuffer "puro"
  const keyBuffer = new Uint8Array(hash.hash).slice().buffer;

  return crypto.subtle.importKey(
    "raw",
    keyBuffer,
    { name: "AES-GCM" },
    true, // chave extraível para exportar para o worker
    ["encrypt", "decrypt"]
  );
}

export const encryptReportPayload = async (
  payload: NewReportResponse,
  encryptData: (data: any) => Promise<CryptoResult<EncryptedPayload>>,
  nonEncryptedKeys: string[] = NON_ENCRYPTED_KEYS
): Promise<ReportData> => {
  try {
    const entries = Object.entries(payload);
    const encryptedEntries = await Promise.all(
      entries.map(async ([key, value]) => {
        // Skip non-encrypted fields if any exist
        if (nonEncryptedKeys.includes(key)) {
          return [key, value];
        }

        try {
          const encryptedValue = await encryptData(value);
          console.log("encryptReportPayload value", value);
          return [key, encryptedValue.data];
        } catch (err: any) {
          throw new Error(
            `Error encrypting key "${key}": ${err.message || err}`
          );
        }
      })
    );
    return Object.fromEntries(encryptedEntries) as ReportData;
  } catch (error) {
    throw new Error(`encryptReportPayload error: ${error}`);
  }
};

export const decryptReportPayload = async (
  encryptedPayload: ReportData,
  decryptData: (data: EncryptedPayload) => Promise<CryptoResult<any>>
): Promise<NewReportResponse> => {
  try {
    const entries = Object.entries(encryptedPayload);

    const decryptedEntries = await Promise.all(
      entries.map(async ([key, encryptedValue]) => {
        try {
          // Skip non-encrypted fields if any exist
          if (
            !encryptedValue ||
            typeof encryptedValue !== "object" ||
            !("encrypted" in encryptedValue) ||
            !("iv" in encryptedValue)
          ) {
            return [key, encryptedValue];
          }

          const decryptedResult = await decryptData({
            encrypted: encryptedValue.encrypted as string,
            iv: encryptedValue.iv as string,
          });

          if (!decryptedResult.success) {
            throw new Error(decryptedResult.error || "Unknown error");
          }

          return [key, decryptedResult.data];
        } catch (err: any) {
          throw new Error(
            `Error decrypting key "${key}": ${err.message || err}`
          );
        }
      })
    );

    return Object.fromEntries(decryptedEntries) as NewReportResponse;
  } catch (error) {
    throw new Error(`decryptReportPayload error: ${error}`);
  }
};

export function isResultObject(
  result: unknown
): result is { data: NewReportResponse } {
  const DATA_KEY = REPORT_CONSTANTS.report_result_key;

  return (
    typeof result === "object" &&
    result !== null &&
    DATA_KEY in result &&
    typeof (result as any).data === "object"
  );
}

/**
 * Dada uma chave de criptografia, deriva uma chave HMAC para assinatura.
 */
async function deriveHmacKey(rawKey: ArrayBuffer): Promise<CryptoKey> {
  return crypto.subtle.importKey(
    'raw', rawKey, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
  );
}

/**
 * Assina uma mensagem com HMAC-SHA256 usando uma chave.
 */
async function hmacSign(key: CryptoKey, message: string): Promise<string> {
  const data = new TextEncoder().encode(message);
  const sig = await crypto.subtle.sign('HMAC', key, data);
  const bytes = new Uint8Array(sig);
  return Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Dado um ArrayBuffer de chave e um objeto de relatório,
 * extrai n-grams e os criptografa com HMAC-SHA256.
 * @throws {Error} em caso de rawKey inválido ou sem campos pesquisáveis
 */
export async function encryptNgramsDeterministic(
  rawKey: ArrayBuffer,
  report: Record<string, any>,
  n = 3
): Promise<{ [key: string]: string[] }> {
  // Validação de entrada
  if (!(rawKey instanceof ArrayBuffer) || rawKey.byteLength === 0) {
    throw new Error('Raw key inválida para HMAC');
  }

  // Deriva chave HMAC
  let hmacKey: CryptoKey;
  try {
    hmacKey = await deriveHmacKey(rawKey);
  } catch (err) {
    throw new Error(`Falha ao derivar HMAC key: ${(err as Error).message}`);
  }

  // Extrai n-grams do objeto
  const ngrams = extractNgrams(report, n);
  if (Object.keys(ngrams).length === 0) {
    throw new Error('Nenhum campo pesquisável encontrou n-grams');
  }

  console.log("encryptNgramsDeterministic ngrams", ngrams);

  // Aplica HMAC em cada gram
  const encrypted: Record<string, string[]> = {};
  for (const [prop, grams] of Object.entries(ngrams)) {
    try {
      encrypted[prop] = await Promise.all(
        (grams || []).map(g => hmacSign(hmacKey, g))
      );
    } catch (err) {
      throw new Error(`Erro ao assinar n-grams para "${prop}": ${(err as Error).message}`);
    }
  }

  console.log("encryptNgramsDeterministic encrypted", encrypted);
  return encrypted;
}