import React from "react";
import { Sociedade } from "../../model/Sociedades";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray, renderSourceTooltip } from "./helpers.strategy";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";

export class RenderSociedades implements RenderStrategy<Sociedade> {

    validateKeys = (keys: Array<keyof Sociedade>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    /**
     * Renders the enderecos information for a sociedade
     * @param sociedade The Sociedade object
     * @returns React element or null if no data
     */
    renderEnderecos = (sociedade?: Sociedade): React.ReactElement | null => {
        //return renderValidArray('enderecos', sociedade || {});
        return renderValidArray('enderecos', sociedade || {}, [], true /* hasLabel */);
    };

    /**
     * Renders the telefones information for a sociedade
     * @param sociedade The Sociedade object
     * @returns React element or null if no data
     */
    renderTelefones = (sociedade?: Sociedade): React.ReactElement | null => {
        return renderValidArray('telefones', sociedade || {}, ['operadora', 'whatsapp'], false /* hasLabel */);
    };

    /**
     * Renders the emails information for a sociedade
     * @param sociedade The Sociedade object
     * @returns React element or null if no data
     */
    renderEmails = (sociedade?: Sociedade): React.ReactElement | null => {
        //return renderSimpleArray('emails', sociedade || {}, 'email address');
        return renderValidArray('emails', sociedade || {}, [], false /* hasLabel */);
    };

    formatByKey: Record<
        string,
        (sociedade?: Sociedade) => React.ReactElement | null
    > = {
            razao_social: (sociedade?: Sociedade) => {
                if (!sociedade?.razao_social) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6 group">
                            <CustomReadOnlyInputField
                                label={(sociedade.razao_social.label || "Nome Completo").toUpperCase()}
                                colorClass="bg-primary"
                                labelTextClass="text-accent"
                                value={parseValue(sociedade.razao_social.value)}
                                tooltip={renderSourceTooltip(sociedade.razao_social.source)}
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (sociedade?: Sociedade) => {
                if (!sociedade?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="mb-6">
                        {Object.entries(sociedade.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="group">
                                <CustomReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={String(value.value)}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                    tooltip={renderSourceTooltip(value.source)}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },

            enderecos: (sociedade?: Sociedade) => {
                return this.renderEnderecos(sociedade);
            },

            telefones: (sociedade?: Sociedade) => {
                return renderValidArray('telefones', sociedade || {}, ["área code"], false /* hasLabel */);
            },

            emails: (sociedade?: Sociedade) => {
                return this.renderEmails(sociedade);
            }
        }

    render = (sociedade: Sociedade): React.ReactElement[] => {
        const keys = Object.keys(sociedade) as Array<keyof Sociedade>;

        if (!this.validateKeys(keys)) {
            console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof Sociedade> = [
            'razao_social',
            'detalhes',
            'enderecos',
            'telefones',
            'emails'
        ];

        // Filter the keys to only include those that exist in the sociedade object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(sociedade);
                }

                // Check if the value is an array with the expected structure
                const value = sociedade[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, sociedade);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}