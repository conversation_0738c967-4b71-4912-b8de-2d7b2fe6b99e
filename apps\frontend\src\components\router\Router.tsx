import { lazy } from "react";
import DefaultLayout from "~/layouts/DefaultLayout";
import LoginLayout from "~/layouts/LoginLayout";
import { Routes, Route } from "react-router";
import ProtectedRoute from "~/components/router/ProtectedRoute";

const LoginPage = lazy(() => import("~/pages/login"));
const ReportListPage = lazy(() => import("~/pages/report/list"));
const ReportDetailsPage = lazy(() => import("~/pages/report/details"));
const AccountPage = lazy(() => import("~/pages/account"));

const Router = () => {
  return (
    <Routes>
      <Route element={<LoginLayout />}>
        <Route path="/login" element={<LoginPage />} />
      </Route>

      {/* Rotas protegidas */}
      <Route element={<ProtectedRoute />}>
        <Route element={<DefaultLayout />}>
          <Route path="/" element={<ReportListPage />} />
          <Route path="report/:type/:id" element={<ReportDetailsPage />} />
          <Route path="/conta/configuracoes" element={<AccountPage />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default Router;
