import { ValueWithSource } from "./ValueWithSource";

interface _RecursoPublicoBase {
    valor?: string;
    "valor pago"?: string;
    "valor empenhado"?: string;
    "valor liquidado"?: string;
    data?: string;
    credor?: string;
    "label default key"?: string;
}

// Interface for Transparência MG
interface _RecursoPublicoMG extends _RecursoPublicoBase {
    "categoria economica"?: string;
    "grupo de despesa"?: string;
}

// Interface for Transparência AM
interface _RecursoPublicoAM extends _RecursoPublicoBase {
    "nome do orgao"?: string;
    "codigo do orgao"?: string;
    "pagamento execicio anterior"?: string;
    "numero de ordem bancaria"?: string;
    "numero da nota de lancamento"?: string;
    "numero do empenho"?: string;
    "fonte do recurso"?: string;
    classificacao?: string;
}

// Interface for Transparência SC
interface _RecursoPublicoSC extends _RecursoPublicoBase {
    "nome do orgao"?: string;
    "unidade gestora"?: string;
    "status pagamento"?: string;
    pagamento?: string;
    "numero de ordem bancaria"?: string;
    historico?: string;
}

// Interface for Transparência Manaus
interface _RecursoPublicoManaus extends _RecursoPublicoBase {
    numero?: string;
    favorecido?: string;
    "unidade gestora"?: string;
    acrescimo?: string;
    anulado?: string;
    pago?: string;
    liquidado?: string;
}

// Interface for Transparência DF
interface _RecursoPublicoDF extends _RecursoPublicoBase {
    "unidade gestora"?: string;
    ano_mes?: string;
    "tipo de despesa"?: string;
    id?: string;
    esfera?: string;
    "programa de trabalho"?: string;
    funcao?: string;
    subfuncao?: string;
    programa?: string;
    "categoria economica"?: string;
    "grupo de natureza da despesa"?: string;
    "modalidade de aplicacao"?: string;
    elemento?: string;
    subelemento?: string;
    "fonte de recurso"?: string;
    empenhado?: string;
    liquidado?: string;
    "pago ex"?: string;
    "pago rpp"?: string;
    "pago rpnp"?: string;
    "pago ret"?: string;
}

// Union type for all resource types
export type _RecursoPublico = _RecursoPublicoBase | _RecursoPublicoMG | _RecursoPublicoAM | _RecursoPublicoSC | _RecursoPublicoManaus | _RecursoPublicoDF;

export interface RecursoPublico {
    orgao: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
}
