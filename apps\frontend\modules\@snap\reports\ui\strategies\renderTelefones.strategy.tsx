import React from "react";
import { Telefone } from "../../model/Telefones";
import { RenderStrategy } from "./RenderStrategy";
import { renderValidArray } from "./helpers.strategy";


export class RenderTelefones implements RenderStrategy<Telefone> {
  validateKeys = (keys: Array<keyof Telefone>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (telefones?: Telefone) => React.ReactElement | null
  > = {
      detalhes: (telefones?: Telefone) => {
        if (!telefones?.detalhes?.length) return null;

        const noRenderProps = ['label default key', 'bookmark', /* 'whatsapp' */];
        const titleProp = "numero"

        return renderValidArray('detalhes', telefones || {}, noRenderProps, true /* hasLabel */, false /* hasTitle */, true /* hasGenericLabel */);
      },
    };

  render = (data: Telefone): React.ReactElement[] => {
    const elements: React.ReactElement[] = [];

    Object.keys(this.formatByKey).forEach((key) => {
      const element = this.formatByKey[key](data);
      if (element) {
        elements.push(
          <React.Fragment key={`fragment-${key}`}>{element}</React.Fragment>
        );
      }
    });

    return elements;
  };
}
