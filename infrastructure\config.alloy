prometheus.remote_write "metrics_write" {
    endpoint {
        name = "default"
        url  = "http://prometheus:9090"
        queue_config { }
        metadata_config { }
    }
}

loki.process "logs_process_client" {
    forward_to = [loki.write.logs_write_client.receiver]

    stage.logfmt {
        mapping = { "kind" = "", "service_name" = "", "app" = "" }
    }

    stage.labels {
        values = { "kind" = "kind", "service_name" = "service_name", "app" = "app" }
    }
}

loki.write "logs_write_client" {
    endpoint {
        url = "http://loki:3100/loki/api/v1/push"
    }
}

logging {
    level = "info"
}

faro.receiver "integrations_app_agent_receiver" {
    server {
        listen_address           = "0.0.0.0"
        cors_allowed_origins     = ["http://localhost:8080"]
        api_key                  = "1127"
        max_allowed_payload_size = "10MiB"

        rate_limiting {
            rate = 100
        }
    }

    sourcemaps { }

    output {
        logs   = [loki.process.logs_process_client.receiver]
        traces = [otelcol.exporter.otlp.trace_write.input]
    }
}

otelcol.receiver.otlp "default" {
    grpc {
        include_metadata = true
    }

    output {
        metrics = []
        logs    = []
        traces  = [otelcol.exporter.otlp.trace_write.input]
    }
}

otelcol.exporter.otlp "trace_write" {
    retry_on_failure {
        max_elapsed_time = "1m0s"
    }

    client {
        endpoint = "http://tempo:3200"
    }
}