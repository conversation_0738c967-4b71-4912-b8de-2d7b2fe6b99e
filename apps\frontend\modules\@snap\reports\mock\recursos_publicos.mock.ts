import { RecursoPublico } from "../model/RecursosPublicos";

// Mock for Transparência MG
const recursoPublicoMG1: RecursoPublico = {
    orgao: {
        value: "Secretaria de Estado de Educação",
        label: "Nome do Orgão",
        source: ["PortalDaTransparenciaDeMinasGerais"],
        is_deleted: false
    },
    detalhes: {
        "valor pago": {
            value: "R$ 15.750,00",
            label: "Valor Pago (R$)",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        },
        "valor empenhado": {
            value: "R$ 16.000,00",
            label: "Valor Empenhado",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        },
        "valor liquidado": {
            value: "R$ 15.750,00",
            label: "Valor Liquidado",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        }
    }
};

// Mock for Transparência MG - Despesas
const recursoPublicoMG2: RecursoPublico = {
    orgao: {
        value: "Secretaria de Estado de Educação",
        label: "Nome do Orgão",
        source: ["PortalDaTransparenciaDeMinasGerais"],
        is_deleted: false
    },
    detalhes: {
        "valor pago": {
            value: "R$ 30.950,00",
            label: "Valor Pago (R$)",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        },
        "valor empenhado": {
            value: "R$ 28.000,00",
            label: "Valor Empenhado",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        },
        "valor liquidado": {
            value: "R$ 30.950,00",
            label: "Valor Liquidado",
            source: ["PortalDaTransparenciaDeMinasGerais"],
            is_deleted: false
        }
    }
};

// Mock for Transparência AM
const recursoPublicoAM: RecursoPublico = {
    orgao: {
        value: "Secretaria de Estado de Educação",
        label: "Nome do Orgão",
        source: ["PortalDaTransparenciaDoAmazonas"],
        is_deleted: false
    },
    detalhes: {
        "nome do orgao": {
            value: "Secretaria de Estado de Educação",
            label: "Nome do Orgão",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "codigo do orgao": {
            value: "00123",
            label: "Código do Orgão",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "pagamento execicio anterior": {
            value: "R$ 0,00",
            label: "Pagamento Execício Anterior",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "numero de ordem bancaria": {
            value: "2023OB00123",
            label: "Nº Ordem Bancária",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "numero da nota de lancamento": {
            value: "2023NL00456",
            label: "Nº Nota de Lançamento",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "numero do empenho": {
            value: "2023EM00789",
            label: "Nº Empenho",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        "fonte do recurso": {
            value: "Recursos Ordinários",
            label: "Fonte do Recurso",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        },
        classificacao: {
            value: "Educação",
            label: "Classificação",
            source: ["PortalDaTransparenciaDoAmazonas"],
            is_deleted: false
        }
    }
};


const recursoPublicoSC: RecursoPublico = {
    orgao: {
        value: "Secretaria de Estado da Saúde",
        label: "Nome do Orgão",
        source: ["Portal da Transparência de Santa Catarina"],
        is_deleted: false
    },
    detalhes: {
        "nome do orgao": {
            value: "Secretaria de Estado da Saúde",
            label: "Nome do Orgão",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        data: {
            value: "10/06/2023",
            label: "Data Despesa",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        credor: {
            value: "Maria Oliveira",
            label: "Credor",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        "unidade gestora": {
            value: "Fundo Estadual de Saúde",
            label: "Unidade Gestora",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        "status pagamento": {
            value: "Pago",
            label: "Status Pagamento",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        pagamento: {
            value: "Integral",
            label: "Pagamento",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        valor: {
            value: "R$ 7.800,00",
            label: "Valor",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        "numero de ordem bancaria": {
            value: "2023OB00456",
            label: "Nº Ordem Bancária",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        },
        historico: {
            value: "Prestação de serviços de consultoria",
            label: "Histórico",
            source: ["Portal da Transparência de Santa Catarina"],
            is_deleted: false
        }
    }
};

// Mock for Transparência Manaus
const recursoPublicoManaus: RecursoPublico = {
    orgao: {
        value: "Secretaria Municipal de Educação",
        label: "Nome do Orgão",
        source: ["Portal da Transparência de Manaus"],
        is_deleted: false
    },
    detalhes: {
        numero: {
            value: "2023NE00321",
            label: "Número",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        favorecido: {
            value: "Carlos Santos",
            label: "Favorecido",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        "unidade gestora": {
            value: "Secretaria Municipal de Educação",
            label: "Unidade Gestora",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        data: {
            value: "05/04/2023",
            label: "Data",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        "valor empenhado": {
            value: "R$ 22.500,00",
            label: "Valor Empenhado (R$)",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        acrescimo: {
            value: "R$ 0,00",
            label: "Acréscimo (R$)",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        anulado: {
            value: "R$ 0,00",
            label: "Anulado (R$)",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        pago: {
            value: "R$ 22.500,00",
            label: "Valor Pago (R$)",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        },
        liquidado: {
            value: "R$ 22.500,00",
            label: "Valor Liquidado (R$)",
            source: ["Portal da Transparência de Manaus"],
            is_deleted: false
        }
    }
};

// Mock for Transparência DF
const recursoPublicoDF: RecursoPublico = {
   orgao: {
        value: "Secretaria de Estado de Educação",
        label: "Nome do Orgão",
        source: ["Portal da Transparência do Distrito Federal"],
        is_deleted: false
    },
    detalhes: {
        valor: {
            value: "R$ 18.900,00",
            label: "Valor (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        data: {
            value: "12/07/2023",
            label: "Data Despesa",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        credor: {
            value: "Ana Pereira",
            label: "Credor",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "valor empenhado": {
            value: "R$ 19.000,00",
            label: "Valor Empenhado",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "valor liquidado": {
            value: "R$ 18.900,00",
            label: "Valor Liquidado",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        }
    }
};

// Mock for Transparência DF - Viagem
const recursoPublicoDF_Viagem: RecursoPublico = {
    orgao: {
        value: "Secretaria de Estado de Educação",
        label: "Nome do Orgão",
        source: ["Portal da Transparência do Distrito Federal"],
        is_deleted: false
    },
    detalhes: {
        "unidade gestora": {
            value: "Secretaria de Estado de Economia",
            label: "Unidade Gestora",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        ano_mes: {
            value: "2023/07",
            label: "Ano/Mês",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        credor: {
            value: "Pedro Almeida",
            label: "Credor",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "tipo de despesa": {
            value: "Diárias",
            label: "Tipo de Despesa",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        id: {
            value: "DF2023-0789",
            label: "ID",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        esfera: {
            value: "Fiscal",
            label: "Esfera",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "programa de trabalho": {
            value: "04.122.8203.8517.0051",
            label: "Programa de Trabalho",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        funcao: {
            value: "Administração",
            label: "Função",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        subfuncao: {
            value: "Administração Geral",
            label: "Sub-Função",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        programa: {
            value: "Gestão Pública",
            label: "Programa",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "categoria economica": {
            value: "Despesas Correntes",
            label: "Categoria Econômica",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "grupo de natureza da despesa": {
            value: "Outras Despesas Correntes",
            label: "Grupo de Despesas",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "modalidade de aplicacao": {
            value: "Aplicação Direta",
            label: "Modalidade de Aplicação",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        elemento: {
            value: "Diárias - Civil",
            label: "Elemento",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        subelemento: {
            value: "Diárias no País",
            label: "Sub-Elemento",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "fonte de recurso": {
            value: "Recursos Ordinários",
            label: "Fonte do Recurso",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        empenhado: {
            value: "R$ 3.500,00",
            label: "Valor Empenhado (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        liquidado: {
            value: "R$ 3.500,00",
            label: "Valor Liquidado (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "pago ex": {
            value: "R$ 0,00",
            label: "Pago Ex (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "pago rpp": {
            value: "R$ 0,00",
            label: "Pago RPP (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "pago rpnp": {
            value: "R$ 0,00",
            label: "Pago RPNP (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        "pago ret": {
            value: "R$ 0,00",
            label: "Pago RET (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        },
        valor: {
            value: "R$ 3.500,00",
            label: "Valor (R$)",
            source: ["Portal da Transparência do Distrito Federal"],
            is_deleted: false
        }
    }
};

// Export the complete mock
export const recursosPublicosMock: RecursoPublico[] = [
    recursoPublicoMG1,
    recursoPublicoMG2,
    recursoPublicoAM,
    recursoPublicoSC,
    recursoPublicoManaus,
    recursoPublicoDF,
    recursoPublicoDF_Viagem
]
    ;
