import { useMemo } from "react";
import { cn } from "~/lib/utils";
import { Tabs } from '@snap/design-system'
import * as T from "../report/details/tabs"


const TabContainer = () => {

    const tabList = [
        {
            value: 'registries',
            key: 'registries',
            label: 'REGISTROS',
            children: <T.ReportRecordsList />
        },
        {
            value: 'trash',
            key: 'trash',
            label: 'LIXEIRA',
            children: <T.ReportTrash />
        }
    ]


    return (
        <Tabs items={tabList} className="[&_[role=tab]]:cursor-pointer" />
    );
};

export default TabContainer;
