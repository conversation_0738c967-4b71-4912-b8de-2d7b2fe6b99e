import { <PERSON>ere<PERSON>, _Endereco } from "../model/Enderecos";

export const enderecoMockTelefone: Endereco = {
    "detalhes": [
        {
            "value": {
                "logradouro": {
                    "value": "RUA DO ROMANCISTA",
                    "label": "Logradouro",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "complemento": {
                    "value": "CS",
                    "label": "Complemento",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "bairro": {
                    "value": "FREGUESIA ILHA DO GOVERNADOR",
                    "label": "Bairro",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "cidade": {
                    "value": "RIO DE JANEIRO",
                    "label": "Cidade",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "estado ou regiao": {
                    "value": "BR_RJ",
                    "label": "Estado",
                    "source": ["SNAP"],
                    "is_deleted": false
                },
                "cep ou zipcode": {
                    "value": "21911170",
                    "label": "CEP",
                    "source": ["SNAP"],
                    "is_deleted": false
                }
            },
            "label": "Endereço",
            "source": ["SNAP"],
            "is_deleted": false
        },
        {
            "value": {
                "logradouro": {
                    "value": "RUA AMÉLIA THRETAS",
                    "label": "Logradouro",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "complemento": {
                    "value": "C",
                    "label": "Complemento",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "bairro": {
                    "value": "FREGUESIA ILHA DO GOVERNADOR",
                    "label": "Bairro",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "city": {
                    "value": "RIO DE JANEIRO",
                    "label": "Cidade",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "cep ou zipcode": {
                    "value": "21910087",
                    "label": "CEP",
                    "source": ["IRBIS"],
                    "is_deleted": false
                },
                "area": {
                    "value": "RJ",
                    "label": "Estado",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            },
            "label": "Endereço",
            "source": ["IRBIS"],
            "is_deleted": false
        }
    ]
};