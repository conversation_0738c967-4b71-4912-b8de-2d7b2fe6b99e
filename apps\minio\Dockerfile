FROM minio/minio:RELEASE.2025-04-22T22-12-26Z
LABEL authors="gbrabelo"

ARG DEV_MODE=true
ARG INSTALL_PATH=/opt/minio/
ENV WAIT_ON_KAFKA=${INSTALL_PATH}/scripts/wait_until_kafka_alive.sh
ENV WAIT_ON_MINIO_START=${INSTALL_PATH}/scripts/wait_until_minio_alive.sh
ENV WAIT_ON_MINIO_STOP=${INSTALL_PATH}/scripts/wait_until_minio_dead.sh

COPY ./apps/minio/minio_init.sh ${INSTALL_PATH}/scripts/
COPY ./scripts/wait_until_kafka_alive.sh ${INSTALL_PATH}/scripts/
COPY ./scripts/wait_until_minio_alive.sh ${INSTALL_PATH}/scripts/
COPY ./scripts/wait_until_minio_dead.sh ${INSTALL_PATH}/scripts/
WORKDIR $INSTALL_PATH

#CMD ./scripts/minio_init.sh
ENTRYPOINT ["/bin/bash"]
CMD ["./scripts/minio_init.sh"]
#"/bin/bash",

# RUN /bin/bash ./scripts/minio_init.sh

#CMD ["server", "/data", "--console-address", ":9001"]
