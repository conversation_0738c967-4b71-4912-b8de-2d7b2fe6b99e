import { ProcessedItem, AccordionGroup, OriginConfig } from "~/types/global";
import {
  findItemWithBookmark,
  findPeopleByRelationship,
  findPersonMother,
  getObjectProps,
  getSingular,
  getValueAtPath,
  parseKeyToLabel,
  removeNoRenderProperties,
} from "~/helpers/";
import { accordionUiGroups } from "./constants";

export function parseFieldDataForUI(
  data: any,
  parent: string,
  path?: string,
  skipRootAccordion: boolean = false
): ProcessedItem[] {
  // This array will store the fields to return.
  const fields: ProcessedItem[] = [];

  if (data !== null && typeof data === "object") {
    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        // Build the itemPath for the current item.
        const itemPath = `${parent}[${index}]`;

        // Decide if we want to create an Accordion wrapper.
        // If skipRootAccordion is false, we create a new accordion node with nested children.
        if (!skipRootAccordion) {
          // Create nested fields by recursively processing the item.
          const nestedFields = parseFieldDataForUI(
            item,
            parent,
            itemPath,
            false
          );
          const title = `${parseKeyToLabel(parent)} ${index + 1}`;
          fields.push({
            type: "Accordion",
            title,
            // Nest the parsed children inside the accordion.
            children: nestedFields,
            parent,
            path: itemPath,
          });
        } else {
          // If skipRootAccordion is true, we simply flatten the structure:
          // we append all children directly without wrapping them in an accordion.
          fields.push(...parseFieldDataForUI(item, parent, itemPath, false));
        }
      });
    } else {
      for (const key in data) {
        // Skip numeric keys if they are just object indices.
        /*         if (/^\d+$/.test(key)) {
          fields.push(...parseFieldDataForUI(data[key], parent, path ? `${path}.${key}` : key));
          continue;
        } */

        const value = data[key];
        const currentPath = parent ? `${parent}.${key}` : key;
        const label = parseKeyToLabel(getSingular(key));

        if (value !== null && typeof value === "object") {
          // Process nested objects: create an accordion node and recursively parse its children.
          const nestedFields = parseFieldDataForUI(
            value,
            key,
            currentPath,
            false
          );
          fields.push({
            type: "Accordion",
            title: parseKeyToLabel(key),
            children: nestedFields,
            parent,
            path: currentPath,
          });
        } else {
          // For primitive values, return a TextField.
          fields.push({
            type: "TextField",
            name: key,
            label: label,
            value: value,
            children: null,
            parent,
            path: currentPath,
          });
        }
      }
    }
  }

  return fields;
}

function applyFilterFromConfig(originData: any, config: OriginConfig): any {
  let resultData = originData;
  // Valor alvo com base no path definido na configuração
  const targetData = getValueAtPath(originData, config.path);

  console.log("Valor alvo:", config, "TARGET DATA: ", targetData);

  switch (config.filterFunction?.name) {
    case "findItemWithBookmark":
      if (Array.isArray(targetData)) {
        const primaryResult = findItemWithBookmark(targetData, 4);
        if (primaryResult !== undefined) {
          resultData = primaryResult;
          if (config.hasMotherName) {
            const motherData = findPersonMother(resultData["pessoa"]);
            if (motherData) {
              resultData = { ...resultData, ...motherData };
            }
          }
        } else {
          resultData = targetData;
        }
      }
      break;
    case "findPeopleByRelationship":
      if (Array.isArray(targetData)) {
        resultData = findPeopleByRelationship(
          targetData,
          ...(config.filterFunction.args as [string[], boolean])
        );
      } else if (
        typeof targetData === "object" &&
        Array.isArray(targetData["pessoa"])
      ) {
        resultData = findPeopleByRelationship(
          targetData["pessoa"],
          ...(config.filterFunction.args as [string[], boolean])
        );
      }
      break;
    case "getObjectProps":
      if (
        config.filterFunction.args &&
        config.filterFunction.args.length === 2
      ) {
        const [arrayKey, propsToCopy] = config.filterFunction.args;
        resultData = getObjectProps(targetData, arrayKey, propsToCopy);
      }
      break;
    default:
      resultData = targetData;
  }

  // Remover props que não devem ser renderizadas
  if (config.noRenderPropList && Array.isArray(config.noRenderPropList)) {
    resultData = removeNoRenderProperties(resultData, config.noRenderPropList);
  }

  console.log("Resultado:", config, resultData);
  return resultData;
}

export function processReportData(
  reportData: any
): { data: AccordionGroup[] } | { error: string } {
  try {
    if (!reportData?.result?.data) {
      return { error: "No report data provided" };
    }

    const data = reportData.result.data;
    const reportTypes = Object.keys(data);
    if (reportTypes.length === 0) {
      return { data: [] };
    }

    const reportType = reportTypes[0];
    // Configuração de filtros e renderização para um tipo de report - ex.: "cpf"
    const renderConfig = accordionUiGroups[reportType];
    if (!renderConfig) {
      console.error(
        `No configuration info found for report type: ${reportType}`
      );
      return { data: [] };
    }
    const reportTypeData = data[reportType][0]; // ex: "data": {"cpf": [{...}]}
    const groups: AccordionGroup[] = [];

    // Itera sobre cada grupo na configuração unificada
    for (const groupName in renderConfig) {
      const groupConfig = renderConfig[groupName];
      let groupFields: ProcessedItem[] = [];

      // Itera sobre cada origin definido para o grupo
      for (const originKey in groupConfig.origins) {
        if (reportTypeData.hasOwnProperty(originKey)) {
          let originData = reportTypeData[originKey];
          const originConfigs = groupConfig.origins[originKey];

          // if (originConfig) {
          //   originData = applyFilterFromConfig(originData, originConfig);
          // }
          // groupFields.push(
          //   ...parseFieldDataForUI(originData, originKey, undefined, true)
          // );

          if (Array.isArray(originConfigs)) {
            originConfigs.forEach((config) => {
              // Aplica a configuração de filtro se existir para a origin específica
              const filteredData = applyFilterFromConfig(originData, config);

              // Último argumento true para não gerar um accordion para a origin key.
              // o terceiro argumento undefined pois o path de cada item é definido apenas quando parseFieldDataForUI é chamada recursivamente.
              groupFields.push(
                ...parseFieldDataForUI(filteredData, originKey, undefined, true)
              );
            });
          }
        }
      }

      if (groupFields.length > 0) {
        // Post-process to flatten  Accordion if present
        const newGroupFields: ProcessedItem[] = [];
        groupFields.forEach((field) => {
          if (
            field.type === "Accordion" &&
            Object.keys(groupConfig.origins).includes(field.parent)
          ) {
            // Add its children directly
            newGroupFields.push(...(field.children as ProcessedItem[]));
          } else {
            newGroupFields.push(field);
          }
        });
        groups.push({
          title: groupName,
          subtitle: groupConfig.subtitle,
          fields: newGroupFields,
        });
      }
    }
    console.log("data: groups:", groups);
    return { data: groups };
  } catch (error: any) {
    return { error: error.message };
  }
}
