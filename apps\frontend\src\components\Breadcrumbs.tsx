import React from "react";
import { Link, useNavigate } from "react-router";
import { Home, ChevronRight } from "lucide-react";
import { BreadcrumbItem } from "~/types/global";

interface BreadcrumbNavigationProps {
  breadcrumbs?: BreadcrumbItem[];
}

const baseLinkClasses =
  "flex items-center gap-4 font-mono uppercase tracking-wide transition-opacity hover:opacity-80";

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  breadcrumbs = [],
}) => {
  const navigate = useNavigate();

  const homeLink: BreadcrumbItem = {
    title: "dashboard",
    icon: <Home />,
    href: "/",
  };

  const defaultBreadcrumbs = [homeLink, ...breadcrumbs];
  const totalBreadcrumbs = defaultBreadcrumbs.length;

  return (
    <div className="flex items-center w-full">
      {defaultBreadcrumbs.map((crumb, index) => {
        const isLast = index === totalBreadcrumbs - 1;
        const isCurrentPage = isLast && totalBreadcrumbs > 1;

        return (
          <React.Fragment key={index}>
            {index > 0 && <ChevronRight className="mx-2" size={16} />}
            {!isCurrentPage && crumb.href ? (
              <Link
                to={crumb.href}
                className={baseLinkClasses}
                onClick={(e) => {
                  e.preventDefault();
                  navigate(crumb.href || "", { replace: true });
                }}
              >
                {crumb.icon && crumb.icon}
                <p>{crumb.title}</p>
              </Link>
            ) : (
              <span className={`${baseLinkClasses} text-brand-primary`}>
                {crumb.icon && crumb.icon}
                <p>{crumb.title}</p>
              </span>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default BreadcrumbNavigation;
