import { REPORT_CONSTANTS } from "./config/constants";

export type ReportType = "cpf" | "cnpj" | "email" | "telefone" | null;

export interface ReportSection {
  title: string;
  subtitle: string;
  subsection: string;
  source: string[];
  data_count: number;
  is_deleted?: boolean;
  data: Array<Record<string, any>>;
}

export type NormalizeOptions = {
  case?: "lower" | "upper" | "preserve";
  allowSpecialChars?: RegExp;
};

export interface Dictionary {
  [key: string]: string;
}

export interface ReportMetadata {
  [REPORT_CONSTANTS.new_report.report_id]: string;
  [REPORT_CONSTANTS.new_report.report_status]: string;
  [REPORT_CONSTANTS.new_report.report_type]: string;
  [REPORT_CONSTANTS.new_report.report_search_args]: object;
  [REPORT_CONSTANTS.new_report.report_name]: string;
  [REPORT_CONSTANTS.new_report.creation_at]: string;
  [REPORT_CONSTANTS.new_report.modified_at]: string;
  /* podem vir sem valor */
  [REPORT_CONSTANTS.new_report.subject_name]: string;
  [REPORT_CONSTANTS.new_report.subject_mother_name]: string;
  [REPORT_CONSTANTS.new_report.subject_age]: number | null;
  [REPORT_CONSTANTS.new_report.subject_sex]: string;
}